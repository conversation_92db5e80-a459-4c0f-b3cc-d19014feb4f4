class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  // Mock exercises data
  static final List<Map<String, dynamic>> _mockExercises = [
    {
      'id': 1,
      'name': 'Push-ups',
      'slug': 'push-ups',
      'description':
          'Classic bodyweight exercise for chest, shoulders, and triceps',
      'muscle_groups': ['Chest', 'Shoulders', 'Triceps'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Beginner',
      'exercise_type': 'strength',
      'category': 'upper_body',
      'instructions': [
        'Start in a plank position with hands slightly wider than shoulders',
        'Lower your body until chest nearly touches the floor',
        'Push back up to starting position',
        'Keep your body in a straight line throughout',
      ],
      'tips': [
        'Keep your core engaged',
        'Don\'t let your hips sag',
        'Control the movement - don\'t rush',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 8.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 30))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 2,
      'name': 'Squats',
      'slug': 'squats',
      'description':
          'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
      'muscle_groups': ['Legs', 'Glutes'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Beginner',
      'exercise_type': 'strength',
      'category': 'lower_body',
      'instructions': [
        'Stand with feet shoulder-width apart',
        'Lower your body as if sitting back into a chair',
        'Keep your chest up and knees behind toes',
        'Push through heels to return to standing',
      ],
      'tips': [
        'Keep your weight on your heels',
        'Don\'t let knees cave inward',
        'Go as low as your mobility allows',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 6.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 29))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 3,
      'name': 'Plank',
      'slug': 'plank',
      'description': 'Isometric core exercise that strengthens the entire core',
      'muscle_groups': ['Core', 'Shoulders'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Beginner',
      'exercise_type': 'strength',
      'category': 'core',
      'instructions': [
        'Start in a push-up position',
        'Lower onto your forearms',
        'Keep your body in a straight line',
        'Hold the position',
      ],
      'tips': [
        'Don\'t let your hips sag or pike up',
        'Breathe normally',
        'Start with shorter holds and build up',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 4.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 28))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 4,
      'name': 'Jumping Jacks',
      'slug': 'jumping-jacks',
      'description': 'Full-body cardio exercise that gets your heart rate up',
      'muscle_groups': ['Cardio', 'Legs', 'Shoulders'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Beginner',
      'exercise_type': 'cardio',
      'category': 'full_body',
      'instructions': [
        'Stand with feet together, arms at sides',
        'Jump while spreading legs shoulder-width apart',
        'Simultaneously raise arms overhead',
        'Jump back to starting position',
      ],
      'tips': [
        'Land softly on the balls of your feet',
        'Keep a steady rhythm',
        'Modify by stepping side to side if needed',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 10.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 27))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 5,
      'name': 'Lunges',
      'slug': 'lunges',
      'description':
          'Unilateral leg exercise that improves balance and strength',
      'muscle_groups': ['Legs', 'Glutes'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Intermediate',
      'exercise_type': 'strength',
      'category': 'lower_body',
      'instructions': [
        'Stand with feet hip-width apart',
        'Step forward with one leg',
        'Lower your hips until both knees are at 90 degrees',
        'Push back to starting position',
      ],
      'tips': [
        'Keep your front knee over your ankle',
        'Don\'t let your front knee go past your toes',
        'Keep your torso upright',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 7.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 26))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 6,
      'name': 'Mountain Climbers',
      'slug': 'mountain-climbers',
      'description': 'Dynamic exercise combining cardio and core strengthening',
      'muscle_groups': ['Core', 'Cardio', 'Shoulders'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Intermediate',
      'exercise_type': 'cardio',
      'category': 'full_body',
      'instructions': [
        'Start in a plank position',
        'Bring one knee toward your chest',
        'Quickly switch legs',
        'Continue alternating at a fast pace',
      ],
      'tips': [
        'Keep your hips level',
        'Maintain plank position throughout',
        'Land lightly on your feet',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 12.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 25))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 7,
      'name': 'Burpees',
      'slug': 'burpees',
      'description': 'Full-body exercise combining strength and cardio',
      'muscle_groups': ['Full Body', 'Cardio'],
      'equipment': ['Bodyweight'],
      'difficulty': 'Advanced',
      'exercise_type': 'cardio',
      'category': 'full_body',
      'instructions': [
        'Start standing, then squat down',
        'Place hands on floor and jump feet back to plank',
        'Do a push-up (optional)',
        'Jump feet back to squat, then jump up with arms overhead',
      ],
      'tips': [
        'Move at your own pace',
        'Modify by stepping instead of jumping',
        'Focus on form over speed',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 15.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 24))
          .toIso8601String(),
      'updated_at': null,
    },
    {
      'id': 8,
      'name': 'Dumbbell Bicep Curls',
      'slug': 'dumbbell-bicep-curls',
      'description': 'Isolation exercise targeting the biceps',
      'muscle_groups': ['Biceps'],
      'equipment': ['Dumbbell'],
      'difficulty': 'Beginner',
      'exercise_type': 'strength',
      'category': 'upper_body',
      'instructions': [
        'Stand with dumbbells at your sides',
        'Keep elbows close to your body',
        'Curl weights up toward shoulders',
        'Lower with control',
      ],
      'tips': [
        'Don\'t swing the weights',
        'Keep your wrists straight',
        'Focus on the muscle contraction',
      ],
      'video_url': null,
      'image_url': null,
      'duration_minutes': 1,
      'calories_per_minute': 5.0,
      'is_active': true,
      'created_by': null,
      'created_at': DateTime.now()
          .subtract(const Duration(days: 23))
          .toIso8601String(),
      'updated_at': null,
    },
  ];

  // Mock muscle groups
  static const List<Map<String, dynamic>> _mockMuscleGroups = [
    {'id': 1, 'name': 'Chest', 'description': 'Pectoral muscles'},
    {
      'id': 2,
      'name': 'Back',
      'description': 'Latissimus dorsi, rhomboids, traps',
    },
    {'id': 3, 'name': 'Shoulders', 'description': 'Deltoids'},
    {'id': 4, 'name': 'Biceps', 'description': 'Biceps brachii'},
    {'id': 5, 'name': 'Triceps', 'description': 'Triceps brachii'},
    {'id': 6, 'name': 'Legs', 'description': 'Quadriceps, hamstrings, calves'},
    {'id': 7, 'name': 'Glutes', 'description': 'Gluteal muscles'},
    {'id': 8, 'name': 'Core', 'description': 'Abdominals, obliques'},
    {'id': 9, 'name': 'Cardio', 'description': 'Cardiovascular exercises'},
    {'id': 10, 'name': 'Full Body', 'description': 'Multiple muscle groups'},
  ];

  // Mock equipment
  static const List<Map<String, dynamic>> _mockEquipment = [
    {'id': 1, 'name': 'Bodyweight', 'description': 'No equipment needed'},
    {'id': 2, 'name': 'Dumbbell', 'description': 'Free weights'},
    {'id': 3, 'name': 'Barbell', 'description': 'Long bar with weights'},
    {'id': 4, 'name': 'Kettlebell', 'description': 'Cast iron weight'},
    {'id': 5, 'name': 'Resistance Band', 'description': 'Elastic bands'},
    {'id': 6, 'name': 'Cable', 'description': 'Cable machine'},
    {'id': 7, 'name': 'Machine', 'description': 'Weight machines'},
  ];

  /// Get mock exercises with filtering
  Future<Map<String, dynamic>> getExercises({
    String? search,
    List<String>? muscleGroups,
    List<String>? equipment,
    List<String>? difficulty,
    List<String>? category,
    bool? isFavorite,
    int limit = 20,
    int offset = 0,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    List<Map<String, dynamic>> filteredExercises = List.from(_mockExercises);

    // Apply filters
    if (search != null && search.isNotEmpty) {
      filteredExercises = filteredExercises.where((exercise) {
        return exercise['name'].toString().toLowerCase().contains(
              search.toLowerCase(),
            ) ||
            exercise['description'].toString().toLowerCase().contains(
              search.toLowerCase(),
            );
      }).toList();
    }

    if (muscleGroups != null && muscleGroups.isNotEmpty) {
      filteredExercises = filteredExercises.where((exercise) {
        final exerciseMuscleGroups = List<String>.from(
          exercise['muscle_groups'],
        );
        return muscleGroups.any(
          (group) => exerciseMuscleGroups.any(
            (exerciseGroup) =>
                exerciseGroup.toLowerCase().contains(group.toLowerCase()),
          ),
        );
      }).toList();
    }

    if (equipment != null && equipment.isNotEmpty) {
      filteredExercises = filteredExercises.where((exercise) {
        final exerciseEquipment = List<String>.from(exercise['equipment']);
        return equipment.any(
          (equip) => exerciseEquipment.any(
            (exerciseEquip) =>
                exerciseEquip.toLowerCase().contains(equip.toLowerCase()),
          ),
        );
      }).toList();
    }

    if (difficulty != null && difficulty.isNotEmpty) {
      filteredExercises = filteredExercises.where((exercise) {
        return difficulty.contains(exercise['difficulty']);
      }).toList();
    }

    if (category != null && category.isNotEmpty) {
      filteredExercises = filteredExercises.where((exercise) {
        return category.contains(exercise['category']);
      }).toList();
    }

    if (isFavorite != null && isFavorite) {
      filteredExercises = filteredExercises.where((exercise) {
        return exercise['is_favorite'] == true;
      }).toList();
    }

    // Apply pagination
    final total = filteredExercises.length;
    final startIndex = offset;
    final endIndex = (startIndex + limit).clamp(0, total);

    final paginatedExercises = filteredExercises.sublist(
      startIndex.clamp(0, total),
      endIndex,
    );

    return {
      'exercises': paginatedExercises,
      'total': total,
      'has_more': endIndex < total,
      'offset': offset,
      'limit': limit,
    };
  }

  /// Get specific exercise by ID
  Future<Map<String, dynamic>?> getExercise(int exerciseId) async {
    await Future.delayed(const Duration(milliseconds: 300));

    try {
      return _mockExercises.firstWhere(
        (exercise) => exercise['id'] == exerciseId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get muscle groups
  Future<List<dynamic>> getMuscleGroups() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockMuscleGroups;
  }

  /// Get equipment types
  Future<List<dynamic>> getEquipment() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockEquipment;
  }

  /// Get exercise statistics
  Future<Map<String, dynamic>> getExerciseStats() async {
    await Future.delayed(const Duration(milliseconds: 300));

    return {
      'total_exercises': _mockExercises.length,
      'by_difficulty': {
        'Beginner': _mockExercises
            .where((e) => e['difficulty'] == 'Beginner')
            .length,
        'Intermediate': _mockExercises
            .where((e) => e['difficulty'] == 'Intermediate')
            .length,
        'Advanced': _mockExercises
            .where((e) => e['difficulty'] == 'Advanced')
            .length,
      },
      'by_category': {
        'Strength': _mockExercises
            .where((e) => e['category'] == 'Strength')
            .length,
        'Cardio': _mockExercises.where((e) => e['category'] == 'Cardio').length,
        'Core': _mockExercises.where((e) => e['category'] == 'Core').length,
      },
      'by_equipment': {
        'Bodyweight': _mockExercises
            .where((e) => (e['equipment'] as List).contains('Bodyweight'))
            .length,
        'Dumbbell': _mockExercises
            .where((e) => (e['equipment'] as List).contains('Dumbbell'))
            .length,
      },
    };
  }
}
