import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/api_service.dart';
import '../models/analytics_model.dart';

// API Service provider
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());

// Analytics overview provider
final analyticsOverviewProvider = FutureProvider<AnalyticsOverview>((
  ref,
) async {
  final apiService = ref.watch(apiServiceProvider);
  try {
    final response = await apiService.getAnalyticsOverview();
    return AnalyticsOverview.fromJson(response);
  } catch (e) {
    throw Exception('Failed to load analytics overview: $e');
  }
});

// Workout analytics provider
final workoutAnalyticsProvider =
    FutureProvider.family<WorkoutAnalytics, String>((ref, period) async {
      final apiService = ref.watch(apiServiceProvider);
      try {
        final response = await apiService.getWorkoutAnalytics(period: period);
        return WorkoutAnalytics.fromJson(response);
      } catch (e) {
        throw Exception('Failed to load workout analytics: $e');
      }
    });

// Progress analytics provider
final progressAnalyticsProvider =
    FutureProvider.family<ProgressAnalytics, ProgressParams>((
      ref,
      params,
    ) async {
      final apiService = ref.watch(apiServiceProvider);
      try {
        final response = await apiService.getProgressAnalytics(
          metric: params.metric,
          period: params.period,
        );
        return ProgressAnalytics.fromJson(response);
      } catch (e) {
        throw Exception('Failed to load progress analytics: $e');
      }
    });

// Goal analytics provider
final goalAnalyticsProvider = FutureProvider<GoalAnalytics>((ref) async {
  final apiService = ref.watch(apiServiceProvider);
  try {
    final response = await apiService.getGoalAnalytics();
    return GoalAnalytics.fromJson(response);
  } catch (e) {
    throw Exception('Failed to load goal analytics: $e');
  }
});

// Social analytics provider
final socialAnalyticsProvider = FutureProvider<SocialAnalytics>((ref) async {
  final apiService = ref.watch(apiServiceProvider);
  try {
    final response = await apiService.getSocialAnalytics();
    return SocialAnalytics.fromJson(response);
  } catch (e) {
    throw Exception('Failed to load social analytics: $e');
  }
});

// User stats provider
final userStatsProvider = FutureProvider<UserStats>((ref) async {
  final apiService = ref.watch(apiServiceProvider);
  try {
    final response = await apiService.getUserStats();
    return UserStats.fromJson(response);
  } catch (e) {
    throw Exception('Failed to load user stats: $e');
  }
});

// User analytics provider
final userAnalyticsProvider = FutureProvider<UserAnalytics>((ref) async {
  final apiService = ref.watch(apiServiceProvider);
  try {
    final response = await apiService.getUserAnalytics();
    return UserAnalytics.fromJson(response);
  } catch (e) {
    throw Exception('Failed to load user analytics: $e');
  }
});

// Selected period provider for analytics
final selectedAnalyticsPeriodProvider = StateProvider<String>((ref) => 'month');

// Selected metric provider for progress analytics
final selectedProgressMetricProvider = StateProvider<String>((ref) => 'weight');

// Progress parameters class
class ProgressParams {
  final String metric;
  final String period;

  const ProgressParams({required this.metric, required this.period});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProgressParams &&
          runtimeType == other.runtimeType &&
          metric == other.metric &&
          period == other.period;

  @override
  int get hashCode => metric.hashCode ^ period.hashCode;
}

// Combined progress analytics provider that watches both metric and period
final combinedProgressAnalyticsProvider = FutureProvider<ProgressAnalytics>((
  ref,
) async {
  final metric = ref.watch(selectedProgressMetricProvider);
  final period = ref.watch(selectedAnalyticsPeriodProvider);

  final params = ProgressParams(metric: metric, period: period);
  return ref.watch(progressAnalyticsProvider(params).future);
});

// Analytics state management
class AnalyticsState {
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const AnalyticsState({this.isLoading = false, this.error, this.lastUpdated});

  AnalyticsState copyWith({
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return AnalyticsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

class AnalyticsNotifier extends StateNotifier<AnalyticsState> {
  final ApiService _apiService;

  AnalyticsNotifier(this._apiService) : super(const AnalyticsState());

  // Refresh all analytics data
  Future<void> refreshAnalytics() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // This will trigger a refresh of all analytics providers
      state = state.copyWith(isLoading: false, lastUpdated: DateTime.now());
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  // Test API connection
  Future<bool> testConnection() async {
    try {
      return await _apiService.testConnection();
    } catch (e) {
      return false;
    }
  }
}

// Analytics notifier provider
final analyticsNotifierProvider =
    StateNotifierProvider<AnalyticsNotifier, AnalyticsState>((ref) {
      final apiService = ref.watch(apiServiceProvider);
      return AnalyticsNotifier(apiService);
    });

// API connection status provider
final apiConnectionProvider = FutureProvider<bool>((ref) async {
  final apiService = ref.watch(apiServiceProvider);
  return await apiService.testConnection();
});

// Refresh analytics provider
final refreshAnalyticsProvider = Provider<Future<void> Function()>((ref) {
  return () async {
    // Invalidate all analytics providers to force refresh
    ref.invalidate(analyticsOverviewProvider);
    ref.invalidate(workoutAnalyticsProvider);
    ref.invalidate(progressAnalyticsProvider);
    ref.invalidate(goalAnalyticsProvider);
    ref.invalidate(socialAnalyticsProvider);
    ref.invalidate(userStatsProvider);
    ref.invalidate(userAnalyticsProvider);
  };
});
