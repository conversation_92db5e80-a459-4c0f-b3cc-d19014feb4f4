import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';

class CommunityService {
  static final String _baseUrl = ApiConfig.baseUrl;

  // Get community posts for feed
  Future<List<Map<String, dynamic>>> getCommunityPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    print('🔍 CommunityService.getCommunityPosts() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for community posts');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching community posts from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/community/posts?limit=$limit&offset=$offset'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Community posts API response status: ${response.statusCode}');
      print('🔍 Community posts API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final posts = data['posts'] as List? ?? [];
        print('✅ Community posts fetched successfully: ${posts.length} posts');
        return posts.cast<Map<String, dynamic>>();
      } else {
        print('❌ Failed to fetch community posts: ${response.statusCode}');
        throw Exception(
          'Failed to fetch community posts: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('❌ Error fetching community posts: $e');
      return [];
    }
  }

  // Get active challenges
  Future<List<Map<String, dynamic>>> getActiveChallenges({
    int limit = 10,
  }) async {
    print('🔍 CommunityService.getActiveChallenges() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for challenges');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching active challenges from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/community/challenges?limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Challenges API response status: ${response.statusCode}');
      print('🔍 Challenges API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final challenges = data['challenges'] as List? ?? [];
        print(
          '✅ Challenges fetched successfully: ${challenges.length} challenges',
        );
        return challenges.cast<Map<String, dynamic>>();
      } else {
        print('❌ Failed to fetch challenges: ${response.statusCode}');
        throw Exception('Failed to fetch challenges: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching challenges: $e');
      return [];
    }
  }

  // Get leaderboard
  Future<List<Map<String, dynamic>>> getLeaderboard({int limit = 10}) async {
    print('🔍 CommunityService.getLeaderboard() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for leaderboard');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching leaderboard from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/community/leaderboard?limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Leaderboard API response status: ${response.statusCode}');
      print('🔍 Leaderboard API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final leaderboard = data['leaderboard'] as List? ?? [];
        print(
          '✅ Leaderboard fetched successfully: ${leaderboard.length} users',
        );
        return leaderboard.cast<Map<String, dynamic>>();
      } else {
        print('❌ Failed to fetch leaderboard: ${response.statusCode}');
        throw Exception('Failed to fetch leaderboard: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching leaderboard: $e');
      return [];
    }
  }

  // Get user friends and suggestions
  Future<Map<String, dynamic>> getUserFriends() async {
    print('🔍 CommunityService.getUserFriends() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for friends');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching user friends from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/community/friends'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Friends API response status: ${response.statusCode}');
      print('🔍 Friends API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('✅ Friends data fetched successfully');
        return {
          'friends': data['friends'] as List? ?? [],
          'suggestions': data['suggestions'] as List? ?? [],
        };
      } else {
        print('❌ Failed to fetch friends: ${response.statusCode}');
        throw Exception('Failed to fetch friends: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching friends: $e');
      return {'friends': [], 'suggestions': []};
    }
  }

  // Like a post
  Future<bool> likePost(String postId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/api/community/posts/$postId/like'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('❌ Error liking post: $e');
      return false;
    }
  }

  // Join a challenge
  Future<bool> joinChallenge(String challengeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/api/community/challenges/$challengeId/join'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('❌ Error joining challenge: $e');
      return false;
    }
  }

  // Create a new post
  Future<bool> createPost({
    required String content,
    String? postType,
    List<String>? mediaUrls,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/api/community/posts'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'content': content,
          'post_type': postType ?? 'general',
          'media_urls': mediaUrls,
        }),
      );

      return response.statusCode == 201;
    } catch (e) {
      print('❌ Error creating post: $e');
      return false;
    }
  }
}
