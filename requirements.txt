# ============================================================================
# WibeFit App - Complete Requirements File
# ============================================================================
# This file lists all software, tools, and dependencies required to run
# the WibeFit fitness tracking application.

# ============================================================================
# SYSTEM REQUIREMENTS
# ============================================================================

# Operating Systems (Any of the following):
# - macOS 10.14+ (for iOS development)
# - Windows 10+ (for Android/Windows development)
# - Linux Ubuntu 18.04+ (for Android/Linux development)

# Hardware Requirements:
# - RAM: 8GB minimum, 16GB recommended
# - Storage: 10GB free space minimum
# - CPU: Intel i5 or equivalent AMD processor

# ============================================================================
# DEVELOPMENT TOOLS
# ============================================================================

# Flutter SDK
# Version: 3.0.0 or higher
# Download: https://docs.flutter.dev/get-started/install
# Installation: Follow platform-specific Flutter installation guide

# Dart SDK
# Version: 2.17.0 or higher (included with Flutter)
# Note: Automatically installed with Flutter SDK

# Android Studio
# Version: 2021.1.1 or higher
# Download: https://developer.android.com/studio
# Required for: Android development and emulator

# Xcode (macOS only)
# Version: 13.0 or higher
# Download: Mac App Store
# Required for: iOS development and simulator

# Visual Studio Code (Optional but recommended)
# Version: 1.60.0 or higher
# Download: https://code.visualstudio.com/
# Extensions:
#   - Flutter
#   - Dart
#   - Python

# ============================================================================
# MOBILE DEVELOPMENT REQUIREMENTS
# ============================================================================

# Android SDK
# API Level: 21 (Android 5.0) minimum, 33+ recommended
# Components required:
#   - Android SDK Platform-Tools
#   - Android SDK Build-Tools 30.0.3+
#   - Android Emulator
#   - Intel x86 Emulator Accelerator (HAXM installer)

# iOS Development (macOS only)
# Xcode Command Line Tools
# iOS Simulator
# CocoaPods (for iOS dependencies)
# Installation: sudo gem install cocoapods

# ============================================================================
# BACKEND REQUIREMENTS
# ============================================================================

# Python
# Version: 3.8.0 or higher
# Download: https://python.org/downloads/
# Note: Python 3.9+ recommended for best performance

# Python Virtual Environment (recommended)
# Creation: python -m venv venv
# Activation: 
#   - Windows: venv\Scripts\activate
#   - macOS/Linux: source venv/bin/activate

# ============================================================================
# DATABASE REQUIREMENTS
# ============================================================================

# PostgreSQL (Optional - for production database)
# Version: 13.0 or higher
# Download: https://postgresql.org/download/
# Default credentials can be configured in backend server

# SQLite (Included with Python)
# Version: 3.31.0 or higher
# Note: Used for development and offline storage

# ============================================================================
# FLUTTER DEPENDENCIES
# ============================================================================
# The following are automatically installed via 'flutter pub get'
# Listed here for reference and version tracking

# Core Dependencies:
# flutter: sdk
# cupertino_icons: ^1.0.2

# State Management:
# flutter_riverpod: ^2.4.9
# riverpod_annotation: ^2.3.3

# UI & Navigation:
# go_router: ^12.1.3
# flutter_animate: ^4.3.0

# Authentication:
# firebase_core: ^2.24.2
# firebase_auth: ^4.15.3
# google_sign_in: ^6.1.6

# Data & Storage:
# shared_preferences: ^2.2.2
# sqflite: ^2.3.0
# path_provider: ^2.1.1

# HTTP & API:
# http: ^1.1.2
# dio: ^5.4.0

# Media & Files:
# image_picker: ^1.0.4
# video_player: ^2.8.1

# Utilities:
# intl: ^0.18.1
# uuid: ^4.2.1

# Development Dependencies:
# flutter_test: sdk
# flutter_lints: ^3.0.0
# build_runner: ^2.4.7
# riverpod_generator: ^2.3.9

# ============================================================================
# PYTHON BACKEND DEPENDENCIES
# ============================================================================
# Install via: pip install -r backend/requirements.txt

# Core Framework:
# fastapi==0.104.1
# uvicorn==0.24.0

# Database:
# psycopg2-binary==2.9.10  # PostgreSQL adapter
# sqlalchemy==2.0.23      # ORM

# Authentication:
# python-jose==3.3.0      # JWT tokens
# passlib==1.7.4          # Password hashing
# python-multipart==0.0.6 # Form data

# Utilities:
# python-dotenv==1.0.0    # Environment variables
# pydantic==2.5.0         # Data validation

# ============================================================================
# DEVELOPMENT SETUP COMMANDS
# ============================================================================

# 1. Clone Repository:
# git clone <repository-url>
# cd wibefit_app

# 2. Flutter Setup:
# cd frontend
# flutter doctor          # Check Flutter installation
# flutter pub get         # Install dependencies
# flutter devices         # List available devices

# 3. Backend Setup:
# cd backend
# python -m venv venv                    # Create virtual environment
# source venv/bin/activate               # Activate (macOS/Linux)
# venv\Scripts\activate                  # Activate (Windows)
# pip install -r requirements.txt       # Install dependencies

# 4. Database Setup (Optional):
# createdb wibefit_db                    # Create PostgreSQL database
# # Update credentials in wibefit_postgresql_server.py

# 5. Run Application:
# # Terminal 1 - Backend:
# cd backend
# python wibefit_postgresql_server.py   # PostgreSQL server
# # OR
# python wibefit_v1_server.py          # SQLite server

# # Terminal 2 - Frontend:
# cd frontend
# flutter run                           # Run on connected device

# ============================================================================
# PLATFORM-SPECIFIC SETUP
# ============================================================================

# Android Setup:
# 1. Install Android Studio
# 2. Install Android SDK (API 21+)
# 3. Create Android Virtual Device (AVD)
# 4. Enable USB Debugging on physical device (optional)
# 5. Accept Android licenses: flutter doctor --android-licenses

# iOS Setup (macOS only):
# 1. Install Xcode from Mac App Store
# 2. Install Xcode Command Line Tools: xcode-select --install
# 3. Install CocoaPods: sudo gem install cocoapods
# 4. Open iOS Simulator: open -a Simulator
# 5. For physical device: Enable Developer Mode in iOS Settings

# Web Setup (Optional):
# 1. Enable web support: flutter config --enable-web
# 2. Run on web: flutter run -d chrome

# ============================================================================
# VERIFICATION COMMANDS
# ============================================================================

# Check Flutter Installation:
# flutter doctor -v

# Check Connected Devices:
# flutter devices

# Check Python Installation:
# python --version
# pip --version

# Check PostgreSQL Installation:
# psql --version

# Test Backend Server:
# curl http://localhost:8000/health

# ============================================================================
# TROUBLESHOOTING
# ============================================================================

# Common Issues and Solutions:

# 1. Flutter Doctor Issues:
# - Android toolchain: Run 'flutter doctor --android-licenses'
# - Xcode issues: Update Xcode and command line tools
# - VS Code: Install Flutter and Dart extensions

# 2. Build Issues:
# - Clean build: flutter clean && flutter pub get
# - Reset dependencies: rm pubspec.lock && flutter pub get
# - Clear cache: flutter pub cache repair

# 3. Backend Issues:
# - Port conflicts: Change port in server files
# - Database connection: Check PostgreSQL service status
# - Python modules: Reinstall with pip install -r requirements.txt

# 4. Performance Issues:
# - Enable release mode: flutter run --release
# - Close other applications to free RAM
# - Use physical device instead of emulator

# ============================================================================
# OPTIONAL TOOLS
# ============================================================================

# Git (Version Control):
# Version: 2.30.0 or higher
# Download: https://git-scm.com/downloads

# Postman (API Testing):
# Download: https://postman.com/downloads/

# pgAdmin (PostgreSQL GUI):
# Download: https://pgadmin.org/download/

# Firebase CLI (for Firebase features):
# Installation: npm install -g firebase-tools

# ============================================================================
# MINIMUM SYSTEM SPECIFICATIONS
# ============================================================================

# Development Machine:
# - CPU: Intel i5 / AMD Ryzen 5 or equivalent
# - RAM: 8GB (16GB recommended)
# - Storage: 50GB free space
# - Internet: Stable connection for downloads

# Target Devices:
# - Android: API 21+ (Android 5.0+)
# - iOS: iOS 11.0+
# - RAM: 2GB minimum
# - Storage: 100MB for app installation

# ============================================================================
# NOTES
# ============================================================================

# 1. This requirements file covers the complete development environment
# 2. For production deployment, additional considerations may apply
# 3. Version numbers are minimum requirements - newer versions usually work
# 4. Some dependencies are platform-specific (iOS requires macOS)
# 5. Internet connection required for initial setup and dependency downloads
# 6. Regular updates recommended for security and performance improvements

# Last Updated: July 2025
# Project: WibeFit v1.0
# Maintainer: WibeFit Development Team
