import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';

class UserService {
  static String get _baseUrl => ApiConfig.baseUrl;

  // Get current user profile
  Future<Map<String, dynamic>?> getCurrentUser() async {
    print('🔍 UserService.getCurrentUser() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      print('🔍 Token from storage: ${token?.substring(0, 20)}...');

      if (token == null) {
        print('❌ No authentication token found');
        throw Exception('No authentication token found');
      }

      print('🔍 Making API call to: $_baseUrl/api/auth/me');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/me'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 API response status: ${response.statusCode}');
      print('🔍 API response body: ${response.body}');

      if (response.statusCode == 200) {
        final userData = json.decode(response.body);
        print('✅ User data fetched successfully: ${userData['full_name']}');
        return userData;
      } else {
        print('❌ API call failed with status: ${response.statusCode}');
        throw Exception('Failed to fetch user profile: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching user profile: $e');
      return null;
    }
  }

  // Get user's favorite exercises count
  Future<int> getFavoriteExercisesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        return 0;
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/api/workouts/exercises/favorites?limit=1'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['total'] ?? 0;
      } else {
        return 0;
      }
    } catch (e) {
      print('Error fetching favorite exercises count: $e');
      return 0;
    }
  }

  // Get user's recent exercises count
  Future<int> getRecentExercisesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        return 0;
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/api/workouts/exercises/recent?limit=1'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['total'] ?? 0;
      } else {
        return 0;
      }
    } catch (e) {
      print('Error fetching recent exercises count: $e');
      return 0;
    }
  }

  // Get user profile data from database
  Future<Map<String, dynamic>?> getUserProfileData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['profile'];
      } else if (response.statusCode == 404) {
        // Profile doesn't exist yet
        return null;
      } else {
        throw Exception('Failed to fetch user profile: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching user profile data: $e');
      return null;
    }
  }

  // Get user's workout statistics
  Future<Map<String, dynamic>> getUserStats() async {
    print('🔍 UserService.getUserStats() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for stats');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching user stats from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/users/me/stats'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Stats API response status: ${response.statusCode}');
      print('🔍 Stats API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('✅ User stats fetched successfully');

        // Convert API response to expected format
        return {
          'totalWorkouts': data['total_workouts'] ?? 0,
          'totalDays': data['current_streak_days'] ?? 0,
          'currentStreak': data['current_streak_days'] ?? 0,
          'longestStreak': data['longest_streak_days'] ?? 0,
          'favoriteExercises': await getFavoriteExercisesCount(),
          'recentExercises': await getRecentExercisesCount(),
          'totalCalories': data['total_calories_burned'] ?? 0,
          'totalWorkoutTime': data['total_workout_time_minutes'] ?? 0,
          'averageWorkoutDuration': data['average_workout_duration'] ?? 0,
          'achievementsCount': data['achievements_count'] ?? 0,
          'friendsCount': data['friends_count'] ?? 0,
        };
      } else {
        print('❌ Failed to fetch user stats: ${response.statusCode}');
        throw Exception('Failed to fetch user stats: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Error fetching user stats: $e');
      return {
        'totalWorkouts': 0,
        'totalDays': 0,
        'currentStreak': 0,
        'longestStreak': 0,
        'favoriteExercises': 0,
        'recentExercises': 0,
        'totalCalories': 0,
        'totalWorkoutTime': 0,
        'averageWorkoutDuration': 0,
        'achievementsCount': 0,
        'friendsCount': 0,
      };
    }
  }

  // Update user profile
  Future<bool> updateUserProfile(Map<String, dynamic> userData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/api/auth/me'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(userData),
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error updating user profile: $e');
      return false;
    }
  }

  // Get user achievements from database
  Future<List<Map<String, dynamic>>> getUserAchievements() async {
    print('🔍 UserService.getUserAchievements() called');
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print('❌ No authentication token found for achievements');
        throw Exception('No authentication token found');
      }

      print('🔍 Fetching user achievements from API');
      final response = await http.get(
        Uri.parse('$_baseUrl/api/users/me/achievements'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Achievements API response status: ${response.statusCode}');
      print('🔍 Achievements API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        print('✅ User achievements fetched successfully');

        // Convert API response to expected format
        final achievements = data['achievements'] as List? ?? [];
        return achievements
            .map<Map<String, dynamic>>(
              (achievement) => {
                'title': achievement['title'] ?? 'Unknown Achievement',
                'icon': achievement['icon'] ?? 'star',
                'earned': true, // All returned achievements are earned
                'description': achievement['description'] ?? '',
                'points': achievement['points'] ?? 0,
                'earned_at': achievement['earned_at'],
              },
            )
            .toList();
      } else if (response.statusCode == 404) {
        // No achievements yet - return empty list
        print('📝 No achievements found for user');
        return [];
      } else {
        print('❌ Failed to fetch user achievements: ${response.statusCode}');
        throw Exception(
          'Failed to fetch user achievements: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('❌ Error fetching user achievements: $e');
      // Return empty list on error
      return [];
    }
  }
}
