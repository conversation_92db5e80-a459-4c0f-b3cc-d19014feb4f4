import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/workout_timer_service.dart';
import '../../../shared/models/workout_session_model.dart';
import '../providers/workout_providers.dart';
import '../models/workout_models.dart';
import '../../exercise/models/exercise_model.dart';
import '../../auth/providers/auth_provider.dart';

class WorkoutExecutionScreen extends StatelessWidget {
  final List<Exercise>? exercises;
  final String? workoutName;
  final CustomWorkout? customWorkout;

  const WorkoutExecutionScreen({
    super.key,
    this.exercises,
    this.workoutName,
    this.customWorkout,
  });

  @override
  Widget build(BuildContext context) {
    return _OptimizedWorkoutExecutionContent(
      exercises: exercises,
      workoutName: workoutName,
      customWorkout: customWorkout,
    );
  }
}

class _OptimizedWorkoutExecutionContent extends ConsumerStatefulWidget {
  final List<Exercise>? exercises;
  final String? workoutName;
  final CustomWorkout? customWorkout;

  const _OptimizedWorkoutExecutionContent({
    this.exercises,
    this.workoutName,
    this.customWorkout,
  });

  @override
  ConsumerState<_OptimizedWorkoutExecutionContent> createState() =>
      _OptimizedWorkoutExecutionContentState();
}

class _OptimizedWorkoutExecutionContentState
    extends ConsumerState<_OptimizedWorkoutExecutionContent>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // Prevent rebuilds during workout
  late List<Exercise> _exercises;
  late String _workoutName;
  bool _isWorkoutStarted = false;

  // Timer service integration
  final WorkoutTimerService _timerService = WorkoutTimerService();
  int _elapsedSeconds = 0;
  WorkoutSessionModel? _currentSession;

  // Controllers for user input
  final TextEditingController _repsController = TextEditingController(
    text: '12',
  );
  final TextEditingController _weightController = TextEditingController(
    text: '20.0',
  );

  @override
  void initState() {
    super.initState();
    _exercises = widget.exercises ?? [];
    _workoutName =
        widget.workoutName ?? widget.customWorkout?.name ?? 'Custom Workout';

    // If we have a custom workout, extract exercises
    if (widget.customWorkout != null) {
      _exercises = widget.customWorkout!.exercises
          .map((e) => e.exercise)
          .toList();
    }

    // Setup timer listeners
    _setupTimerListeners();

    // Auto-start workout timer
    _startWorkoutTimer();
  }

  void _setupTimerListeners() {
    _timerService.elapsedTimeStream.listen((seconds) {
      if (mounted) {
        setState(() {
          _elapsedSeconds = seconds;
        });
      }
    });

    _timerService.sessionStream.listen((session) {
      if (mounted) {
        setState(() {
          _currentSession = session;
        });
      }
    });
  }

  Future<void> _startWorkoutTimer() async {
    final user = ref.read(authProvider).user;
    if (user == null) return;

    // Get user's current weight from profile
    final profile = user.metadata;
    final bodyWeight = profile?['current_weight']?.toDouble() ?? 70.0;

    // Convert exercises to session models
    final exerciseModels = _exercises
        .map(
          (exercise) => ExerciseSessionModel(
            id: 'ex_${exercise.id}',
            exerciseId: exercise.id.toString(),
            exerciseName: exercise.name,
            sets: 3,
            reps: 12,
            durationSeconds: 0,
            caloriesBurned: 0,
            completed: false,
          ),
        )
        .toList();

    await _timerService.startWorkout(
      userId: user.id,
      workoutPlanId: 'demo_plan',
      workoutName: _workoutName,
      exercises: exerciseModels,
      bodyWeight: bodyWeight,
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _showWorkoutSummary(BuildContext context, WorkoutSessionModel session) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Workout Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Great job completing "${session.workoutName}"!'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    const Icon(Icons.timer, color: AppTheme.primaryColor),
                    const SizedBox(height: 4),
                    Text('${session.durationMinutes} min'),
                    const Text('Duration', style: TextStyle(fontSize: 12)),
                  ],
                ),
                Column(
                  children: [
                    const Icon(
                      Icons.local_fire_department,
                      color: Colors.orange,
                    ),
                    const SizedBox(height: 4),
                    Text('${session.caloriesBurned}'),
                    const Text('Calories', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.pop();
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final workoutState = ref.watch(workoutSessionProvider);
    final timerState = ref.watch(timerProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => _handleBackPress(context),
        ),
        title: Text(
          _workoutName,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        actions: [
          // Timer display
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _formatTime(_elapsedSeconds),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          if (workoutState.isActive)
            IconButton(
              icon: Icon(
                workoutState.isPaused ? Icons.play_arrow : Icons.pause,
                color: Colors.black,
              ),
              onPressed: () => _toggleWorkout(),
            ),
        ],
      ),
      body: _exercises.isEmpty
          ? _buildEmptyState()
          : !_isWorkoutStarted
          ? _buildWorkoutPreview()
          : _buildWorkoutExecution(workoutState, timerState),
    );
  }

  void _handleBackPress(BuildContext context) {
    final workoutState = ref.read(workoutSessionProvider);

    if (workoutState.isActive) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: AppTheme.cardColor,
          title: const Text(
            'Exit Workout?',
            style: TextStyle(color: Colors.black),
          ),
          content: const Text(
            'Do you want to finish and save your workout, or exit without saving?',
            style: TextStyle(color: Colors.black87),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: TextStyle(color: AppTheme.primaryColor),
              ),
            ),
            TextButton(
              onPressed: () async {
                // Exit without saving
                await _timerService.cancelWorkout();
                ref.read(workoutSessionProvider.notifier).resetWorkout();
                Navigator.pop(context);
                context.pop();
              },
              child: const Text(
                'Exit',
                style: TextStyle(color: AppTheme.errorColor),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                // Finish and save workout
                final session = await _timerService.stopWorkout();
                ref.read(workoutSessionProvider.notifier).resetWorkout();
                Navigator.pop(context);

                if (session != null) {
                  _showWorkoutSummary(context, session);
                } else {
                  context.pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: const Text(
                'Finish Workout',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    } else {
      context.pop();
    }
  }

  void _toggleWorkout() {
    final workoutState = ref.read(workoutSessionProvider);

    if (workoutState.isPaused) {
      ref.read(workoutSessionProvider.notifier).resumeWorkout();
    } else {
      ref.read(workoutSessionProvider.notifier).pauseWorkout();
    }
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.fitness_center, size: 64, color: AppTheme.textSecondary),
          SizedBox(height: 16),
          Text(
            'No Exercises Selected',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Please add exercises to start your workout',
            style: TextStyle(fontSize: 16, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutPreview() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWorkoutSummary(),
          const SizedBox(height: 24),
          _buildExercisesList(),
          const Spacer(),
          _buildStartButton(),
        ],
      ),
    );
  }

  Widget _buildWorkoutSummary() {
    final totalDuration = _exercises.fold<int>(
      0,
      (sum, exercise) => sum + (exercise.durationMinutes ?? 3),
    );

    final estimatedCalories = _exercises.fold<double>(
      0,
      (sum, exercise) =>
          sum +
          ((exercise.caloriesPerMinute ?? 5.0) *
              (exercise.durationMinutes ?? 3)),
    );

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Summary',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: _buildSummaryItem(
                  icon: Icons.timer,
                  label: 'Duration',
                  value: '${totalDuration}min',
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  icon: Icons.local_fire_department,
                  label: 'Calories',
                  value: '${estimatedCalories.round()}',
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  icon: Icons.fitness_center,
                  label: 'Exercises',
                  value: '${_exercises.length}',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Colors.black87, fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildExercisesList() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exercises (${_exercises.length})',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...List.generate(_exercises.length, (index) {
            final exercise = _exercises[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exercise.name,
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${exercise.durationMinutes ?? 3} min • ${exercise.difficulty}',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildStartButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _startWorkout,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          'Start Workout',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _startWorkout() {
    setState(() {
      _isWorkoutStarted = true;
    });

    // Start the workout session
    ref
        .read(workoutSessionProvider.notifier)
        .startWorkout(customWorkoutName: _workoutName);
  }

  Widget _buildWorkoutExecution(
    WorkoutSessionState workoutState,
    TimerState timerState,
  ) {
    if (_exercises.isEmpty) return _buildEmptyState();

    final currentExercise =
        workoutState.currentExerciseIndex < _exercises.length
        ? _exercises[workoutState.currentExerciseIndex]
        : null;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildWorkoutProgress(workoutState),
          const SizedBox(height: 24),
          if (currentExercise != null) ...[
            _buildCurrentExercise(currentExercise, workoutState),
            const SizedBox(height: 24),
            _buildTimer(timerState),
            const SizedBox(height: 24),
            _buildExerciseControls(currentExercise, workoutState),
            const SizedBox(height: 24),
          ],
          _buildWorkoutControls(workoutState),
          const SizedBox(height: 100), // Extra space at bottom
        ],
      ),
    );
  }

  Widget _buildWorkoutProgress(WorkoutSessionState workoutState) {
    final progress = _exercises.isEmpty
        ? 0.0
        : (workoutState.currentExerciseIndex + 1) / _exercises.length;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Exercise ${workoutState.currentExerciseIndex + 1} of ${_exercises.length}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                _formatDuration(workoutState.elapsedTime),
                style: const TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppTheme.textSecondary.withOpacity(0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(
              AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Calories: ${workoutState.totalCaloriesBurned.round()}',
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                ),
              ),
              Text(
                'Set ${workoutState.currentSet}',
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentExercise(
    Exercise exercise,
    WorkoutSessionState workoutState,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            exercise.name,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            exercise.description ?? 'No description available',
            style: const TextStyle(color: Colors.black87, fontSize: 16),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildExerciseInfo(
                icon: Icons.fitness_center,
                label: exercise.difficulty,
              ),
              _buildExerciseInfo(
                icon: Icons.category,
                label: exercise.category.replaceAll('_', ' '),
              ),
              _buildExerciseInfo(
                icon: Icons.timer,
                label: '${exercise.durationMinutes ?? 3}min',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseInfo({required IconData icon, required String label}) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(color: Colors.black87, fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildTimer(TimerState timerState) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Text(
            timerState.type == TimerType.rest ? 'Rest Time' : 'Exercise Time',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: 120,
                height: 120,
                child: CircularProgressIndicator(
                  value: timerState.progress,
                  strokeWidth: 8,
                  backgroundColor: AppTheme.textSecondary.withOpacity(0.3),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    timerState.type == TimerType.rest
                        ? AppTheme.primaryColor
                        : Colors.orange,
                  ),
                ),
              ),
              Text(
                _formatTimerDuration(timerState.remaining),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              IconButton(
                onPressed: () => ref.read(timerProvider.notifier).addTime(-10),
                icon: const Icon(Icons.remove, color: Colors.black),
              ),
              IconButton(
                onPressed: () {
                  if (timerState.isRunning) {
                    ref.read(timerProvider.notifier).pauseTimer();
                  } else {
                    ref.read(timerProvider.notifier).resumeTimer();
                  }
                },
                icon: Icon(
                  timerState.isRunning ? Icons.pause : Icons.play_arrow,
                  color: AppTheme.primaryColor,
                  size: 32,
                ),
              ),
              IconButton(
                onPressed: () => ref.read(timerProvider.notifier).addTime(10),
                icon: const Icon(Icons.add, color: Colors.black),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseControls(
    Exercise exercise,
    WorkoutSessionState workoutState,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Log Your Performance',
            style: TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInputField(
                  label: 'Reps',
                  hint: '12',
                  onChanged: (value) {
                    // Store reps value
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInputField(
                  label: 'Weight (kg)',
                  hint: '20',
                  onChanged: (value) {
                    // Store weight value
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _completeSet(exercise, workoutState),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Complete Set ${workoutState.currentSet}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required String label,
    required String hint,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.black87, fontSize: 12),
        ),
        const SizedBox(height: 4),
        TextField(
          onChanged: onChanged,
          keyboardType: TextInputType.number,
          style: const TextStyle(color: Colors.black),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.black54),
            filled: true,
            fillColor: AppTheme.backgroundColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWorkoutControls(WorkoutSessionState workoutState) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: workoutState.currentExerciseIndex > 0
                ? () => ref
                      .read(workoutSessionProvider.notifier)
                      .previousExercise()
                : null,
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppTheme.primaryColor),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Previous',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: () => _nextExercise(workoutState),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              workoutState.currentExerciseIndex >= _exercises.length - 1
                  ? 'Finish Workout'
                  : 'Next Exercise',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _completeSet(Exercise exercise, WorkoutSessionState workoutState) {
    // Log the exercise performance
    ref
        .read(workoutSessionProvider.notifier)
        .logExercise(
          exercise: exercise,
          reps: 12, // TODO: Get from input field
          weightKg: 20.0, // TODO: Get from input field
          durationSeconds: 60,
        );

    // Move to next set or start rest timer
    ref.read(workoutSessionProvider.notifier).nextSet();

    // Start rest timer
    ref
        .read(timerProvider.notifier)
        .startTimer(
          duration: const Duration(seconds: 60),
          type: TimerType.rest,
        );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Set ${workoutState.currentSet} completed!'),
        backgroundColor: AppTheme.primaryColor,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _nextExercise(WorkoutSessionState workoutState) {
    if (workoutState.currentExerciseIndex >= _exercises.length - 1) {
      // Finish workout
      _finishWorkout();
    } else {
      // Move to next exercise
      ref.read(workoutSessionProvider.notifier).nextExercise();
    }
  }

  void _finishWorkout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Workout Complete!',
          style: TextStyle(color: Colors.black),
        ),
        content: const Text(
          'Great job! How would you rate this workout?',
          style: TextStyle(color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref
                  .read(workoutSessionProvider.notifier)
                  .completeWorkout(
                    difficultyRating: 7,
                    satisfactionRating: 8,
                    notes: 'Great workout!',
                  );
              Navigator.pop(context);
              context.pop();
            },
            child: Text(
              'Complete',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }

  String _formatTimerDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
