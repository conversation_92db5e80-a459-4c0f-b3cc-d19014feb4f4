#!/usr/bin/env python3
"""
Minimal WibeFit Backend Server
Simple server that logs all requests and provides basic API endpoints
"""

import json
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

try:
    import uvicorn
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
except ImportError:
    print("Installing required packages...")
    os.system("pip3 install fastapi uvicorn")
    import uvicorn
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel

# Create FastAPI app
app = FastAPI(title="WibeFit Minimal API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
storage = {
    "users": {},
    "profiles": {},
    "requests": []
}

# Log file
LOG_FILE = "wibefit_requests.log"

def log_request(endpoint: str, method: str, data: Any = None):
    """Log all requests to file and memory"""
    timestamp = datetime.now().isoformat()
    log_entry = {
        "timestamp": timestamp,
        "endpoint": endpoint,
        "method": method,
        "data": data
    }
    
    # Add to memory
    storage["requests"].append(log_entry)
    
    # Write to file
    try:
        with open(LOG_FILE, "a") as f:
            f.write(f"{timestamp} | {method} {endpoint} | {json.dumps(data)}\n")
    except Exception as e:
        print(f"Error writing to log file: {e}")
    
    print(f"📝 LOGGED: {method} {endpoint} - {data}")

# Pydantic models
class UserProfileUpdate(BaseModel):
    age: Optional[int] = None
    height: Optional[float] = None
    current_weight: Optional[float] = None
    fitness_level: Optional[str] = None
    primary_goals: Optional[List[str]] = None
    equipment_access: Optional[List[str]] = None
    preferred_workout_days: Optional[List[str]] = None
    gender: Optional[str] = None

# Health check
@app.get("/")
async def root():
    log_request("/", "GET")
    return {
        "message": "WibeFit Minimal API is running!",
        "timestamp": datetime.now().isoformat(),
        "total_requests": len(storage["requests"])
    }

@app.get("/health")
async def health_check():
    log_request("/health", "GET")
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "total_requests": len(storage["requests"])
    }

# Auth endpoints
@app.post("/api/auth/login")
async def login(request: dict):
    log_request("/api/auth/login", "POST", request)
    return {
        "access_token": f"token_{datetime.now().timestamp()}",
        "token_type": "bearer",
        "user": {
            "id": 1,
            "email": request.get("email", "<EMAIL>"),
            "full_name": "Demo User"
        }
    }

@app.post("/api/auth/register")
async def register(request: dict):
    log_request("/api/auth/register", "POST", request)
    user_id = len(storage["users"]) + 1
    user = {
        "id": user_id,
        "email": request.get("email"),
        "full_name": request.get("full_name"),
        "created_at": datetime.now().isoformat()
    }
    storage["users"][user_id] = user
    
    return {
        "access_token": f"token_{datetime.now().timestamp()}",
        "token_type": "bearer",
        "user": user
    }

@app.get("/api/auth/me")
async def get_current_user():
    log_request("/api/auth/me", "GET")
    return {
        "id": 1,
        "email": "<EMAIL>",
        "full_name": "Demo User",
        "role": "member"
    }

# Profile endpoints
@app.put("/api/auth/profile")
async def update_profile(profile_data: dict):
    log_request("/api/auth/profile", "PUT", profile_data)
    
    # Store profile data
    user_id = profile_data.get("user_id", 1)
    storage["profiles"][user_id] = {
        **profile_data,
        "updated_at": datetime.now().isoformat()
    }
    
    print(f"✅ PROFILE SAVED TO DATABASE: User {user_id}")
    print(f"📊 Profile Data: {json.dumps(profile_data, indent=2)}")
    
    return {
        "message": "Profile updated successfully",
        "profile": storage["profiles"][user_id]
    }

@app.get("/api/auth/profile")
async def get_profile():
    log_request("/api/auth/profile", "GET")
    user_id = 1
    if user_id in storage["profiles"]:
        return {"profile": storage["profiles"][user_id]}
    else:
        raise HTTPException(status_code=404, detail="Profile not found")

# Debug endpoints
@app.get("/api/debug/requests")
async def get_all_requests():
    """Get all logged requests"""
    return {
        "total_requests": len(storage["requests"]),
        "requests": storage["requests"][-50:]  # Last 50 requests
    }

@app.get("/api/debug/profiles")
async def get_all_profiles():
    """Get all stored profiles"""
    return {
        "total_profiles": len(storage["profiles"]),
        "profiles": storage["profiles"]
    }

@app.get("/api/debug/storage")
async def get_storage_info():
    """Get storage statistics"""
    return {
        "users": len(storage["users"]),
        "profiles": len(storage["profiles"]),
        "requests": len(storage["requests"]),
        "log_file": LOG_FILE,
        "log_file_exists": os.path.exists(LOG_FILE)
    }

# Community endpoints (mock)
@app.get("/api/community/posts")
async def get_community_posts():
    log_request("/api/community/posts", "GET")
    return {"posts": []}

@app.post("/api/community/posts")
async def create_community_post(post_data: dict):
    log_request("/api/community/posts", "POST", post_data)
    return {"message": "Post created successfully"}

if __name__ == "__main__":
    print("🚀 Starting WibeFit Minimal Backend Server...")
    print("📍 Server will be available at:")
    print("   - Local: http://localhost:8000")
    print("   - Network: http://***********:8000")
    print("   - Health check: http://***********:8000/health")
    print(f"📝 Requests will be logged to: {LOG_FILE}")
    print("")
    
    # Create log file if it doesn't exist
    if not os.path.exists(LOG_FILE):
        with open(LOG_FILE, "w") as f:
            f.write(f"WibeFit Request Log - Started at {datetime.now().isoformat()}\n")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
