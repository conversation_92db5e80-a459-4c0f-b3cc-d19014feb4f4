import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../exercise/models/exercise_model.dart';
import '../../exercise/providers/exercise_provider.dart';
import 'workout_execution_screen.dart';

class WorkoutBuilderScreen extends ConsumerStatefulWidget {
  const WorkoutBuilderScreen({super.key});

  @override
  ConsumerState<WorkoutBuilderScreen> createState() =>
      _WorkoutBuilderScreenState();
}

class _WorkoutBuilderScreenState extends ConsumerState<WorkoutBuilderScreen> {
  final List<Exercise> _selectedExercises = [];
  String _workoutName = 'Today\'s Workout';
  String _selectedMuscleGroup = 'All';
  final int _targetDuration = 30;
  final String _workoutType = 'Strength';
  final TextEditingController _workoutNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _workoutNameController.text = _workoutName;
    // Load exercises when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(exerciseProvider.notifier).loadExercises();
    });
  }

  @override
  void dispose() {
    _workoutNameController.dispose();
    super.dispose();
  }

  int get _estimatedDuration {
    if (_selectedExercises.isEmpty) return _targetDuration;
    return _selectedExercises.fold(0, (total, exercise) {
      return total + (exercise.durationMinutes ?? 3);
    });
  }

  int get _exerciseCount => _selectedExercises.length;

  @override
  Widget build(BuildContext context) {
    final exerciseState = ref.watch(exerciseProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        title: Text(
          _workoutName,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.black),
            onPressed: () => _editWorkoutName(),
          ),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.black),
            onPressed: () => _shareWorkout(),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDurationSection(),
              const SizedBox(height: 16),
              _buildExerciseControls(),
              const SizedBox(height: 16),
              _buildWorkoutSections(),
              const SizedBox(height: 20),
              if (_selectedExercises.isEmpty)
                Center(child: _buildEmptyState())
              else
                _buildSelectedExercises(),
              const SizedBox(height: 20),
              _buildExerciseSelection(exerciseState),
              const SizedBox(height: 40),
              _buildActionButtons(context),
              const SizedBox(height: 20), // Extra padding at bottom
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDurationSection() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${_estimatedDuration}mins',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: AppTheme.primaryColor,
                ),
              ),
              Text(
                'Estimated Duration',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
        Container(width: 1, height: 40, color: AppTheme.surfaceColor),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${_estimatedDuration}mins',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: AppTheme.primaryColor,
                ),
              ),
              Text(
                'Actual Duration',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseControls() {
    return Column(
      children: [
        Row(
          children: [
            Text(
              '$_exerciseCount exercises',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Spacer(),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // Reorder exercises
                },
                icon: const Icon(Icons.reorder, size: 18),
                label: const Text('Reorder'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // Create superset
                },
                icon: const Icon(Icons.link, size: 18),
                label: const Text('Superset'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWorkoutSections() {
    return Column(
      children: [
        _buildSection('Warm-up', '4 stretches', Icons.accessibility_new),
        const SizedBox(height: 16),
        _buildSection('Cool-down', '4 stretches', Icons.self_improvement),
      ],
    );
  }

  Widget _buildSection(String title, String subtitle, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppTheme.textSecondary, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.titleMedium),
                Text(subtitle, style: Theme.of(context).textTheme.bodyMedium),
              ],
            ),
          ),
          Icon(Icons.keyboard_arrow_down, color: AppTheme.textSecondary),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.add_circle_outline,
                size: 40,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(height: 12),
              Text(
                'Add Exercise',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Add the first exercise to start',
          style: Theme.of(
            context,
          ).textTheme.bodyLarge?.copyWith(color: AppTheme.textSecondary),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        OutlinedButton(
          onPressed: () {
            // Load past workouts
          },
          child: const Text('Load Past Workouts'),
        ),
      ],
    );
  }

  Widget _buildSelectedExercises() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Exercises',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _selectedExercises.length,
          itemBuilder: (context, index) {
            final exercise = _selectedExercises[index];
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                title: Text(
                  exercise.name,
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  '${exercise.durationMinutes ?? 3} min • ${exercise.difficulty}',
                  style: const TextStyle(color: Colors.black87),
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                  onPressed: () => _removeExercise(exercise),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildExerciseSelection(exerciseState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Add Exercises',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            DropdownButton<String>(
              value: _selectedMuscleGroup,
              items: ['All', 'Chest', 'Back', 'Legs', 'Arms', 'Core']
                  .map(
                    (group) =>
                        DropdownMenuItem(value: group, child: Text(group)),
                  )
                  .toList(),
              onChanged: (value) {
                setState(() {
                  _selectedMuscleGroup = value!;
                });
              },
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (exerciseState.isLoading)
          const Center(child: CircularProgressIndicator())
        else if (exerciseState.exercises.isEmpty)
          const Center(
            child: Text(
              'No exercises available',
              style: TextStyle(color: Colors.grey),
            ),
          )
        else
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: exerciseState.exercises.length,
              itemBuilder: (context, index) {
                final exercise = exerciseState.exercises[index];
                final isSelected = _selectedExercises.contains(exercise);

                return Container(
                  width: 160,
                  margin: const EdgeInsets.only(right: 12),
                  child: Card(
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8),
                      onTap: () => _toggleExercise(exercise),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    exercise.name,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Icon(
                                  isSelected
                                      ? Icons.check_circle
                                      : Icons.add_circle_outline,
                                  color: isSelected
                                      ? Colors.green
                                      : AppTheme.primaryColor,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              exercise.difficulty ?? 'Beginner',
                              style: TextStyle(
                                color: _getDifficultyColor(
                                  exercise.difficulty ?? 'Beginner',
                                ),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${exercise.durationMinutes ?? 3} min',
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                            const Spacer(),
                            if (exercise.muscleGroups?.isNotEmpty == true)
                              Text(
                                exercise.muscleGroups!.first,
                                style: const TextStyle(
                                  color: Colors.black87,
                                  fontSize: 11,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        if (_selectedExercises.isNotEmpty) ...[
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: () => _startWorkout(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Start Workout (${_selectedExercises.length} exercises)',
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          width: double.infinity,
          height: 56,
          child: OutlinedButton(
            onPressed: () {
              context.push('/workout/exercises');
            },
            child: const Text('Browse All Exercises'),
          ),
        ),
      ],
    );
  }

  void _editWorkoutName() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Workout Name'),
        content: TextField(
          controller: _workoutNameController,
          decoration: const InputDecoration(hintText: 'Enter workout name'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _workoutName = _workoutNameController.text;
              });
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _shareWorkout() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Workout sharing coming soon!')),
    );
  }

  void _toggleExercise(Exercise exercise) {
    setState(() {
      if (_selectedExercises.contains(exercise)) {
        _selectedExercises.remove(exercise);
      } else {
        _selectedExercises.add(exercise);
      }
    });
  }

  void _removeExercise(Exercise exercise) {
    setState(() {
      _selectedExercises.remove(exercise);
    });
  }

  void _startWorkout() {
    if (_selectedExercises.isEmpty) return;

    // Navigate to workout execution with selected exercises
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutExecutionScreen(
          exercises: _selectedExercises,
          workoutName: _workoutName,
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
