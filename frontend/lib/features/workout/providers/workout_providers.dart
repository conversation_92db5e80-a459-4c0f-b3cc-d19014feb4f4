import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/workout_models.dart';
import '../services/workout_service.dart';
import '../../exercise/models/exercise_model.dart';

// Workout Service Provider
final workoutServiceProvider = Provider<WorkoutService>((ref) {
  return WorkoutService();
});

// Current Workout Session State
class WorkoutSessionState {
  final WorkoutSession? currentSession;
  final bool isActive;
  final bool isPaused;
  final Duration elapsedTime;
  final int currentExerciseIndex;
  final int currentSet;
  final List<ExerciseLog> exerciseLogs;
  final double totalCaloriesBurned;
  final bool isLoading;
  final String? error;

  const WorkoutSessionState({
    this.currentSession,
    this.isActive = false,
    this.isPaused = false,
    this.elapsedTime = Duration.zero,
    this.currentExerciseIndex = 0,
    this.currentSet = 1,
    this.exerciseLogs = const [],
    this.totalCaloriesBurned = 0.0,
    this.isLoading = false,
    this.error,
  });

  WorkoutSessionState copyWith({
    WorkoutSession? currentSession,
    bool? isActive,
    bool? isPaused,
    Duration? elapsedTime,
    int? currentExerciseIndex,
    int? currentSet,
    List<ExerciseLog>? exerciseLogs,
    double? totalCaloriesBurned,
    bool? isLoading,
    String? error,
  }) {
    return WorkoutSessionState(
      currentSession: currentSession ?? this.currentSession,
      isActive: isActive ?? this.isActive,
      isPaused: isPaused ?? this.isPaused,
      elapsedTime: elapsedTime ?? this.elapsedTime,
      currentExerciseIndex: currentExerciseIndex ?? this.currentExerciseIndex,
      currentSet: currentSet ?? this.currentSet,
      exerciseLogs: exerciseLogs ?? this.exerciseLogs,
      totalCaloriesBurned: totalCaloriesBurned ?? this.totalCaloriesBurned,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Workout Session Notifier
class WorkoutSessionNotifier extends StateNotifier<WorkoutSessionState> {
  final WorkoutService _workoutService;
  Timer? _timer;

  WorkoutSessionNotifier(this._workoutService)
    : super(const WorkoutSessionState());

  // Start a new workout session
  Future<void> startWorkout({
    int? workoutPlanId,
    String? customWorkoutName,
    List<CustomWorkoutExercise>? customExercises,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final session = await _workoutService.createWorkoutSession(
        workoutPlanId: workoutPlanId,
        customWorkoutName: customWorkoutName,
        customExercises: customExercises,
      );

      state = state.copyWith(
        currentSession: session,
        isActive: true,
        isPaused: false,
        isLoading: false,
        currentExerciseIndex: 0,
        currentSet: 1,
        exerciseLogs: [],
        totalCaloriesBurned: 0.0,
      );

      _startTimer();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start workout: $e',
      );
    }
  }

  // Pause workout
  void pauseWorkout() {
    if (state.isActive && !state.isPaused) {
      _timer?.cancel();

      final updatedSession = state.currentSession?.copyWith(
        pausedAt: DateTime.now(),
      );

      state = state.copyWith(currentSession: updatedSession, isPaused: true);

      if (updatedSession != null) {
        _updateSessionInBackground(updatedSession);
      }
    }
  }

  // Resume workout
  void resumeWorkout() {
    if (state.isActive && state.isPaused) {
      final updatedSession = state.currentSession?.copyWith(pausedAt: null);

      state = state.copyWith(currentSession: updatedSession, isPaused: false);

      _startTimer();

      if (updatedSession != null) {
        _updateSessionInBackground(updatedSession);
      }
    }
  }

  // Complete workout
  Future<void> completeWorkout({
    int? difficultyRating,
    int? satisfactionRating,
    String? notes,
  }) async {
    if (state.currentSession == null) return;

    _timer?.cancel();

    final completedSession = state.currentSession!.copyWith(
      completedAt: DateTime.now(),
      durationMinutes: state.elapsedTime.inMinutes.toDouble(),
      caloriesBurned: state.totalCaloriesBurned,
      difficultyRating: difficultyRating,
      satisfactionRating: satisfactionRating,
      notes: notes,
      isCompleted: true,
    );

    state = state.copyWith(
      currentSession: completedSession,
      isActive: false,
      isPaused: false,
    );

    // Save to backend and local storage
    await _workoutService.updateWorkoutSession(
      completedSession.id!,
      completedSession,
    );
    await _workoutService.saveWorkoutLocally(completedSession);
  }

  // Log exercise performance
  Future<void> logExercise({
    required Exercise exercise,
    int? reps,
    double? weightKg,
    int? durationSeconds,
    double? distanceMeters,
    int? restSeconds,
    String? notes,
  }) async {
    if (state.currentSession == null) return;

    try {
      final exerciseLog = await _workoutService.logExercise(
        workoutSessionId: state.currentSession!.id!,
        exerciseId: exercise.id,
        setNumber: state.currentSet,
        reps: reps,
        weightKg: weightKg,
        durationSeconds: durationSeconds,
        distanceMeters: distanceMeters,
        restSeconds: restSeconds,
        notes: notes,
      );

      // Calculate calories for this exercise
      final exerciseCalories = _workoutService.calculateCaloriesBurned(
        exercise: exercise,
        durationMinutes: ((durationSeconds ?? 60) / 60).round(),
        reps: reps,
        sets: 1,
      );

      state = state.copyWith(
        exerciseLogs: [...state.exerciseLogs, exerciseLog],
        totalCaloriesBurned: state.totalCaloriesBurned + exerciseCalories,
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to log exercise: $e');
    }
  }

  // Move to next exercise
  void nextExercise() {
    state = state.copyWith(
      currentExerciseIndex: state.currentExerciseIndex + 1,
      currentSet: 1,
    );
  }

  // Move to previous exercise
  void previousExercise() {
    if (state.currentExerciseIndex > 0) {
      state = state.copyWith(
        currentExerciseIndex: state.currentExerciseIndex - 1,
        currentSet: 1,
      );
    }
  }

  // Move to next set
  void nextSet() {
    state = state.copyWith(currentSet: state.currentSet + 1);
  }

  // Reset workout
  void resetWorkout() {
    _timer?.cancel();
    state = const WorkoutSessionState();
  }

  // Start timer
  void _startTimer() {
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.currentSession != null && state.isActive && !state.isPaused) {
        final elapsed = DateTime.now().difference(
          state.currentSession!.startedAt,
        );
        state = state.copyWith(elapsedTime: elapsed);
      }
    });
  }

  // Update session in background
  Future<void> _updateSessionInBackground(WorkoutSession session) async {
    try {
      await _workoutService.updateWorkoutSession(session.id!, session);
    } catch (e) {
      print('Failed to update session in background: $e');
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

// Workout Session Provider
final workoutSessionProvider =
    StateNotifierProvider<WorkoutSessionNotifier, WorkoutSessionState>((ref) {
      final workoutService = ref.watch(workoutServiceProvider);
      return WorkoutSessionNotifier(workoutService);
    });

// Workout Statistics Provider
final workoutStatsProvider = FutureProvider<WorkoutStats>((ref) async {
  final workoutService = ref.watch(workoutServiceProvider);
  return await workoutService.getWorkoutStats();
});

// Local Workouts Provider
final localWorkoutsProvider = FutureProvider<List<WorkoutSession>>((ref) async {
  final workoutService = ref.watch(workoutServiceProvider);
  return await workoutService.getLocalWorkouts();
});

// Timer Provider (for rest timer, exercise timer, etc.)
class TimerState {
  final Duration duration;
  final Duration remaining;
  final bool isRunning;
  final bool isCompleted;
  final TimerType type;

  const TimerState({
    this.duration = Duration.zero,
    this.remaining = Duration.zero,
    this.isRunning = false,
    this.isCompleted = false,
    this.type = TimerType.rest,
  });

  TimerState copyWith({
    Duration? duration,
    Duration? remaining,
    bool? isRunning,
    bool? isCompleted,
    TimerType? type,
  }) {
    return TimerState(
      duration: duration ?? this.duration,
      remaining: remaining ?? this.remaining,
      isRunning: isRunning ?? this.isRunning,
      isCompleted: isCompleted ?? this.isCompleted,
      type: type ?? this.type,
    );
  }

  double get progress {
    if (duration.inSeconds == 0) return 0.0;
    return 1.0 - (remaining.inSeconds / duration.inSeconds);
  }
}

enum TimerType { rest, exercise, warmup, cooldown }

class TimerNotifier extends StateNotifier<TimerState> {
  Timer? _timer;

  TimerNotifier() : super(const TimerState());

  void startTimer({
    required Duration duration,
    TimerType type = TimerType.rest,
  }) {
    _timer?.cancel();

    state = TimerState(
      duration: duration,
      remaining: duration,
      isRunning: true,
      isCompleted: false,
      type: type,
    );

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.remaining.inSeconds > 0) {
        state = state.copyWith(
          remaining: Duration(seconds: state.remaining.inSeconds - 1),
        );
      } else {
        _completeTimer();
      }
    });
  }

  void pauseTimer() {
    _timer?.cancel();
    state = state.copyWith(isRunning: false);
  }

  void resumeTimer() {
    if (!state.isCompleted && state.remaining.inSeconds > 0) {
      startTimer(duration: state.remaining, type: state.type);
    }
  }

  void stopTimer() {
    _timer?.cancel();
    state = const TimerState();
  }

  void addTime(int seconds) {
    final newRemaining = Duration(seconds: state.remaining.inSeconds + seconds);
    final newDuration = Duration(seconds: state.duration.inSeconds + seconds);

    state = state.copyWith(duration: newDuration, remaining: newRemaining);
  }

  void _completeTimer() {
    _timer?.cancel();
    state = state.copyWith(
      isRunning: false,
      isCompleted: true,
      remaining: Duration.zero,
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

// Timer Provider
final timerProvider = StateNotifierProvider<TimerNotifier, TimerState>((ref) {
  return TimerNotifier();
});
