#!/usr/bin/env python3
"""
WibeFit v1.0 PostgreSQL Backend Server
Backend server that connects to your local PostgreSQL database
"""

import json
import os
import psycopg2
from datetime import datetime
from typing import Optional, Dict, Any
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

# PostgreSQL connection settings
DB_CONFIG = {
    'host': 'localhost',
    'database': 'wibefit_db',
    'user': 'postgres',  # Change this to your PostgreSQL username
    'password': 'password',  # Change this to your PostgreSQL password
    'port': 5432
}

def init_postgresql_database():
    """Initialize PostgreSQL database with required tables"""
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Drop existing tables to fix foreign key issues
        cursor.execute('DROP TABLE IF EXISTS request_logs CASCADE')
        cursor.execute('DROP TABLE IF EXISTS body_weights CASCADE')
        cursor.execute('DROP TABLE IF EXISTS workout_sessions CASCADE')
        cursor.execute('DROP TABLE IF EXISTS user_profiles CASCADE')
        cursor.execute('DROP TABLE IF EXISTS users CASCADE')

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                created_at TIMESTAMP NOT NULL,
                last_login_at TIMESTAMP,
                metadata JSONB
            )
        ''')
        
        # User profiles table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_profiles (
                user_id TEXT PRIMARY KEY,
                age INTEGER,
                height REAL,
                current_weight REAL,
                fitness_level TEXT,
                primary_goals JSONB,
                equipment_access JSONB,
                preferred_workout_days JSONB,
                gender TEXT,
                profile_setup_completed BOOLEAN DEFAULT FALSE,
                profile_setup_date TIMESTAMP,
                updated_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Workout sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS workout_sessions (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                workout_plan_id TEXT,
                workout_name TEXT NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                duration_minutes INTEGER DEFAULT 0,
                calories_burned INTEGER DEFAULT 0,
                body_weight REAL,
                status TEXT DEFAULT 'planned',
                exercises JSONB,
                metadata JSONB,
                created_at TIMESTAMP NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Body weight tracking table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS body_weights (
                id SERIAL PRIMARY KEY,
                user_id TEXT NOT NULL,
                weight REAL NOT NULL,
                date TIMESTAMP NOT NULL,
                created_at TIMESTAMP NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Request logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS request_logs (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP NOT NULL,
                method TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                data JSONB,
                response_status INTEGER,
                created_at TIMESTAMP NOT NULL
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ PostgreSQL database initialized successfully")
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL connection error: {e}")
        print("Please ensure PostgreSQL is running and credentials are correct")
        return False
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False

def log_request(method: str, endpoint: str, data: Any = None, status: int = 200):
    """Log request to PostgreSQL database"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO request_logs (timestamp, method, endpoint, data, response_status, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        ''', (
            datetime.now(),
            method,
            endpoint,
            json.dumps(data) if data else None,
            status,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
        print(f"📝 LOGGED: {method} {endpoint} - Status: {status}")
    except Exception as e:
        print(f"❌ Error logging request: {e}")

class WibeFitPostgreSQLHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        self.send_cors_headers()
        
        if self.path == '/':
            self.handle_root()
        elif self.path == '/health':
            self.handle_health()
        elif self.path.startswith('/api/debug'):
            self.handle_debug()
        else:
            self.send_error(404, "Not Found")

    def do_POST(self):
        """Handle POST requests"""
        self.send_cors_headers()
        
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        try:
            data = json.loads(post_data) if post_data else {}
        except json.JSONDecodeError:
            data = {}
        
        if self.path == '/api/auth/register':
            self.handle_register(data)
        elif self.path == '/api/auth/login':
            self.handle_login(data)
        elif self.path == '/api/auth/profile':
            self.handle_profile_update(data)
        elif self.path == '/api/workout/session':
            self.handle_workout_session(data)
        elif self.path == '/api/body-weight':
            self.handle_body_weight(data)
        else:
            self.send_error(404, "Not Found")

    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    def handle_root(self):
        """Handle root endpoint"""
        log_request('GET', '/')
        
        response = {
            "message": "WibeFit v1.0 PostgreSQL Backend Server",
            "version": "1.0.0",
            "database": "PostgreSQL",
            "timestamp": datetime.now().isoformat(),
            "status": "running"
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def handle_health(self):
        """Handle health check"""
        log_request('GET', '/health')
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM workout_sessions")
            workout_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM request_logs")
            log_count = cursor.fetchone()[0]
            
            conn.close()
            
            response = {
                "status": "healthy",
                "database": "PostgreSQL",
                "timestamp": datetime.now().isoformat(),
                "stats": {
                    "users": user_count,
                    "workouts": workout_count,
                    "logs": log_count
                }
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            response = {
                "status": "error",
                "message": f"Database connection failed: {e}"
            }
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())

    def handle_register(self, data):
        """Handle user registration"""
        log_request('POST', '/api/auth/register', data)
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            user_id = f"user_{int(datetime.now().timestamp())}"
            
            cursor.execute('''
                INSERT INTO users (id, email, full_name, role, created_at)
                VALUES (%s, %s, %s, %s, %s)
            ''', (
                user_id,
                data.get('email', ''),
                data.get('full_name', ''),
                'user',
                datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            response = {
                "message": "User registered successfully",
                "user": {
                    "id": user_id,
                    "email": data.get('email'),
                    "full_name": data.get('full_name')
                }
            }
            
            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ USER REGISTERED: {data.get('email')} -> ID: {user_id}")
            
        except Exception as e:
            print(f"❌ Registration error: {e}")
            self.send_error(500, str(e))

    def handle_login(self, data):
        """Handle user login"""
        log_request('POST', '/api/auth/login', data)
        
        try:
            # For demo purposes, create a simple login response
            response = {
                "access_token": f"token_{int(datetime.now().timestamp())}",
                "token_type": "bearer",
                "user": {
                    "id": "demo_user",
                    "email": data.get('email', '<EMAIL>'),
                    "full_name": "Demo User",
                    "role": "user"
                }
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ USER LOGGED IN: {data.get('email')}")
            
        except Exception as e:
            print(f"❌ Login error: {e}")
            self.send_error(500, str(e))

    def handle_profile_update(self, data):
        """Handle profile update"""
        log_request('POST', '/api/auth/profile', data)
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            user_id = data.get('user_id', 'demo_user')
            
            # Insert or update profile
            cursor.execute('''
                INSERT INTO user_profiles 
                (user_id, age, height, current_weight, fitness_level, primary_goals, 
                 equipment_access, preferred_workout_days, gender, profile_setup_completed, 
                 profile_setup_date, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (user_id) DO UPDATE SET
                age = EXCLUDED.age,
                height = EXCLUDED.height,
                current_weight = EXCLUDED.current_weight,
                fitness_level = EXCLUDED.fitness_level,
                primary_goals = EXCLUDED.primary_goals,
                equipment_access = EXCLUDED.equipment_access,
                preferred_workout_days = EXCLUDED.preferred_workout_days,
                gender = EXCLUDED.gender,
                profile_setup_completed = EXCLUDED.profile_setup_completed,
                updated_at = EXCLUDED.updated_at
            ''', (
                user_id,
                data.get('age'),
                data.get('height'),
                data.get('current_weight'),
                data.get('fitness_level'),
                json.dumps(data.get('primary_goals', [])),
                json.dumps(data.get('equipment_access', [])),
                json.dumps(data.get('preferred_workout_days', [])),
                data.get('gender'),
                True,
                datetime.now(),
                datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            response = {"message": "Profile updated successfully"}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ PROFILE UPDATED: User {user_id}")
            print(f"📊 Profile Data: {json.dumps(data, indent=2)}")
            
        except Exception as e:
            print(f"❌ Profile update error: {e}")
            self.send_error(500, str(e))

    def handle_workout_session(self, data):
        """Handle workout session save"""
        log_request('POST', '/api/workout/session', data)
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO workout_sessions 
                (id, user_id, workout_plan_id, workout_name, start_time, end_time,
                 duration_minutes, calories_burned, body_weight, status, exercises, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                data.get('id'),
                data.get('user_id'),
                data.get('workout_plan_id'),
                data.get('workout_name'),
                datetime.fromisoformat(data.get('start_time').replace('Z', '+00:00')) if data.get('start_time') else None,
                datetime.fromisoformat(data.get('end_time').replace('Z', '+00:00')) if data.get('end_time') else None,
                data.get('duration_minutes', 0),
                data.get('calories_burned', 0),
                data.get('body_weight'),
                data.get('status', 'completed'),
                json.dumps(data.get('exercises', [])),
                datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
            response = {"message": "Workout session saved successfully"}
            
            self.send_response(201)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
            print(f"✅ WORKOUT SAVED: {data.get('workout_name')} - {data.get('duration_minutes')} min")
            
        except Exception as e:
            print(f"❌ Workout save error: {e}")
            self.send_error(500, str(e))

    def handle_debug(self):
        """Handle debug endpoint"""
        log_request('GET', '/api/debug')
        
        try:
            conn = psycopg2.connect(**DB_CONFIG)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM workout_sessions")
            workout_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM request_logs")
            log_count = cursor.fetchone()[0]
            
            conn.close()
            
            response = {
                "database": "PostgreSQL",
                "users": user_count,
                "workouts": workout_count,
                "logs": log_count,
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode())
            
        except Exception as e:
            print(f"❌ Debug error: {e}")
            self.send_error(500, str(e))

def run_postgresql_server(port=8000):
    """Run the PostgreSQL server"""
    if not init_postgresql_database():
        print("❌ Failed to initialize PostgreSQL database")
        print("Please check your PostgreSQL connection settings")
        return
    
    server_address = ('', port)
    httpd = HTTPServer(server_address, WibeFitPostgreSQLHandler)
    
    print(f"🚀 WibeFit v1.0 PostgreSQL Backend Server starting...")
    print(f"📍 Server running at:")
    print(f"   - Local: http://localhost:{port}")
    print(f"   - Network: http://***********:{port}")
    print(f"   - Health: http://***********:{port}/health")
    print(f"   - Debug: http://***********:{port}/api/debug")
    print(f"💾 Database: PostgreSQL ({DB_CONFIG['database']})")
    print("")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_postgresql_server()
