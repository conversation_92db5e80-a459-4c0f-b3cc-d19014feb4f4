# Animations Folder

This folder is for storing animated exercise demonstrations and UI animations.

## Recommended Files:
- **Exercise Animations**: Looping GIFs or Lottie files showing exercise movements
- **UI Animations**: Loading spinners, transitions, micro-interactions
- **3D Models**: If using 3D exercise demonstrations

## Supported Formats:
- **GIF**: For simple exercise loops
- **<PERSON><PERSON> JSON**: For complex, scalable animations
- **WebP**: For high-quality animated images
- **SVG**: For vector-based animations

## Exercise Animation Guidelines:
- **Duration**: 2-4 seconds for a complete exercise cycle
- **Loop**: Should loop seamlessly
- **Size**: Optimize for mobile (under 2MB per animation)
- **Style**: Consistent visual style across all animations

## Naming Convention:
- `exercise_name_animation.gif`
- `exercise_name_3d.json`
- `ui_loading_spinner.json`
- `muscle_highlight_chest.gif`

## Example Files:
- `bench_press_animation.gif`
- `squat_animation.gif`
- `loading_coach_sam.json`
- `workout_complete_celebration.json`
- `muscle_group_highlight.gif`
