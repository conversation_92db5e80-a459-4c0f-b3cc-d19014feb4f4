import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../shared/models/workout_session_model.dart';
import 'mock_api_service.dart';

class WorkoutTimerService {
  static const String _currentSessionKey = 'current_workout_session';
  
  Timer? _timer;
  WorkoutSessionModel? _currentSession;
  DateTime? _startTime;
  int _elapsedSeconds = 0;
  
  // Stream controllers for real-time updates
  final StreamController<int> _elapsedTimeController = StreamController<int>.broadcast();
  final StreamController<WorkoutSessionModel?> _sessionController = StreamController<WorkoutSessionModel?>.broadcast();
  
  // Getters for streams
  Stream<int> get elapsedTimeStream => _elapsedTimeController.stream;
  Stream<WorkoutSessionModel?> get sessionStream => _sessionController.stream;
  
  // Getters for current state
  WorkoutSessionModel? get currentSession => _currentSession;
  int get elapsedSeconds => _elapsedSeconds;
  bool get isRunning => _timer?.isActive ?? false;
  
  // Singleton pattern
  static final WorkoutTimerService _instance = WorkoutTimerService._internal();
  factory WorkoutTimerService() => _instance;
  WorkoutTimerService._internal();

  /// Start a new workout session
  Future<void> startWorkout({
    required String userId,
    required String workoutPlanId,
    required String workoutName,
    required List<ExerciseSessionModel> exercises,
    double? bodyWeight,
  }) async {
    // Stop any existing session
    await stopWorkout();
    
    _startTime = DateTime.now();
    _elapsedSeconds = 0;
    
    _currentSession = WorkoutSessionModel(
      id: 'session_${DateTime.now().millisecondsSinceEpoch}',
      userId: userId,
      workoutPlanId: workoutPlanId,
      workoutName: workoutName,
      startTime: _startTime!,
      durationMinutes: 0,
      caloriesBurned: 0,
      bodyWeight: bodyWeight,
      exercises: exercises,
      status: WorkoutStatus.inProgress,
      metadata: {
        'version': '1.0',
        'device': 'mobile',
      },
    );
    
    // Save session to local storage
    await _saveCurrentSession();
    
    // Start timer
    _startTimer();
    
    // Notify listeners
    _sessionController.add(_currentSession);
    
    print('🏋️ Workout started: $workoutName');
  }

  /// Pause the current workout
  void pauseWorkout() {
    _timer?.cancel();
    print('⏸️ Workout paused');
  }

  /// Resume the current workout
  void resumeWorkout() {
    if (_currentSession != null && _startTime != null) {
      _startTimer();
      print('▶️ Workout resumed');
    }
  }

  /// Stop and complete the current workout
  Future<WorkoutSessionModel?> stopWorkout() async {
    _timer?.cancel();
    
    if (_currentSession == null) return null;
    
    final endTime = DateTime.now();
    final durationMinutes = _elapsedSeconds ~/ 60;
    final caloriesBurned = _calculateCaloriesBurned();
    
    final completedSession = _currentSession!.copyWith(
      endTime: endTime,
      durationMinutes: durationMinutes,
      caloriesBurned: caloriesBurned,
      status: WorkoutStatus.completed,
    );
    
    // Save completed session to database
    await _saveWorkoutToDatabase(completedSession);
    
    // Clear current session
    await _clearCurrentSession();
    _currentSession = null;
    _startTime = null;
    _elapsedSeconds = 0;
    
    // Notify listeners
    _sessionController.add(null);
    _elapsedTimeController.add(0);
    
    print('✅ Workout completed: ${completedSession.workoutName}');
    print('📊 Duration: $durationMinutes minutes, Calories: $caloriesBurned');
    
    return completedSession;
  }

  /// Cancel the current workout
  Future<void> cancelWorkout() async {
    _timer?.cancel();
    
    if (_currentSession != null) {
      final cancelledSession = _currentSession!.copyWith(
        status: WorkoutStatus.cancelled,
        endTime: DateTime.now(),
      );
      
      // Optionally save cancelled session
      await _saveWorkoutToDatabase(cancelledSession);
    }
    
    await _clearCurrentSession();
    _currentSession = null;
    _startTime = null;
    _elapsedSeconds = 0;
    
    _sessionController.add(null);
    _elapsedTimeController.add(0);
    
    print('❌ Workout cancelled');
  }

  /// Load any existing session from storage
  Future<void> loadExistingSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionJson = prefs.getString(_currentSessionKey);
      
      if (sessionJson != null) {
        final sessionData = json.decode(sessionJson);
        _currentSession = WorkoutSessionModel.fromJson(sessionData);
        _startTime = _currentSession!.startTime;
        
        // Calculate elapsed time since start
        _elapsedSeconds = DateTime.now().difference(_startTime!).inSeconds;
        
        // Resume timer if session is in progress
        if (_currentSession!.status == WorkoutStatus.inProgress) {
          _startTimer();
        }
        
        _sessionController.add(_currentSession);
        print('🔄 Resumed existing workout session');
      }
    } catch (e) {
      print('❌ Error loading existing session: $e');
    }
  }

  /// Private method to start the timer
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _elapsedSeconds++;
      _elapsedTimeController.add(_elapsedSeconds);
      
      // Auto-save session every minute
      if (_elapsedSeconds % 60 == 0) {
        _saveCurrentSession();
      }
    });
  }

  /// Calculate calories burned based on workout data
  int _calculateCaloriesBurned() {
    if (_currentSession == null) return 0;
    
    // Basic calorie calculation
    // This is a simplified version - in real app you'd use more sophisticated formulas
    final durationMinutes = _elapsedSeconds / 60;
    final bodyWeight = _currentSession!.bodyWeight ?? 70.0; // Default 70kg
    
    // Rough estimate: 5-8 calories per minute based on intensity
    final baseCaloriesPerMinute = 6.0;
    final weightMultiplier = bodyWeight / 70.0; // Adjust for body weight
    
    return (durationMinutes * baseCaloriesPerMinute * weightMultiplier).round();
  }

  /// Save current session to local storage
  Future<void> _saveCurrentSession() async {
    if (_currentSession == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionJson = json.encode(_currentSession!.toJson());
      await prefs.setString(_currentSessionKey, sessionJson);
    } catch (e) {
      print('❌ Error saving current session: $e');
    }
  }

  /// Clear current session from local storage
  Future<void> _clearCurrentSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentSessionKey);
    } catch (e) {
      print('❌ Error clearing current session: $e');
    }
  }

  /// Save completed workout to database
  Future<void> _saveWorkoutToDatabase(WorkoutSessionModel session) async {
    try {
      // Save to mock API (local storage)
      await MockApiService.saveWorkoutSession(session.toJson());
      
      // TODO: Also save to real backend when available
      print('💾 Workout session saved to database');
    } catch (e) {
      print('❌ Error saving workout to database: $e');
    }
  }

  /// Get workout history
  Future<List<WorkoutSessionModel>> getWorkoutHistory(String userId) async {
    try {
      final sessions = await MockApiService.getWorkoutHistory(userId);
      return sessions.map((s) => WorkoutSessionModel.fromJson(s)).toList();
    } catch (e) {
      print('❌ Error loading workout history: $e');
      return [];
    }
  }

  /// Dispose resources
  void dispose() {
    _timer?.cancel();
    _elapsedTimeController.close();
    _sessionController.close();
  }
}
