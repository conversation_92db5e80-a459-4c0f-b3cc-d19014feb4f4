"""
Workout Pydantic schemas for request/response validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class WorkoutDifficulty(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class WorkoutCategory(str, Enum):
    STRENGTH = "strength"
    CARDIO = "cardio"
    FLEXIBILITY = "flexibility"
    MIXED = "mixed"
    SPORTS = "sports"


# Workout Plan schemas
class WorkoutPlanBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    difficulty: WorkoutDifficulty
    duration_minutes: int = Field(..., gt=0, le=300)
    category: WorkoutCategory
    target_muscle_groups: Optional[List[str]] = None
    equipment_needed: Optional[List[str]] = None
    calories_estimate: Optional[float] = Field(None, gt=0)
    is_public: bool = True
    is_featured: bool = False
    tags: Optional[List[str]] = None


class WorkoutPlanCreate(WorkoutPlanBase):
    pass


class WorkoutPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    difficulty: Optional[WorkoutDifficulty] = None
    duration_minutes: Optional[int] = Field(None, gt=0, le=300)
    category: Optional[WorkoutCategory] = None
    target_muscle_groups: Optional[List[str]] = None
    equipment_needed: Optional[List[str]] = None
    calories_estimate: Optional[float] = Field(None, gt=0)
    is_public: Optional[bool] = None
    is_featured: Optional[bool] = None
    tags: Optional[List[str]] = None


class WorkoutPlanResponse(WorkoutPlanBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    slug: str
    creator_id: Optional[int] = None
    rating: float
    total_ratings: int
    total_completions: int
    thumbnail_url: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


class WorkoutSessionBase(BaseModel):
    workout_plan_id: int
    notes: Optional[str] = None


class WorkoutSessionCreate(WorkoutSessionBase):
    pass


class WorkoutSessionUpdate(BaseModel):
    completed_at: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    calories_burned: Optional[int] = None
    notes: Optional[str] = None
    exercises_completed: Optional[List[Dict[str, Any]]] = None


class WorkoutSessionResponse(WorkoutSessionBase):
    model_config = ConfigDict(from_attributes=True)

    id: int
    user_id: int
    started_at: datetime
    completed_at: Optional[datetime] = None
    is_completed: bool
    created_at: datetime


# Legacy schemas for compatibility
class WorkoutSession(WorkoutSessionResponse):
    pass


# Workout search and filter schemas
class WorkoutSearchParams(BaseModel):
    q: Optional[str] = Field(None, max_length=255)  # Search query
    difficulty: Optional[List[WorkoutDifficulty]] = None
    category: Optional[List[WorkoutCategory]] = None
    duration_min: Optional[int] = Field(None, ge=0)
    duration_max: Optional[int] = Field(None, ge=0)
    equipment: Optional[List[str]] = None
    muscle_groups: Optional[List[str]] = None
    is_featured: Optional[bool] = None
    limit: int = Field(20, ge=1, le=100)
    offset: int = Field(0, ge=0)


class WorkoutListResponse(BaseModel):
    workouts: List[WorkoutPlanResponse]
    total: int
    limit: int
    offset: int
    has_more: bool


# Workout statistics
class WorkoutStats(BaseModel):
    total_workouts: int = 0
    total_sessions: int = 0
    total_time_minutes: float = 0
    total_calories_burned: float = 0
    average_session_duration: Optional[float] = None
    favorite_workout_category: Optional[str] = None
    workouts_by_difficulty: Dict[str, int] = {}
    workouts_by_category: Dict[str, int] = {}
    recent_sessions: List[Dict[str, Any]] = []
