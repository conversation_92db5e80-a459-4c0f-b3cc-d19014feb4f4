import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/app_router.dart';
import 'core/services/auth_service.dart';
import 'core/services/theme_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first (required for app to work properly)
  // This is fast and necessary for core functionality
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Start app with Firebase ready
  runApp(const ProviderScope(child: WibeFitApp()));

  // Initialize other services in background (non-blocking)
  _initializeServicesAsync();
}

// Background initialization of services
void _initializeServicesAsync() async {
  try {
    // Initialize authentication service (Firebase is already initialized)
    AuthService().initializeAsync();
  } catch (e) {
    print('Background auth service initialization failed: $e');
    // App can still work without auth service for basic functionality
  }
}

class WibeFitApp extends ConsumerWidget {
  const WibeFitApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp.router(
      title: 'WibeFit',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      routerConfig: AppRouter.router,
    );
  }
}
