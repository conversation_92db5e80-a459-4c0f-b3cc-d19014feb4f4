import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class ExerciseService {
  static String get _baseUrl => ApiConfig.baseUrl;
  final ApiService _apiService = ApiService();

  // Get exercises with filtering
  Future<Map<String, dynamic>> getExercises({
    String? search,
    List<String>? muscleGroups,
    List<String>? equipment,
    List<String>? difficulty,
    List<String>? category,
    List<String>? exerciseType,
    bool? isFavorite,
    int limit = 20,
    int offset = 0,
  }) async {
    print(
      '🔧 ExerciseService.getExercises called with limit=$limit, offset=$offset',
    );
    return await _apiService.getExercises(
      search: search,
      muscleGroups: muscleGroups,
      equipment: equipment,
      difficulty: difficulty,
      category: category,
      isFavorite: isFavorite,
      limit: limit,
      offset: offset,
    );
  }

  // Get specific exercise by ID
  Future<Map<String, dynamic>> getExercise(int exerciseId) async {
    print('🔧 ExerciseService.getExercise called with id=$exerciseId');
    return await _apiService.getExercise(exerciseId);
  }

  // Add exercise to favorites
  Future<bool> addToFavorites(int exerciseId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/api/workouts/exercises/$exerciseId/favorite'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error adding to favorites: $e');
      return false;
    }
  }

  // Remove exercise from favorites
  Future<bool> removeFromFavorites(int exerciseId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await http.delete(
        Uri.parse('$_baseUrl/api/workouts/exercises/$exerciseId/favorite'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error removing from favorites: $e');
      return false;
    }
  }

  // Get user's favorite exercises
  Future<Map<String, dynamic>> getFavoriteExercises({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication required');
      }

      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      final uri = Uri.parse(
        '$_baseUrl/api/workouts/exercises/favorites',
      ).replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to fetch favorite exercises: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error fetching favorite exercises: $e');
      throw Exception('Failed to fetch favorite exercises: $e');
    }
  }

  // Get user's recent exercises
  Future<Map<String, dynamic>> getRecentExercises({int limit = 10}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication required');
      }

      final queryParams = <String, String>{'limit': limit.toString()};

      final uri = Uri.parse(
        '$_baseUrl/api/workouts/exercises/recent',
      ).replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to fetch recent exercises: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error fetching recent exercises: $e');
      throw Exception('Failed to fetch recent exercises: $e');
    }
  }

  // Track exercise access
  Future<bool> trackExerciseAccess(int exerciseId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        return false; // Don't track if not authenticated
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/api/workouts/exercises/$exerciseId/track-access'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Error tracking exercise access: $e');
      return false;
    }
  }

  // Get muscle groups
  Future<List<dynamic>> getMuscleGroups() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/exercises/categories/muscle-groups'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as List<dynamic>;
      } else {
        throw Exception(
          'Failed to fetch muscle groups: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('Error fetching muscle groups: $e');
      // Return default muscle groups if API fails
      return [
        {'id': 1, 'name': 'chest', 'description': 'Chest muscles'},
        {'id': 2, 'name': 'back', 'description': 'Back muscles'},
        {'id': 3, 'name': 'shoulders', 'description': 'Shoulder muscles'},
        {'id': 4, 'name': 'arms', 'description': 'Arm muscles'},
        {'id': 5, 'name': 'legs', 'description': 'Leg muscles'},
        {'id': 6, 'name': 'core', 'description': 'Core muscles'},
        {'id': 7, 'name': 'glutes', 'description': 'Glute muscles'},
      ];
    }
  }

  // Get equipment types
  Future<List<dynamic>> getEquipment() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/exercises/categories/equipment'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as List<dynamic>;
      } else {
        throw Exception('Failed to fetch equipment: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching equipment: $e');
      // Return default equipment if API fails
      return [
        {'id': 1, 'name': 'bodyweight', 'description': 'No equipment needed'},
        {'id': 2, 'name': 'dumbbell', 'description': 'Dumbbells'},
        {'id': 3, 'name': 'barbell', 'description': 'Barbells'},
        {'id': 4, 'name': 'kettlebell', 'description': 'Kettlebells'},
        {'id': 5, 'name': 'resistance_band', 'description': 'Resistance bands'},
        {'id': 6, 'name': 'cable', 'description': 'Cable machines'},
        {'id': 7, 'name': 'machine', 'description': 'Weight machines'},
      ];
    }
  }
}
