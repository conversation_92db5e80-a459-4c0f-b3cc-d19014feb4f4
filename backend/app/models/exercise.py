"""
Exercise database models
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from ..core.database import Base


class Exercise(Base):
    __tablename__ = "exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    slug = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructions = Column(JSON, nullable=True)  # List of instruction steps
    tips = Column(JSON, nullable=True)  # List of tips
    muscle_groups = Column(JSON, nullable=False)  # Primary and secondary muscle groups
    equipment = Column(JSON, nullable=True)  # Required equipment
    difficulty = Column(String(20), nullable=False)  # beginner, intermediate, advanced, expert
    exercise_type = Column(String(50), nullable=False)  # strength, cardio, flexibility, balance
    category = Column(String(50), nullable=False)  # upper_body, lower_body, core, full_body
    duration_minutes = Column(Integer, nullable=True)  # For timed exercises
    calories_per_minute = Column(Float, nullable=True)  # Estimated calories burned
    video_url = Column(String(500), nullable=True)  # GCS video URL
    image_url = Column(String(500), nullable=True)  # GCS image URL
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    creator = relationship("User")
    workout_exercises = relationship("WorkoutExercise", back_populates="exercise")
    user_exercises = relationship("UserExercise", back_populates="exercise")


class ExerciseVariation(Base):
    __tablename__ = "exercise_variations"
    
    id = Column(Integer, primary_key=True, index=True)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    difficulty_modifier = Column(String(20), nullable=False)  # easier, harder, same
    instructions = Column(JSON, nullable=True)
    video_url = Column(String(500), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    exercise = relationship("Exercise")


class UserExercise(Base):
    __tablename__ = "user_exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False)
    is_favorite = Column(Boolean, default=False)
    personal_best_weight = Column(Float, nullable=True)
    personal_best_reps = Column(Integer, nullable=True)
    personal_best_time = Column(Float, nullable=True)  # in seconds
    notes = Column(Text, nullable=True)
    last_performed = Column(DateTime, nullable=True)
    total_sessions = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User")
    exercise = relationship("Exercise", back_populates="user_exercises")


class ExerciseLog(Base):
    __tablename__ = "exercise_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False)
    workout_session_id = Column(Integer, ForeignKey("workout_sessions.id"), nullable=True)
    sets_completed = Column(Integer, nullable=True)
    reps_completed = Column(JSON, nullable=True)  # List of reps per set
    weight_used = Column(JSON, nullable=True)  # List of weights per set
    duration_seconds = Column(Float, nullable=True)
    distance_meters = Column(Float, nullable=True)
    calories_burned = Column(Float, nullable=True)
    difficulty_rating = Column(Integer, nullable=True)  # 1-10 scale
    form_rating = Column(Integer, nullable=True)  # 1-10 scale
    notes = Column(Text, nullable=True)
    performed_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
    exercise = relationship("Exercise")
    workout_session = relationship("WorkoutSession")


class MuscleGroup(Base):
    __tablename__ = "muscle_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    category = Column(String(50), nullable=False)  # primary, secondary
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class Equipment(Base):
    __tablename__ = "equipment"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    category = Column(String(50), nullable=False)  # free_weights, machines, bodyweight, cardio
    description = Column(Text, nullable=True)
    image_url = Column(String(500), nullable=True)
    is_common = Column(Boolean, default=False)  # Common equipment for filtering
    created_at = Column(DateTime(timezone=True), server_default=func.now())
