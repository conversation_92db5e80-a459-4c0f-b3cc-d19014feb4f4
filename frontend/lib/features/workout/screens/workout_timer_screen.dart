import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/workout_timer_service.dart';
import '../../../shared/models/workout_session_model.dart';
import '../../auth/providers/auth_provider.dart';

class WorkoutTimerScreen extends ConsumerStatefulWidget {
  final String workoutName;
  final String workoutPlanId;

  const WorkoutTimerScreen({
    super.key,
    required this.workoutName,
    required this.workoutPlanId,
  });

  @override
  ConsumerState<WorkoutTimerScreen> createState() => _WorkoutTimerScreenState();
}

class _WorkoutTimerScreenState extends ConsumerState<WorkoutTimerScreen> {
  final WorkoutTimerService _timerService = WorkoutTimerService();
  int _elapsedSeconds = 0;
  WorkoutSessionModel? _currentSession;
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _initializeWorkout();
    _setupListeners();
  }

  void _setupListeners() {
    _timerService.elapsedTimeStream.listen((seconds) {
      if (mounted) {
        setState(() {
          _elapsedSeconds = seconds;
        });
      }
    });

    _timerService.sessionStream.listen((session) {
      if (mounted) {
        setState(() {
          _currentSession = session;
          _isRunning = session?.status == WorkoutStatus.inProgress;
        });
      }
    });
  }

  Future<void> _initializeWorkout() async {
    final user = ref.read(authProvider).user;
    if (user == null) return;

    // Get user's current weight from profile
    final profile = user.metadata;
    final bodyWeight = profile?['current_weight']?.toDouble() ?? 70.0;

    // Create sample exercises for the workout
    final exercises = _createSampleExercises();

    await _timerService.startWorkout(
      userId: user.id,
      workoutPlanId: widget.workoutPlanId,
      workoutName: widget.workoutName,
      exercises: exercises,
      bodyWeight: bodyWeight,
    );
  }

  List<ExerciseSessionModel> _createSampleExercises() {
    return [
      const ExerciseSessionModel(
        id: 'ex1',
        exerciseId: 'push_ups',
        exerciseName: 'Push-ups',
        sets: 3,
        reps: 15,
        durationSeconds: 0,
        caloriesBurned: 0,
        completed: false,
      ),
      const ExerciseSessionModel(
        id: 'ex2',
        exerciseId: 'squats',
        exerciseName: 'Squats',
        sets: 3,
        reps: 20,
        durationSeconds: 0,
        caloriesBurned: 0,
        completed: false,
      ),
      const ExerciseSessionModel(
        id: 'ex3',
        exerciseId: 'plank',
        exerciseName: 'Plank',
        sets: 3,
        reps: 1,
        durationSeconds: 30,
        caloriesBurned: 0,
        completed: false,
      ),
    ];
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _pauseResumeWorkout() async {
    if (_isRunning) {
      _timerService.pauseWorkout();
    } else {
      _timerService.resumeWorkout();
    }
  }

  Future<void> _finishWorkout() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Finish Workout'),
        content: const Text('Are you sure you want to finish this workout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Finish'),
          ),
        ],
      ),
    );

    if (result == true) {
      final completedSession = await _timerService.stopWorkout();
      if (completedSession != null && mounted) {
        _showWorkoutSummary(completedSession);
      }
    }
  }

  void _showWorkoutSummary(WorkoutSessionModel session) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Workout Complete!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Great job completing "${session.workoutName}"!'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    const Icon(Icons.timer, color: AppTheme.primaryColor),
                    const SizedBox(height: 4),
                    Text('${session.durationMinutes} min'),
                    const Text('Duration', style: TextStyle(fontSize: 12)),
                  ],
                ),
                Column(
                  children: [
                    const Icon(Icons.local_fire_department, color: Colors.orange),
                    const SizedBox(height: 4),
                    Text('${session.caloriesBurned}'),
                    const Text('Calories', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.go('/workout');
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.workoutName),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () async {
            final result = await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Cancel Workout'),
                content: const Text('Are you sure you want to cancel this workout?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Continue'),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text('Cancel Workout'),
                  ),
                ],
              ),
            );

            if (result == true) {
              await _timerService.cancelWorkout();
              if (mounted) {
                context.pop();
              }
            }
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Timer Display
            Expanded(
              flex: 2,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _formatTime(_elapsedSeconds),
                      style: const TextStyle(
                        fontSize: 72,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isRunning ? 'Workout in Progress' : 'Workout Paused',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Exercise List
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Exercises',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _currentSession?.exercises.length ?? 0,
                      itemBuilder: (context, index) {
                        final exercise = _currentSession!.exercises[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            title: Text(exercise.exerciseName),
                            subtitle: Text(
                              exercise.durationSeconds > 0
                                  ? '${exercise.sets} sets × ${exercise.durationSeconds}s'
                                  : '${exercise.sets} sets × ${exercise.reps} reps',
                            ),
                            trailing: exercise.completed
                                ? const Icon(Icons.check_circle, color: Colors.green)
                                : const Icon(Icons.radio_button_unchecked, color: Colors.grey),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // Control Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _pauseResumeWorkout,
                    icon: Icon(_isRunning ? Icons.pause : Icons.play_arrow),
                    label: Text(_isRunning ? 'Pause' : 'Resume'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isRunning ? Colors.orange : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _finishWorkout,
                    icon: const Icon(Icons.stop),
                    label: const Text('Finish'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Don't dispose the timer service here as it should persist
    super.dispose();
  }
}
