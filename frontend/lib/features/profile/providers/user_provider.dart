import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/user_service.dart';

// User service provider
final userServiceProvider = Provider<UserService>((ref) {
  return UserService();
});

// Current user provider
final currentUserProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getCurrentUser();
});

// User stats provider
final userStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getUserStats();
});

// User achievements provider
final userAchievementsProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getUserAchievements();
});

// Favorite exercises count provider
final favoriteExercisesCountProvider = FutureProvider<int>((ref) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getFavoriteExercisesCount();
});

// Recent exercises count provider
final recentExercisesCountProvider = FutureProvider<int>((ref) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getRecentExercisesCount();
});

// User profile data provider (fitness data from database)
final userProfileDataProvider = FutureProvider<Map<String, dynamic>?>((
  ref,
) async {
  final userService = ref.read(userServiceProvider);
  return await userService.getUserProfileData();
});
