import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';

class ChangePlanScreen extends ConsumerStatefulWidget {
  const ChangePlanScreen({super.key});

  @override
  ConsumerState<ChangePlanScreen> createState() => _ChangePlanScreenState();
}

class _ChangePlanScreenState extends ConsumerState<ChangePlanScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: const Text(
          'Change Plan',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.primaryColor,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondary,
          tabs: const [
            Tab(text: 'AI Coach Plan'),
            Tab(text: 'Custom Plan'),
            Tab(text: 'Community Plans'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAICoachTab(),
          _buildCustomPlanTab(),
          _buildCommunityPlansTab(),
        ],
      ),
    );
  }

  Widget _buildAICoachTab() {
    final aiPlans = [
      {
        'name': 'Weight Loss Plan',
        'description':
            'High-intensity workouts designed to burn calories and reduce body fat',
        'duration': '8 weeks',
        'frequency': '4-5 times/week',
        'equipment': 'Bodyweight + Dumbbells',
        'difficulty': 'Intermediate',
        'goals': ['Fat Loss', 'Cardio Endurance', 'Muscle Tone'],
        'isRecommended': true,
      },
      {
        'name': 'Muscle Building Plan',
        'description':
            'Progressive strength training to build lean muscle mass',
        'duration': '12 weeks',
        'frequency': '4-6 times/week',
        'equipment': 'Full Gym Equipment',
        'difficulty': 'Advanced',
        'goals': ['Muscle Growth', 'Strength', 'Power'],
        'isRecommended': false,
      },
      {
        'name': 'Beginner Fitness Plan',
        'description': 'Perfect introduction to fitness with basic movements',
        'duration': '6 weeks',
        'frequency': '3-4 times/week',
        'equipment': 'Bodyweight Only',
        'difficulty': 'Beginner',
        'goals': ['General Fitness', 'Habit Building', 'Basic Strength'],
        'isRecommended': false,
      },
      {
        'name': 'Athletic Performance',
        'description':
            'Sport-specific training for enhanced athletic performance',
        'duration': '10 weeks',
        'frequency': '5-6 times/week',
        'equipment': 'Mixed Equipment',
        'difficulty': 'Advanced',
        'goals': ['Speed', 'Agility', 'Power', 'Endurance'],
        'isRecommended': false,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: aiPlans.length,
      itemBuilder: (context, index) {
        final plan = aiPlans[index];
        return _buildPlanCard(
          plan: plan,
          onTap: () => _selectPlan(plan),
          showAIBadge: true,
        );
      },
    );
  }

  Widget _buildCustomPlanTab() {
    final customPlans = [
      {
        'name': 'My Home Workout',
        'description': 'Custom bodyweight routine for home training',
        'duration': 'Ongoing',
        'frequency': '3 times/week',
        'equipment': 'Bodyweight Only',
        'difficulty': 'Intermediate',
        'goals': ['Convenience', 'Consistency'],
        'isCustom': true,
        'createdDate': '2024-01-10',
      },
      {
        'name': 'Quick Morning Routine',
        'description': '15-minute energizing morning workout',
        'duration': 'Ongoing',
        'frequency': 'Daily',
        'equipment': 'Bodyweight Only',
        'difficulty': 'Beginner',
        'goals': ['Energy', 'Consistency'],
        'isCustom': true,
        'createdDate': '2024-01-05',
      },
    ];

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _createCustomPlan(),
              icon: const Icon(Icons.add, color: Colors.white),
              label: const Text(
                'Add Custom Plan',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: customPlans.length,
            itemBuilder: (context, index) {
              final plan = customPlans[index];
              return _buildPlanCard(
                plan: plan,
                onTap: () => _selectPlan(plan),
                showCustomBadge: true,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCommunityPlansTab() {
    final communityPlans = [
      {
        'name': 'Beach Body Challenge',
        'description': 'Popular summer preparation workout by @FitTrainer_Mike',
        'duration': '6 weeks',
        'frequency': '5 times/week',
        'equipment': 'Dumbbells + Resistance Bands',
        'difficulty': 'Intermediate',
        'goals': ['Fat Loss', 'Muscle Tone', 'Definition'],
        'author': '@FitTrainer_Mike',
        'rating': 4.8,
        'followers': 2847,
        'isVerified': true,
      },
      {
        'name': 'Powerlifting Basics',
        'description': 'Verified program by certified powerlifting coach',
        'duration': '16 weeks',
        'frequency': '4 times/week',
        'equipment': 'Barbell + Plates',
        'difficulty': 'Advanced',
        'goals': ['Strength', 'Powerlifting', 'Technique'],
        'author': '@PowerCoach_Sarah',
        'rating': 4.9,
        'followers': 1523,
        'isVerified': true,
      },
      {
        'name': 'Yoga Flow Fusion',
        'description': 'Combining yoga with strength training movements',
        'duration': '4 weeks',
        'frequency': '3-4 times/week',
        'equipment': 'Yoga Mat + Light Weights',
        'difficulty': 'Beginner',
        'goals': ['Flexibility', 'Balance', 'Mindfulness'],
        'author': '@YogaWarrior_Jen',
        'rating': 4.6,
        'followers': 892,
        'isVerified': false,
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: communityPlans.length,
      itemBuilder: (context, index) {
        final plan = communityPlans[index];
        return _buildPlanCard(
          plan: plan,
          onTap: () => _selectPlan(plan),
          showCommunityBadge: true,
        );
      },
    );
  }

  Widget _buildPlanCard({
    required Map<String, dynamic> plan,
    required VoidCallback onTap,
    bool showAIBadge = false,
    bool showCustomBadge = false,
    bool showCommunityBadge = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      plan['name'],
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (showAIBadge) _buildAIBadge(),
                  if (showCustomBadge) _buildCustomBadge(),
                  if (showCommunityBadge) _buildCommunityBadge(plan),
                  if (plan['isRecommended'] == true) _buildRecommendedBadge(),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                plan['description'],
                style: const TextStyle(color: Colors.black87, fontSize: 14),
              ),
              const SizedBox(height: 12),
              _buildPlanDetails(plan),
              if (showCommunityBadge) ...[
                const SizedBox(height: 12),
                _buildCommunityStats(plan),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAIBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.psychology, color: Colors.purple, size: 12),
          const SizedBox(width: 4),
          Text(
            'AI',
            style: TextStyle(
              color: Colors.purple,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.edit, color: Colors.blue, size: 12),
          const SizedBox(width: 4),
          Text(
            'Custom',
            style: TextStyle(
              color: Colors.blue,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommunityBadge(Map<String, dynamic> plan) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (plan['isVerified'] == true) ...[
            Icon(Icons.verified, color: Colors.green, size: 12),
            const SizedBox(width: 4),
          ],
          Icon(Icons.people, color: Colors.green, size: 12),
          const SizedBox(width: 4),
          Text(
            plan['isVerified'] == true ? 'Verified' : 'Community',
            style: TextStyle(
              color: Colors.green,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendedBadge() {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, color: Colors.orange, size: 12),
          const SizedBox(width: 4),
          Text(
            'Recommended',
            style: TextStyle(
              color: Colors.orange,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanDetails(Map<String, dynamic> plan) {
    return Column(
      children: [
        Row(
          children: [
            _buildDetailChip(Icons.schedule, plan['duration']),
            const SizedBox(width: 8),
            _buildDetailChip(Icons.fitness_center, plan['frequency']),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildDetailChip(Icons.sports_gymnastics, plan['equipment']),
            const SizedBox(width: 8),
            _buildDetailChip(
              Icons.trending_up,
              plan['difficulty'],
              color: _getDifficultyColor(plan['difficulty']),
            ),
          ],
        ),
        if (plan['goals'] != null) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: (plan['goals'] as List<String>)
                .map((goal) => _buildGoalChip(goal))
                .toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailChip(IconData icon, String text, {Color? color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: (color ?? Colors.grey).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color ?? Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: color ?? Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalChip(String goal) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        goal,
        style: TextStyle(
          fontSize: 10,
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildCommunityStats(Map<String, dynamic> plan) {
    return Row(
      children: [
        Icon(Icons.person, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          plan['author'],
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 16),
        Icon(Icons.star, size: 14, color: Colors.amber),
        const SizedBox(width: 4),
        Text(
          plan['rating'].toString(),
          style: const TextStyle(
            fontSize: 12,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 16),
        Icon(Icons.people, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          '${plan['followers']} followers',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _selectPlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Switch to ${plan['name']}?'),
        content: Text(
          'Are you sure you want to switch to this plan? Your current progress will be saved.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Switched to ${plan['name']}'),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Switch Plan'),
          ),
        ],
      ),
    );
  }

  void _createCustomPlan() {
    context.push('/workout/edit-custom-plan');
  }
}
