// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkoutPlan _$WorkoutPlanFromJson(Map<String, dynamic> json) => WorkoutPlan(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String?,
      difficulty: json['difficulty'] as String,
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      category: json['category'] as String,
      targetMuscleGroups: (json['targetMuscleGroups'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      equipmentNeeded: (json['equipmentNeeded'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      caloriesEstimate: (json['caloriesEstimate'] as num?)?.toDouble(),
      isPublic: json['isPublic'] as bool? ?? true,
      isFeatured: json['isFeatured'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: (json['totalRatings'] as num?)?.toInt() ?? 0,
      totalCompletions: (json['totalCompletions'] as num?)?.toInt() ?? 0,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      exercises: (json['exercises'] as List<dynamic>?)
          ?.map((e) => WorkoutExercise.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutPlanToJson(WorkoutPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'slug': instance.slug,
      'description': instance.description,
      'difficulty': instance.difficulty,
      'durationMinutes': instance.durationMinutes,
      'category': instance.category,
      'targetMuscleGroups': instance.targetMuscleGroups,
      'equipmentNeeded': instance.equipmentNeeded,
      'caloriesEstimate': instance.caloriesEstimate,
      'isPublic': instance.isPublic,
      'isFeatured': instance.isFeatured,
      'rating': instance.rating,
      'totalRatings': instance.totalRatings,
      'totalCompletions': instance.totalCompletions,
      'tags': instance.tags,
      'thumbnailUrl': instance.thumbnailUrl,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'exercises': instance.exercises,
    };

WorkoutExercise _$WorkoutExerciseFromJson(Map<String, dynamic> json) =>
    WorkoutExercise(
      id: (json['id'] as num).toInt(),
      workoutPlanId: (json['workoutPlanId'] as num).toInt(),
      exerciseId: (json['exerciseId'] as num).toInt(),
      orderIndex: (json['orderIndex'] as num).toInt(),
      sets: (json['sets'] as num?)?.toInt(),
      reps: json['reps'],
      weightKg: (json['weightKg'] as num?)?.toDouble(),
      durationSeconds: (json['durationSeconds'] as num?)?.toInt(),
      restSeconds: (json['restSeconds'] as num?)?.toInt(),
      distanceMeters: (json['distanceMeters'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      isWarmup: json['isWarmup'] as bool? ?? false,
      isCooldown: json['isCooldown'] as bool? ?? false,
      exercise: json['exercise'] == null
          ? null
          : Exercise.fromJson(json['exercise'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WorkoutExerciseToJson(WorkoutExercise instance) =>
    <String, dynamic>{
      'id': instance.id,
      'workoutPlanId': instance.workoutPlanId,
      'exerciseId': instance.exerciseId,
      'orderIndex': instance.orderIndex,
      'sets': instance.sets,
      'reps': instance.reps,
      'weightKg': instance.weightKg,
      'durationSeconds': instance.durationSeconds,
      'restSeconds': instance.restSeconds,
      'distanceMeters': instance.distanceMeters,
      'notes': instance.notes,
      'isWarmup': instance.isWarmup,
      'isCooldown': instance.isCooldown,
      'exercise': instance.exercise,
    };

WorkoutSession _$WorkoutSessionFromJson(Map<String, dynamic> json) =>
    WorkoutSession(
      id: (json['id'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
      workoutPlanId: (json['workoutPlanId'] as num?)?.toInt(),
      name: json['name'] as String?,
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      pausedAt: json['pausedAt'] == null
          ? null
          : DateTime.parse(json['pausedAt'] as String),
      durationMinutes: (json['durationMinutes'] as num?)?.toDouble(),
      caloriesBurned: (json['caloriesBurned'] as num?)?.toDouble(),
      averageHeartRate: (json['averageHeartRate'] as num?)?.toInt(),
      maxHeartRate: (json['maxHeartRate'] as num?)?.toInt(),
      difficultyRating: (json['difficultyRating'] as num?)?.toInt(),
      satisfactionRating: (json['satisfactionRating'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      weather: json['weather'] as String?,
      location: json['location'] as String?,
      isCompleted: json['isCompleted'] as bool? ?? false,
      workoutPlan: json['workoutPlan'] == null
          ? null
          : WorkoutPlan.fromJson(json['workoutPlan'] as Map<String, dynamic>),
      exerciseLogs: (json['exerciseLogs'] as List<dynamic>?)
          ?.map((e) => ExerciseLog.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutSessionToJson(WorkoutSession instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'workoutPlanId': instance.workoutPlanId,
      'name': instance.name,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'pausedAt': instance.pausedAt?.toIso8601String(),
      'durationMinutes': instance.durationMinutes,
      'caloriesBurned': instance.caloriesBurned,
      'averageHeartRate': instance.averageHeartRate,
      'maxHeartRate': instance.maxHeartRate,
      'difficultyRating': instance.difficultyRating,
      'satisfactionRating': instance.satisfactionRating,
      'notes': instance.notes,
      'weather': instance.weather,
      'location': instance.location,
      'isCompleted': instance.isCompleted,
      'workoutPlan': instance.workoutPlan,
      'exerciseLogs': instance.exerciseLogs,
    };

ExerciseLog _$ExerciseLogFromJson(Map<String, dynamic> json) => ExerciseLog(
      id: (json['id'] as num?)?.toInt(),
      workoutSessionId: (json['workoutSessionId'] as num?)?.toInt(),
      exerciseId: (json['exerciseId'] as num).toInt(),
      setNumber: (json['setNumber'] as num).toInt(),
      reps: (json['reps'] as num?)?.toInt(),
      weightKg: (json['weightKg'] as num?)?.toDouble(),
      durationSeconds: (json['durationSeconds'] as num?)?.toInt(),
      distanceMeters: (json['distanceMeters'] as num?)?.toDouble(),
      restSeconds: (json['restSeconds'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      exercise: json['exercise'] == null
          ? null
          : Exercise.fromJson(json['exercise'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ExerciseLogToJson(ExerciseLog instance) =>
    <String, dynamic>{
      'id': instance.id,
      'workoutSessionId': instance.workoutSessionId,
      'exerciseId': instance.exerciseId,
      'setNumber': instance.setNumber,
      'reps': instance.reps,
      'weightKg': instance.weightKg,
      'durationSeconds': instance.durationSeconds,
      'distanceMeters': instance.distanceMeters,
      'restSeconds': instance.restSeconds,
      'notes': instance.notes,
      'completedAt': instance.completedAt?.toIso8601String(),
      'exercise': instance.exercise,
    };

CustomWorkout _$CustomWorkoutFromJson(Map<String, dynamic> json) =>
    CustomWorkout(
      name: json['name'] as String,
      difficulty: json['difficulty'] as String,
      duration: json['duration'] as String,
      target: json['target'] as String,
      exercises: (json['exercises'] as List<dynamic>)
          .map((e) => CustomWorkoutExercise.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$CustomWorkoutToJson(CustomWorkout instance) =>
    <String, dynamic>{
      'name': instance.name,
      'difficulty': instance.difficulty,
      'duration': instance.duration,
      'target': instance.target,
      'exercises': instance.exercises,
      'createdAt': instance.createdAt.toIso8601String(),
    };

CustomWorkoutExercise _$CustomWorkoutExerciseFromJson(
        Map<String, dynamic> json) =>
    CustomWorkoutExercise(
      exercise: Exercise.fromJson(json['exercise'] as Map<String, dynamic>),
      sets: (json['sets'] as num).toInt(),
      reps: (json['reps'] as num).toInt(),
      restSeconds: (json['restSeconds'] as num).toInt(),
      weightKg: (json['weightKg'] as num?)?.toDouble(),
      durationSeconds: (json['durationSeconds'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CustomWorkoutExerciseToJson(
        CustomWorkoutExercise instance) =>
    <String, dynamic>{
      'exercise': instance.exercise,
      'sets': instance.sets,
      'reps': instance.reps,
      'restSeconds': instance.restSeconds,
      'weightKg': instance.weightKg,
      'durationSeconds': instance.durationSeconds,
    };

WorkoutStats _$WorkoutStatsFromJson(Map<String, dynamic> json) => WorkoutStats(
      totalWorkouts: (json['totalWorkouts'] as num?)?.toInt() ?? 0,
      totalSessions: (json['totalSessions'] as num?)?.toInt() ?? 0,
      totalTimeMinutes: (json['totalTimeMinutes'] as num?)?.toDouble() ?? 0,
      totalCaloriesBurned:
          (json['totalCaloriesBurned'] as num?)?.toDouble() ?? 0,
      averageSessionDuration:
          (json['averageSessionDuration'] as num?)?.toDouble(),
      favoriteWorkoutCategory: json['favoriteWorkoutCategory'] as String?,
      workoutsByDifficulty:
          (json['workoutsByDifficulty'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toInt()),
              ) ??
              const {},
      workoutsByCategory:
          (json['workoutsByCategory'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toInt()),
              ) ??
              const {},
      recentSessions: (json['recentSessions'] as List<dynamic>?)
              ?.map((e) => WorkoutSession.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$WorkoutStatsToJson(WorkoutStats instance) =>
    <String, dynamic>{
      'totalWorkouts': instance.totalWorkouts,
      'totalSessions': instance.totalSessions,
      'totalTimeMinutes': instance.totalTimeMinutes,
      'totalCaloriesBurned': instance.totalCaloriesBurned,
      'averageSessionDuration': instance.averageSessionDuration,
      'favoriteWorkoutCategory': instance.favoriteWorkoutCategory,
      'workoutsByDifficulty': instance.workoutsByDifficulty,
      'workoutsByCategory': instance.workoutsByCategory,
      'recentSessions': instance.recentSessions,
    };
