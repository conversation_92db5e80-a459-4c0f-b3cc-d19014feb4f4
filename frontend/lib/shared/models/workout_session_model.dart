class WorkoutSessionModel {
  final String id;
  final String userId;
  final String workoutPlanId;
  final String workoutName;
  final DateTime startTime;
  final DateTime? endTime;
  final int durationMinutes;
  final int caloriesBurned;
  final double? bodyWeight; // User's weight at time of workout
  final List<ExerciseSessionModel> exercises;
  final WorkoutStatus status;
  final Map<String, dynamic>? metadata;

  const WorkoutSessionModel({
    required this.id,
    required this.userId,
    required this.workoutPlanId,
    required this.workoutName,
    required this.startTime,
    this.endTime,
    required this.durationMinutes,
    required this.caloriesBurned,
    this.bodyWeight,
    required this.exercises,
    required this.status,
    this.metadata,
  });

  factory WorkoutSessionModel.fromJson(Map<String, dynamic> json) {
    return WorkoutSessionModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      workoutPlanId: json['workout_plan_id'] as String,
      workoutName: json['workout_name'] as String,
      startTime: DateTime.parse(json['start_time'] as String),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      durationMinutes: json['duration_minutes'] as int,
      caloriesBurned: json['calories_burned'] as int,
      bodyWeight: json['body_weight']?.toDouble(),
      exercises:
          (json['exercises'] as List<dynamic>?)
              ?.map(
                (e) => ExerciseSessionModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
      status: WorkoutStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => WorkoutStatus.planned,
      ),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'workout_plan_id': workoutPlanId,
      'workout_name': workoutName,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration_minutes': durationMinutes,
      'calories_burned': caloriesBurned,
      'body_weight': bodyWeight,
      'exercises': exercises.map((e) => e.toJson()).toList(),
      'status': status.name,
      'metadata': metadata,
    };
  }

  WorkoutSessionModel copyWith({
    String? id,
    String? userId,
    String? workoutPlanId,
    String? workoutName,
    DateTime? startTime,
    DateTime? endTime,
    int? durationMinutes,
    int? caloriesBurned,
    double? bodyWeight,
    List<ExerciseSessionModel>? exercises,
    WorkoutStatus? status,
    Map<String, dynamic>? metadata,
  }) {
    return WorkoutSessionModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      workoutPlanId: workoutPlanId ?? this.workoutPlanId,
      workoutName: workoutName ?? this.workoutName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      caloriesBurned: caloriesBurned ?? this.caloriesBurned,
      bodyWeight: bodyWeight ?? this.bodyWeight,
      exercises: exercises ?? this.exercises,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }
}

class ExerciseSessionModel {
  final String id;
  final String exerciseId;
  final String exerciseName;
  final int sets;
  final int reps;
  final double? weight;
  final int durationSeconds;
  final int caloriesBurned;
  final bool completed;

  const ExerciseSessionModel({
    required this.id,
    required this.exerciseId,
    required this.exerciseName,
    required this.sets,
    required this.reps,
    this.weight,
    required this.durationSeconds,
    required this.caloriesBurned,
    required this.completed,
  });

  factory ExerciseSessionModel.fromJson(Map<String, dynamic> json) {
    return ExerciseSessionModel(
      id: json['id'] as String,
      exerciseId: json['exercise_id'] as String,
      exerciseName: json['exercise_name'] as String,
      sets: json['sets'] as int,
      reps: json['reps'] as int,
      weight: json['weight']?.toDouble(),
      durationSeconds: json['duration_seconds'] as int,
      caloriesBurned: json['calories_burned'] as int,
      completed: json['completed'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'exercise_id': exerciseId,
      'exercise_name': exerciseName,
      'sets': sets,
      'reps': reps,
      'weight': weight,
      'duration_seconds': durationSeconds,
      'calories_burned': caloriesBurned,
      'completed': completed,
    };
  }
}

enum WorkoutStatus { planned, inProgress, completed, cancelled }

// Workout Analytics Model
class WorkoutAnalyticsModel {
  final String userId;
  final int totalWorkouts;
  final int totalMinutes;
  final int totalCalories;
  final double averageWorkoutDuration;
  final int currentStreak;
  final int longestStreak;
  final List<double> weeklyWeights;
  final List<int> weeklyCalories;
  final Map<String, int> exerciseFrequency;
  final DateTime lastWorkout;

  const WorkoutAnalyticsModel({
    required this.userId,
    required this.totalWorkouts,
    required this.totalMinutes,
    required this.totalCalories,
    required this.averageWorkoutDuration,
    required this.currentStreak,
    required this.longestStreak,
    required this.weeklyWeights,
    required this.weeklyCalories,
    required this.exerciseFrequency,
    required this.lastWorkout,
  });

  factory WorkoutAnalyticsModel.fromJson(Map<String, dynamic> json) {
    return WorkoutAnalyticsModel(
      userId: json['user_id'] as String,
      totalWorkouts: json['total_workouts'] as int,
      totalMinutes: json['total_minutes'] as int,
      totalCalories: json['total_calories'] as int,
      averageWorkoutDuration:
          json['average_workout_duration']?.toDouble() ?? 0.0,
      currentStreak: json['current_streak'] as int,
      longestStreak: json['longest_streak'] as int,
      weeklyWeights:
          (json['weekly_weights'] as List<dynamic>?)
              ?.map((e) => (e as num).toDouble())
              .toList() ??
          [],
      weeklyCalories:
          (json['weekly_calories'] as List<dynamic>?)
              ?.map((e) => e as int)
              .toList() ??
          [],
      exerciseFrequency: Map<String, int>.from(
        json['exercise_frequency'] ?? {},
      ),
      lastWorkout: DateTime.parse(json['last_workout'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'total_workouts': totalWorkouts,
      'total_minutes': totalMinutes,
      'total_calories': totalCalories,
      'average_workout_duration': averageWorkoutDuration,
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'weekly_weights': weeklyWeights,
      'weekly_calories': weeklyCalories,
      'exercise_frequency': exerciseFrequency,
      'last_workout': lastWorkout.toIso8601String(),
    };
  }
}
