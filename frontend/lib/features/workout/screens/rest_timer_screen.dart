import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../exercise/models/exercise_model.dart';

class RestTimerScreen extends StatefulWidget {
  final Exercise exercise;

  const RestTimerScreen({super.key, required this.exercise});

  @override
  State<RestTimerScreen> createState() => _RestTimerScreenState();
}

class _RestTimerScreenState extends State<RestTimerScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  int _currentSet = 1;
  final int _totalSets = 3;
  int _restTime = 60; // seconds
  int _remainingTime = 60;
  bool _isResting = false;
  bool _isRunning = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(seconds: _restTime),
      vsync: this,
    );
    _animation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.exercise.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            _buildSetProgress(),
            const SizedBox(height: 40),
            _buildTimer(),
            const SizedBox(height: 40),
            _buildControls(),
            const Spacer(),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildSetProgress() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Text(
            'Set $_currentSet of $_totalSets',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _currentSet / _totalSets,
            backgroundColor: AppTheme.backgroundColor,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildTimer() {
    return SizedBox(
      width: 250,
      height: 250,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 250,
            height: 250,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: 8,
                  backgroundColor: AppTheme.cardColor,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _isResting ? AppTheme.primaryColor : Colors.green,
                  ),
                );
              },
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _isResting ? 'Rest Time' : 'Exercise Time',
                style: TextStyle(fontSize: 16, color: AppTheme.textSecondary),
              ),
              const SizedBox(height: 8),
              Text(
                _formatTime(_remainingTime),
                style: const TextStyle(
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildControlButton(
          icon: Icons.remove,
          onPressed: () => _adjustTime(-10),
          label: '-10s',
        ),
        _buildControlButton(
          icon: _isRunning ? Icons.pause : Icons.play_arrow,
          onPressed: _toggleTimer,
          label: _isRunning ? 'Pause' : 'Start',
          isPrimary: true,
        ),
        _buildControlButton(
          icon: Icons.add,
          onPressed: () => _adjustTime(10),
          label: '+10s',
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String label,
    bool isPrimary = false,
  }) {
    return Column(
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: ElevatedButton(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: isPrimary
                  ? AppTheme.primaryColor
                  : AppTheme.cardColor,
              foregroundColor: Colors.white,
              shape: const CircleBorder(),
              padding: EdgeInsets.zero,
            ),
            child: Icon(icon, size: 24),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(color: AppTheme.textSecondary, fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _skipSet,
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppTheme.textSecondary),
              foregroundColor: AppTheme.textSecondary,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Skip Set'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _completeSet,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Complete Set'),
          ),
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _toggleTimer() {
    setState(() {
      _isRunning = !_isRunning;
    });

    if (_isRunning) {
      _startTimer();
    } else {
      _animationController.stop();
    }
  }

  void _startTimer() {
    _animationController.forward().then((_) {
      if (_isResting) {
        setState(() {
          _isResting = false;
          _remainingTime = 45; // Exercise time
        });
        _resetTimer();
      } else {
        _completeSet();
      }
    });
  }

  void _adjustTime(int seconds) {
    setState(() {
      _remainingTime = (_remainingTime + seconds).clamp(10, 300);
      _restTime = _remainingTime;
    });
    _resetTimer();
  }

  void _resetTimer() {
    _animationController.reset();
    _animationController.duration = Duration(seconds: _remainingTime);
    _animation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );
  }

  void _skipSet() {
    if (_currentSet < _totalSets) {
      setState(() {
        _currentSet++;
        _isResting = true;
        _remainingTime = _restTime;
        _isRunning = false;
      });
      _resetTimer();
    } else {
      _finishWorkout();
    }
  }

  void _completeSet() {
    if (_currentSet < _totalSets) {
      setState(() {
        _currentSet++;
        _isResting = true;
        _remainingTime = _restTime;
        _isRunning = false;
      });
      _resetTimer();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Set ${_currentSet - 1} completed! Rest time started.'),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
    } else {
      _finishWorkout();
    }
  }

  void _finishWorkout() {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (dialogContext) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Workout Complete!',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Great job! You\'ve completed all sets.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Close the dialog first
              Navigator.of(dialogContext).pop();
              // Then navigate back to the previous screen with a delay to ensure proper navigation
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              });
            },
            child: Text(
              'Finish',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }
}
