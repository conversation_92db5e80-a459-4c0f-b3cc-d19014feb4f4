class AIWorkoutPlan {
  final String id;
  final String name;
  final String description;
  final int totalDays;
  final int estimatedCaloriesPerDay;
  final String difficultyLevel;
  final List<String> targetGoals;
  final List<DailyWorkout> dailyWorkouts;
  final List<String> aiInsights;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const AIWorkoutPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.totalDays,
    required this.estimatedCaloriesPerDay,
    required this.difficultyLevel,
    required this.targetGoals,
    required this.dailyWorkouts,
    required this.aiInsights,
    required this.createdAt,
    this.updatedAt,
  });

  factory AIWorkoutPlan.fromJson(Map<String, dynamic> json) {
    return AIWorkoutPlan(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      totalDays: json['total_days'] as int,
      estimatedCaloriesPerDay: json['estimated_calories_per_day'] as int,
      difficultyLevel: json['difficulty_level'] as String,
      targetGoals: List<String>.from(json['target_goals'] as List),
      dailyWorkouts: (json['daily_workouts'] as List)
          .map((workout) => DailyWorkout.fromJson(workout))
          .toList(),
      aiInsights: List<String>.from(json['ai_insights'] as List),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'total_days': totalDays,
      'estimated_calories_per_day': estimatedCaloriesPerDay,
      'difficulty_level': difficultyLevel,
      'target_goals': targetGoals,
      'daily_workouts': dailyWorkouts.map((workout) => workout.toJson()).toList(),
      'ai_insights': aiInsights,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  int get totalEstimatedCalories {
    return dailyWorkouts.fold(0, (sum, workout) => sum + workout.estimatedCalories);
  }

  int get totalEstimatedDuration {
    return dailyWorkouts.fold(0, (sum, workout) => sum + workout.estimatedDuration);
  }

  int get totalExercises {
    return dailyWorkouts.fold(0, (sum, workout) => sum + workout.exercises.length);
  }
}

class DailyWorkout {
  final int day;
  final String name;
  final String description;
  final List<AISelectedExercise> exercises;
  final int estimatedDuration; // in minutes
  final int estimatedCalories;
  final bool isCompleted;
  final DateTime? completedAt;

  const DailyWorkout({
    required this.day,
    required this.name,
    required this.description,
    required this.exercises,
    required this.estimatedDuration,
    required this.estimatedCalories,
    this.isCompleted = false,
    this.completedAt,
  });

  factory DailyWorkout.fromJson(Map<String, dynamic> json) {
    return DailyWorkout(
      day: json['day'] as int,
      name: json['name'] as String,
      description: json['description'] as String,
      exercises: (json['exercises'] as List)
          .map((exercise) => AISelectedExercise.fromJson(exercise))
          .toList(),
      estimatedDuration: json['estimated_duration'] as int,
      estimatedCalories: json['estimated_calories'] as int,
      isCompleted: json['is_completed'] ?? false,
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at'] as String) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'name': name,
      'description': description,
      'exercises': exercises.map((exercise) => exercise.toJson()).toList(),
      'estimated_duration': estimatedDuration,
      'estimated_calories': estimatedCalories,
      'is_completed': isCompleted,
      'completed_at': completedAt?.toIso8601String(),
    };
  }

  DailyWorkout copyWith({
    int? day,
    String? name,
    String? description,
    List<AISelectedExercise>? exercises,
    int? estimatedDuration,
    int? estimatedCalories,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return DailyWorkout(
      day: day ?? this.day,
      name: name ?? this.name,
      description: description ?? this.description,
      exercises: exercises ?? this.exercises,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      estimatedCalories: estimatedCalories ?? this.estimatedCalories,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}

class AISelectedExercise {
  final String exerciseId;
  final String name;
  final String videoUrl;
  final int duration; // in minutes
  final int sets;
  final int reps;
  final double estimatedCalories;
  final String aiReason; // Why AI selected this exercise
  final Map<String, dynamic>? aiMetadata; // Additional AI data
  final bool isCompleted;
  final int? actualSets;
  final int? actualReps;
  final double? actualCaloriesBurned;

  const AISelectedExercise({
    required this.exerciseId,
    required this.name,
    required this.videoUrl,
    required this.duration,
    required this.sets,
    required this.reps,
    required this.estimatedCalories,
    required this.aiReason,
    this.aiMetadata,
    this.isCompleted = false,
    this.actualSets,
    this.actualReps,
    this.actualCaloriesBurned,
  });

  factory AISelectedExercise.fromJson(Map<String, dynamic> json) {
    return AISelectedExercise(
      exerciseId: json['exercise_id'] as String,
      name: json['name'] as String,
      videoUrl: json['video_url'] as String,
      duration: json['duration'] as int,
      sets: json['sets'] as int,
      reps: json['reps'] as int,
      estimatedCalories: (json['estimated_calories'] as num).toDouble(),
      aiReason: json['ai_reason'] as String,
      aiMetadata: json['ai_metadata'] as Map<String, dynamic>?,
      isCompleted: json['is_completed'] ?? false,
      actualSets: json['actual_sets'] as int?,
      actualReps: json['actual_reps'] as int?,
      actualCaloriesBurned: json['actual_calories_burned']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'exercise_id': exerciseId,
      'name': name,
      'video_url': videoUrl,
      'duration': duration,
      'sets': sets,
      'reps': reps,
      'estimated_calories': estimatedCalories,
      'ai_reason': aiReason,
      'ai_metadata': aiMetadata,
      'is_completed': isCompleted,
      'actual_sets': actualSets,
      'actual_reps': actualReps,
      'actual_calories_burned': actualCaloriesBurned,
    };
  }

  AISelectedExercise copyWith({
    String? exerciseId,
    String? name,
    String? videoUrl,
    int? duration,
    int? sets,
    int? reps,
    double? estimatedCalories,
    String? aiReason,
    Map<String, dynamic>? aiMetadata,
    bool? isCompleted,
    int? actualSets,
    int? actualReps,
    double? actualCaloriesBurned,
  }) {
    return AISelectedExercise(
      exerciseId: exerciseId ?? this.exerciseId,
      name: name ?? this.name,
      videoUrl: videoUrl ?? this.videoUrl,
      duration: duration ?? this.duration,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      estimatedCalories: estimatedCalories ?? this.estimatedCalories,
      aiReason: aiReason ?? this.aiReason,
      aiMetadata: aiMetadata ?? this.aiMetadata,
      isCompleted: isCompleted ?? this.isCompleted,
      actualSets: actualSets ?? this.actualSets,
      actualReps: actualReps ?? this.actualReps,
      actualCaloriesBurned: actualCaloriesBurned ?? this.actualCaloriesBurned,
    );
  }

  double get calorieEfficiency {
    if (actualCaloriesBurned == null || estimatedCalories == 0) return 1.0;
    return actualCaloriesBurned! / estimatedCalories;
  }

  double get performanceScore {
    if (!isCompleted || actualSets == null || actualReps == null) return 0.0;
    
    final setsScore = actualSets! / sets;
    final repsScore = actualReps! / reps;
    
    return (setsScore + repsScore) / 2;
  }
}
