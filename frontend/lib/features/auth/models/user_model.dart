class UserModel {
  final String id;
  final String email;
  final String name;
  final String role;
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  // Profile information
  final int? age;
  final String? gender;
  final double? height; // in cm
  final double? weight; // in kg
  final String? fitnessLevel;
  final List<String>? fitnessGoals;
  final List<String>? availableEquipment;
  final int? workoutFrequency; // times per week
  final String? profileImageUrl;
  
  // Preferences
  final bool? notificationsEnabled;
  final String? preferredUnits; // metric/imperial
  final String? timezone;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.createdAt,
    this.updatedAt,
    this.age,
    this.gender,
    this.height,
    this.weight,
    this.fitnessLevel,
    this.fitnessGoals,
    this.availableEquipment,
    this.workoutFrequency,
    this.profileImageUrl,
    this.notificationsEnabled,
    this.preferredUnits,
    this.timezone,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      role: json['role'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String) 
          : null,
      age: json['age'] as int?,
      gender: json['gender'] as String?,
      height: json['height']?.toDouble(),
      weight: json['weight']?.toDouble(),
      fitnessLevel: json['fitness_level'] as String?,
      fitnessGoals: json['fitness_goals'] != null 
          ? List<String>.from(json['fitness_goals'] as List) 
          : null,
      availableEquipment: json['available_equipment'] != null 
          ? List<String>.from(json['available_equipment'] as List) 
          : null,
      workoutFrequency: json['workout_frequency'] as int?,
      profileImageUrl: json['profile_image_url'] as String?,
      notificationsEnabled: json['notifications_enabled'] as bool?,
      preferredUnits: json['preferred_units'] as String?,
      timezone: json['timezone'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'fitness_level': fitnessLevel,
      'fitness_goals': fitnessGoals,
      'available_equipment': availableEquipment,
      'workout_frequency': workoutFrequency,
      'profile_image_url': profileImageUrl,
      'notifications_enabled': notificationsEnabled,
      'preferred_units': preferredUnits,
      'timezone': timezone,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? age,
    String? gender,
    double? height,
    double? weight,
    String? fitnessLevel,
    List<String>? fitnessGoals,
    List<String>? availableEquipment,
    int? workoutFrequency,
    String? profileImageUrl,
    bool? notificationsEnabled,
    String? preferredUnits,
    String? timezone,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      fitnessLevel: fitnessLevel ?? this.fitnessLevel,
      fitnessGoals: fitnessGoals ?? this.fitnessGoals,
      availableEquipment: availableEquipment ?? this.availableEquipment,
      workoutFrequency: workoutFrequency ?? this.workoutFrequency,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      preferredUnits: preferredUnits ?? this.preferredUnits,
      timezone: timezone ?? this.timezone,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.role == role;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        email.hashCode ^
        name.hashCode ^
        role.hashCode;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, name: $name, role: $role)';
  }

  // Convenience getters
  bool get isMember => role == 'member';
  bool get isTrainer => role == 'trainer';
  bool get isAdmin => role == 'admin';
  
  bool get hasCompleteProfile {
    return age != null &&
        gender != null &&
        height != null &&
        weight != null &&
        fitnessLevel != null &&
        fitnessGoals != null &&
        fitnessGoals!.isNotEmpty &&
        availableEquipment != null &&
        availableEquipment!.isNotEmpty &&
        workoutFrequency != null;
  }

  double? get bmi {
    if (height == null || weight == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }
}
