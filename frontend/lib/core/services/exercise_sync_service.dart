import 'package:shared_preferences/shared_preferences.dart';
import '../../features/exercise/models/exercise_model.dart';
import 'google_drive_service.dart';
import 'mock_api_service.dart';

/// Service for syncing exercises from Google Drive to local database
class ExerciseSyncService {
  static const String _syncStatusKey = 'exercise_sync_status';
  static const String _lastSyncKey = 'exercise_last_sync';
  static const String _totalExercisesKey = 'exercise_total_count';

  /// Sync exercises from Google Drive
  static Future<ExerciseSyncResult> syncExercisesFromDrive({
    required String videoFolderId,
    required String pdfFileId,
    Function(String)? onProgress,
    Function(int, int)? onProgressCount,
  }) async {
    print('🔄 Starting exercise sync from Google Drive...');
    onProgress?.call('Initializing sync...');

    try {
      final startTime = DateTime.now();

      // Step 1: Get video files from Google Drive
      onProgress?.call('Fetching video files from Google Drive...');
      final videoFiles = await GoogleDriveService.getVideoFilesFromFolder(
        videoFolderId,
      );

      print('📹 Found ${videoFiles.length} video files');
      onProgressCount?.call(0, videoFiles.length);

      // Step 2: Get exercise descriptions from PDF
      onProgress?.call('Loading exercise descriptions...');
      final descriptions =
          await GoogleDriveService.getExerciseDescriptionsFromPdf(pdfFileId);

      print('📄 Loaded ${descriptions.length} exercise descriptions');

      // Step 3: Process each video file
      final exercises = <Exercise>[];
      int processedCount = 0;

      for (final videoFile in videoFiles) {
        try {
          onProgress?.call('Processing ${videoFile.name}...');

          // Parse exercise metadata from filename
          final metadata = GoogleDriveService.parseExerciseFromFilename(
            videoFile.name,
          );

          // Find matching description
          final descriptionKey = metadata.name.toLowerCase().replaceAll(
            ' ',
            '_',
          );
          final description = descriptions[descriptionKey];

          // Create exercise model
          final exercise = _createExerciseFromDriveData(
            videoFile,
            metadata,
            description,
          );

          exercises.add(exercise);
          processedCount++;

          onProgressCount?.call(processedCount, videoFiles.length);

          print('✅ Processed: ${metadata.name}');
        } catch (e) {
          print('❌ Error processing ${videoFile.name}: $e');
          // Continue with other files
        }
      }

      // Step 4: Save exercises to local database
      onProgress?.call('Saving exercises to database...');
      await _saveExercisesToDatabase(exercises);

      // Step 5: Update sync status
      await _updateSyncStatus(exercises.length);

      final duration = DateTime.now().difference(startTime);

      print(
        '✅ Sync completed: ${exercises.length} exercises in ${duration.inSeconds}s',
      );

      return ExerciseSyncResult(
        success: true,
        totalProcessed: exercises.length,
        totalVideos: videoFiles.length,
        duration: duration,
        exercises: exercises,
      );
    } catch (e) {
      print('❌ Sync failed: $e');

      return ExerciseSyncResult(
        success: false,
        error: e.toString(),
        totalProcessed: 0,
        totalVideos: 0,
        duration: Duration.zero,
        exercises: [],
      );
    }
  }

  /// Create Exercise model from Google Drive data
  static Exercise _createExerciseFromDriveData(
    DriveVideoFile videoFile,
    ExerciseMetadata metadata,
    ExerciseDescription? description,
  ) {
    final now = DateTime.now();

    return Exercise(
      id: videoFile.id.hashCode, // Generate ID from Drive ID
      name: metadata.name,
      slug: _generateSlug(metadata.name),
      description:
          description?.description ??
          'A ${metadata.level.toLowerCase()} level ${metadata.exerciseType.toLowerCase()} exercise targeting ${metadata.bodyPart.toLowerCase()} muscles.',
      instructions:
          description?.instructions ??
          [
            'Follow the video demonstration',
            'Maintain proper form throughout',
            'Control your breathing',
            'Stop if you feel any pain',
          ],
      tips:
          description?.tips ??
          [
            'Start with lighter intensity if you\'re a beginner',
            'Focus on form over speed',
            'Stay hydrated during exercise',
          ],
      muscleGroups:
          description?.muscleGroups ??
          _getMuscleGroupsForBodyPart(metadata.bodyPart),
      equipment:
          description?.equipment ?? _getEquipmentForExercise(metadata.name),
      difficulty: metadata.level.toLowerCase(),
      exerciseType: metadata.exerciseType.toLowerCase(),
      category: metadata.bodyPart.toLowerCase(),
      durationMinutes: _estimateDuration(metadata.level),
      caloriesPerMinute: _estimateCaloriesPerMinute(
        metadata.level,
        metadata.bodyPart,
      ),
      videoUrl: null, // Use Drive video URL instead
      imageUrl: null, // Use Drive thumbnail instead
      isActive: true,
      createdBy: null,
      createdAt: now,
      updatedAt: now,
      isFavorite: false,
      driveVideoId: videoFile.id,
      originalFilename: videoFile.name,
      driveVideoUrl: videoFile.downloadUrl,
      driveThumbnailUrl: videoFile.thumbnailUrl,
    );
  }

  /// Generate URL-friendly slug from exercise name
  static String _generateSlug(String name) {
    return name
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '-');
  }

  /// Get muscle groups for body part
  static List<String> _getMuscleGroupsForBodyPart(String bodyPart) {
    switch (bodyPart.toLowerCase()) {
      case 'chest':
        return ['Chest', 'Triceps', 'Shoulders'];
      case 'back':
        return ['Back', 'Biceps', 'Rear Delts'];
      case 'legs':
        return ['Quadriceps', 'Hamstrings', 'Glutes', 'Calves'];
      case 'shoulders':
        return ['Shoulders', 'Triceps'];
      case 'arms':
        return ['Biceps', 'Triceps', 'Forearms'];
      case 'core':
      case 'abs':
        return ['Core', 'Abs', 'Obliques'];
      default:
        return ['Full Body'];
    }
  }

  /// Get equipment needed for exercise
  static List<String> _getEquipmentForExercise(String exerciseName) {
    final name = exerciseName.toLowerCase();

    if (name.contains('dumbbell')) {
      return ['Dumbbells'];
    } else if (name.contains('barbell')) {
      return ['Barbell', 'Weight Plates'];
    } else if (name.contains('pull') && name.contains('up')) {
      return ['Pull-up Bar'];
    } else if (name.contains('bench')) {
      return ['Bench'];
    } else if (name.contains('kettlebell')) {
      return ['Kettlebell'];
    } else if (name.contains('resistance') || name.contains('band')) {
      return ['Resistance Bands'];
    } else {
      return ['None']; // Bodyweight exercise
    }
  }

  /// Estimate exercise duration in minutes
  static int _estimateDuration(String level) {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 1; // 1 minute
      case 'intermediate':
        return 2; // 2 minutes
      case 'advanced':
        return 3; // 3 minutes
      default:
        return 1;
    }
  }

  /// Estimate calories burned per minute
  static double _estimateCaloriesPerMinute(String level, String bodyPart) {
    double baseCalories = 5.0;

    // Adjust for difficulty
    switch (level.toLowerCase()) {
      case 'beginner':
        baseCalories *= 0.8;
        break;
      case 'intermediate':
        baseCalories *= 1.0;
        break;
      case 'advanced':
        baseCalories *= 1.3;
        break;
    }

    // Adjust for body part (larger muscle groups burn more)
    switch (bodyPart.toLowerCase()) {
      case 'legs':
      case 'back':
        baseCalories *= 1.2;
        break;
      case 'chest':
      case 'shoulders':
        baseCalories *= 1.0;
        break;
      case 'arms':
        baseCalories *= 0.8;
        break;
      case 'core':
        baseCalories *= 0.9;
        break;
    }

    return baseCalories;
  }

  /// Save exercises to local database
  static Future<void> _saveExercisesToDatabase(List<Exercise> exercises) async {
    print('💾 Saving ${exercises.length} exercises to database...');

    // Save to mock API for persistence
    for (final exercise in exercises) {
      await MockApiService.saveExercise(exercise.toJson());
    }

    print('✅ Exercises saved to database');
  }

  /// Update sync status
  static Future<void> _updateSyncStatus(int exerciseCount) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());
    await prefs.setInt(_totalExercisesKey, exerciseCount);
    await prefs.setBool(_syncStatusKey, true);
  }

  /// Get last sync information
  static Future<SyncInfo> getSyncInfo() async {
    final prefs = await SharedPreferences.getInstance();

    final lastSyncString = prefs.getString(_lastSyncKey);
    final totalExercises = prefs.getInt(_totalExercisesKey) ?? 0;
    final hasSynced = prefs.getBool(_syncStatusKey) ?? false;

    return SyncInfo(
      lastSync: lastSyncString != null ? DateTime.parse(lastSyncString) : null,
      totalExercises: totalExercises,
      hasSynced: hasSynced,
    );
  }

  /// Clear sync data
  static Future<void> clearSyncData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_lastSyncKey);
    await prefs.remove(_totalExercisesKey);
    await prefs.remove(_syncStatusKey);
  }
}

/// Result of exercise sync operation
class ExerciseSyncResult {
  final bool success;
  final String? error;
  final int totalProcessed;
  final int totalVideos;
  final Duration duration;
  final List<Exercise> exercises;

  const ExerciseSyncResult({
    required this.success,
    this.error,
    required this.totalProcessed,
    required this.totalVideos,
    required this.duration,
    required this.exercises,
  });
}

/// Information about sync status
class SyncInfo {
  final DateTime? lastSync;
  final int totalExercises;
  final bool hasSynced;

  const SyncInfo({
    this.lastSync,
    required this.totalExercises,
    required this.hasSynced,
  });
}
