class AppConstants {
  // App Info
  static const String appName = 'WibeFit';
  static const String appVersion = '1.0.0';

  // Default Login Credentials (for development)
  static const String defaultEmail = '<EMAIL>';
  static const String defaultPassword = 'wibefit123';
  static const String trainerEmail = '<EMAIL>';
  static const String trainerPassword = 'trainer123';
  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'admin123';

  // Google Sign-In Configuration
  // TODO: Replace with your actual Google Web Client ID
  static const String googleWebClientId =
      'YOUR_GOOGLE_WEB_CLIENT_ID.apps.googleusercontent.com';

  // API Endpoints - Backend is now running!
  // Note: Use ApiConfig.baseUrl for actual API calls (platform-aware)
  static const String baseUrl = 'http://localhost:8000'; // Fallback only
  static const String authEndpoint = '/api/auth';
  static const String workoutEndpoint = '/api/workouts';
  static const String exerciseEndpoint = '/api/exercises';
  static const String userEndpoint = '/api/users';
  static const String analyticsEndpoint = '/api/analytics';

  // Asset Paths
  static const String logoPath = 'assets/logos/';
  static const String imagePath = 'assets/images/';
  static const String videoPath = 'assets/videos/';
  static const String animationPath = 'assets/animations/';
  static const String iconPath = 'assets/icons/';

  // Workout Constants
  static const List<String> muscleGroups = [
    'Chest',
    'Back',
    'Shoulders',
    'Biceps',
    'Triceps',
    'Legs',
    'Core',
    'Cardio',
  ];

  static const List<String> equipmentTypes = [
    'Bodyweight',
    'Barbell',
    'Dumbbell',
    'Kettlebell',
    'Cable',
    'Machine',
    'Resistance Band',
  ];

  static const List<String> workoutDifficulty = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert',
  ];

  // User Roles
  static const String roleVisitor = 'visitor';
  static const String roleMember = 'member';
  static const String roleTrainer = 'trainer';
  static const String roleGymAdmin = 'gym_admin';

  // Workout Plans
  static const List<Map<String, dynamic>> sampleWorkoutPlans = [
    {
      'id': '1',
      'name': 'Weight Loss Plan',
      'description':
          'This plan focuses on maximizing calorie burn and boosting metabolism. Achieve sustainable weight loss while building healthy habits!',
      'duration': '8 weeks',
      'difficulty': 'Intermediate',
      'workoutsPerWeek': 4,
      'targetMuscles': {
        'Day 1': ['Back', 'Chest', 'Biceps', 'Triceps', 'Cardio'],
        'Day 2': ['Legs', 'Shoulders', 'Core'],
        'Day 3': ['Chest', 'Back', 'Triceps', 'Core', 'Cardio'],
        'Day 4': ['Legs', 'Shoulders', 'Core'],
      },
      'estimatedCalories': 789,
      'exercises': 7,
      'sets': 21,
    },
    {
      'id': '2',
      'name': 'Muscle Building Plan',
      'description':
          'Build lean muscle mass with progressive overload training. Perfect for those looking to gain strength and size.',
      'duration': '12 weeks',
      'difficulty': 'Advanced',
      'workoutsPerWeek': 5,
      'targetMuscles': {
        'Day 1': ['Chest', 'Triceps'],
        'Day 2': ['Back', 'Biceps'],
        'Day 3': ['Legs'],
        'Day 4': ['Shoulders', 'Core'],
        'Day 5': ['Full Body'],
      },
      'estimatedCalories': 650,
      'exercises': 8,
      'sets': 24,
    },
  ];

  // Sample Exercises
  static const List<Map<String, dynamic>> sampleExercises = [
    {
      'id': '1',
      'name': 'Bench Press',
      'muscleGroup': 'Chest',
      'equipment': 'Barbell',
      'difficulty': 'Intermediate',
      'instructions':
          'Lie on bench, grip bar wider than shoulders, lower to chest, press up.',
      'videoUrl': 'assets/videos/bench_press.mp4',
      'imageUrl': 'assets/images/bench_press.jpg',
    },
    {
      'id': '2',
      'name': 'Dumbbell Bench Press',
      'muscleGroup': 'Chest',
      'equipment': 'Dumbbell',
      'difficulty': 'Intermediate',
      'instructions': 'Lie on bench with dumbbells, press up and together.',
      'videoUrl': 'assets/videos/dumbbell_bench_press.mp4',
      'imageUrl': 'assets/images/dumbbell_bench_press.jpg',
    },
    {
      'id': '3',
      'name': 'Push Up',
      'muscleGroup': 'Chest',
      'equipment': 'Bodyweight',
      'difficulty': 'Beginner',
      'instructions':
          'Start in plank position, lower body to ground, push back up.',
      'videoUrl': 'assets/videos/push_up.mp4',
      'imageUrl': 'assets/images/push_up.jpg',
    },
    {
      'id': '4',
      'name': 'Dumbbell Pullover',
      'muscleGroup': 'Chest',
      'equipment': 'Dumbbell',
      'difficulty': 'Intermediate',
      'instructions':
          'Lie on bench, hold dumbbell overhead, lower behind head.',
      'videoUrl': 'assets/videos/dumbbell_pullover.mp4',
      'imageUrl': 'assets/images/dumbbell_pullover.jpg',
    },
    {
      'id': '5',
      'name': 'Pec Deck Fly',
      'muscleGroup': 'Chest',
      'equipment': 'Machine',
      'difficulty': 'Beginner',
      'instructions': 'Sit on machine, bring arms together in front of chest.',
      'videoUrl': 'assets/videos/pec_deck_fly.mp4',
      'imageUrl': 'assets/images/pec_deck_fly.jpg',
    },
  ];

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
}
