import 'package:flutter/material.dart';
import '../../../core/services/exercise_sync_service.dart';
import '../../../core/theme/app_theme.dart';

class ExerciseSyncScreen extends StatefulWidget {
  const ExerciseSyncScreen({super.key});

  @override
  State<ExerciseSyncScreen> createState() => _ExerciseSyncScreenState();
}

class _ExerciseSyncScreenState extends State<ExerciseSyncScreen> {
  final _videoFolderController = TextEditingController();
  final _pdfFileController = TextEditingController();
  
  bool _isLoading = false;
  String _statusMessage = '';
  double _progress = 0.0;
  int _processedCount = 0;
  int _totalCount = 0;
  ExerciseSyncResult? _lastResult;
  SyncInfo? _syncInfo;

  @override
  void initState() {
    super.initState();
    _loadSyncInfo();
  }

  @override
  void dispose() {
    _videoFolderController.dispose();
    _pdfFileController.dispose();
    super.dispose();
  }

  Future<void> _loadSyncInfo() async {
    final syncInfo = await ExerciseSyncService.getSyncInfo();
    setState(() {
      _syncInfo = syncInfo;
    });
  }

  Future<void> _startSync() async {
    if (_videoFolderController.text.isEmpty || _pdfFileController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please provide both Google Drive folder ID and PDF file ID'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _progress = 0.0;
      _processedCount = 0;
      _totalCount = 0;
      _statusMessage = 'Starting sync...';
    });

    try {
      final result = await ExerciseSyncService.syncExercisesFromDrive(
        videoFolderId: _videoFolderController.text.trim(),
        pdfFileId: _pdfFileController.text.trim(),
        onProgress: (message) {
          setState(() {
            _statusMessage = message;
          });
        },
        onProgressCount: (processed, total) {
          setState(() {
            _processedCount = processed;
            _totalCount = total;
            _progress = total > 0 ? processed / total : 0.0;
          });
        },
      );

      setState(() {
        _lastResult = result;
        _isLoading = false;
        _statusMessage = result.success 
            ? 'Sync completed successfully!' 
            : 'Sync failed: ${result.error}';
      });

      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '✅ Successfully synced ${result.totalProcessed} exercises!',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        
        // Reload sync info
        await _loadSyncInfo();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Sync failed: ${result.error}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Error: $e';
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Sync error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        foregroundColor: Colors.black,
        title: const Text(
          'Exercise Data Sync',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 24),
            _buildSyncForm(),
            const SizedBox(height: 24),
            if (_isLoading) _buildProgressCard(),
            if (_lastResult != null) _buildResultCard(),
            if (_syncInfo != null) _buildSyncInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue, size: 24),
              SizedBox(width: 8),
              Text(
                'Google Drive Exercise Sync',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'This tool syncs exercise videos from your Google Drive folder and descriptions from a PDF file.',
            style: TextStyle(fontSize: 14, color: Colors.black87),
          ),
          const SizedBox(height: 8),
          const Text(
            'Requirements:',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.black),
          ),
          const Text(
            '• Google Drive folder with exercise videos (52GB supported)',
            style: TextStyle(fontSize: 12, color: Colors.black87),
          ),
          const Text(
            '• PDF file with exercise descriptions',
            style: TextStyle(fontSize: 12, color: Colors.black87),
          ),
          const Text(
            '• Video filenames: "ExerciseName_Level_BodyPart_Type.mp4"',
            style: TextStyle(fontSize: 12, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sync Configuration',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          
          // Video Folder ID
          const Text(
            'Google Drive Video Folder ID',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.black),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _videoFolderController,
            decoration: InputDecoration(
              hintText: 'Enter Google Drive folder ID containing videos',
              hintStyle: const TextStyle(color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.orange),
              ),
            ),
            enabled: !_isLoading,
          ),
          const SizedBox(height: 16),
          
          // PDF File ID
          const Text(
            'Exercise Descriptions PDF File ID',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.black),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _pdfFileController,
            decoration: InputDecoration(
              hintText: 'Enter Google Drive PDF file ID with descriptions',
              hintStyle: const TextStyle(color: Colors.grey),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.orange),
              ),
            ),
            enabled: !_isLoading,
          ),
          const SizedBox(height: 24),
          
          // Sync Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _startSync,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Syncing...'),
                      ],
                    )
                  : const Text(
                      'Start Exercise Sync',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sync Progress',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          
          LinearProgressIndicator(
            value: _progress,
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
          const SizedBox(height: 8),
          
          Text(
            '$_processedCount / $_totalCount exercises processed',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
          ),
          const SizedBox(height: 8),
          
          Text(
            _statusMessage,
            style: const TextStyle(fontSize: 14, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildResultCard() {
    final result = _lastResult!;
    final isSuccess = result.success;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSuccess 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSuccess 
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSuccess ? Icons.check_circle : Icons.error,
                color: isSuccess ? Colors.green : Colors.red,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isSuccess ? 'Sync Successful' : 'Sync Failed',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          if (isSuccess) ...[
            Text('✅ Processed: ${result.totalProcessed} exercises'),
            Text('📹 Total videos: ${result.totalVideos}'),
            Text('⏱️ Duration: ${result.duration.inSeconds} seconds'),
          ] else ...[
            Text('❌ Error: ${result.error}'),
          ],
        ],
      ),
    );
  }

  Widget _buildSyncInfoCard() {
    final info = _syncInfo!;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Sync Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          
          if (info.hasSynced) ...[
            Text('📊 Total exercises: ${info.totalExercises}'),
            if (info.lastSync != null)
              Text('🕒 Last sync: ${_formatDateTime(info.lastSync!)}'),
          ] else ...[
            const Text('ℹ️ No sync performed yet'),
          ],
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
