"""
User Pydantic schemas for request/response validation
"""

from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class FitnessLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class ActivityLevel(str, Enum):
    SEDENTARY = "sedentary"
    LIGHTLY_ACTIVE = "lightly_active"
    MODERATELY_ACTIVE = "moderately_active"
    VERY_ACTIVE = "very_active"


class UserRole(str, Enum):
    MEMBER = "member"
    TRAINER = "trainer"
    ADMIN = "admin"


# Base schemas
class UserBase(BaseModel):
    email: EmailStr
    full_name: str = Field(..., min_length=1, max_length=255)
    username: Optional[str] = Field(None, max_length=100)
    bio: Optional[str] = None
    phone_number: Optional[str] = None
    timezone: str = "UTC"
    language: str = "en"


class UserCreate(UserBase):
    password: Optional[str] = Field(None, min_length=8)  # Optional for Firebase users
    role: UserRole = UserRole.MEMBER


class UserUpdate(BaseModel):
    full_name: Optional[str] = Field(None, min_length=1, max_length=255)
    username: Optional[str] = Field(None, max_length=100)
    bio: Optional[str] = None
    phone_number: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    profile_picture: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserResponse(UserBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    role: UserRole
    is_active: bool
    is_verified: bool
    profile_picture: Optional[str] = None
    date_of_birth: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


# User Profile schemas
class UserProfileBase(BaseModel):
    age: Optional[int] = Field(None, ge=13, le=120)
    height: Optional[float] = Field(None, gt=0, le=300)  # cm
    current_weight: Optional[float] = Field(None, gt=0, le=1000)  # kg
    target_weight: Optional[float] = Field(None, gt=0, le=1000)  # kg
    fitness_level: Optional[FitnessLevel] = None
    activity_level: Optional[ActivityLevel] = None
    primary_goals: Optional[List[str]] = None
    medical_conditions: Optional[str] = None
    dietary_restrictions: Optional[List[str]] = None
    preferred_workout_duration: Optional[int] = Field(None, ge=5, le=300)  # minutes
    preferred_workout_days: Optional[List[str]] = None
    equipment_access: Optional[List[str]] = None
    gender: Optional[str] = None


class UserProfileCreate(UserProfileBase):
    pass


class UserProfileUpdate(UserProfileBase):
    pass


class UserProfileResponse(UserProfileBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


# User Measurement schemas
class UserMeasurementBase(BaseModel):
    measurement_type: str = Field(..., max_length=50)
    value: float = Field(..., gt=0)
    unit: str = Field(..., max_length=20)
    notes: Optional[str] = None


class UserMeasurementCreate(UserMeasurementBase):
    pass


class UserMeasurementResponse(UserMeasurementBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    measured_at: datetime
    created_at: datetime


# User Goal schemas
class GoalType(str, Enum):
    WEIGHT_LOSS = "weight_loss"
    MUSCLE_GAIN = "muscle_gain"
    ENDURANCE = "endurance"
    STRENGTH = "strength"
    FLEXIBILITY = "flexibility"
    GENERAL_FITNESS = "general_fitness"


class UserGoalBase(BaseModel):
    goal_type: GoalType
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    target_value: Optional[float] = Field(None, gt=0)
    unit: Optional[str] = Field(None, max_length=20)
    target_date: Optional[datetime] = None


class UserGoalCreate(UserGoalBase):
    pass


class UserGoalUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    target_value: Optional[float] = Field(None, gt=0)
    current_value: Optional[float] = Field(None, ge=0)
    unit: Optional[str] = Field(None, max_length=20)
    target_date: Optional[datetime] = None
    is_completed: Optional[bool] = None


class UserGoalResponse(UserGoalBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    current_value: float
    is_completed: bool
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


# Legacy schemas for compatibility
class User(UserResponse):
    pass


class UserProfile(UserProfileResponse):
    pass


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    email: Optional[str] = None


class UserStats(BaseModel):
    total_workouts: int = 0
    total_calories_burned: float = 0
    total_workout_time_minutes: float = 0
    current_streak_days: int = 0
    longest_streak_days: int = 0
    favorite_exercise_category: Optional[str] = None
    average_workout_duration: Optional[float] = None
    achievements_count: int = 0
    friends_count: int = 0


class UserAnalytics(BaseModel):
    weekly_workouts: List[int] = []
    monthly_progress: Dict[str, Any] = {}
    goal_progress: List[Dict[str, Any]] = []
    recent_achievements: List[Dict[str, Any]] = []
