import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/constants/app_constants.dart';
import '../models/workout_models.dart';
import '../../exercise/models/exercise_model.dart';
import 'workout_execution_screen.dart';

class CustomWorkoutBuilderScreen extends StatefulWidget {
  const CustomWorkoutBuilderScreen({super.key});

  @override
  State<CustomWorkoutBuilderScreen> createState() =>
      _CustomWorkoutBuilderScreenState();
}

class _CustomWorkoutBuilderScreenState
    extends State<CustomWorkoutBuilderScreen> {
  final TextEditingController _workoutNameController = TextEditingController();
  final List<Map<String, dynamic>> _selectedExercises = [];
  String _selectedDifficulty = 'Beginner';
  String _selectedDuration = '30 min';
  String _selectedTarget = 'Full Body';

  final List<String> _difficulties = ['Beginner', 'Intermediate', 'Advanced'];
  final List<String> _durations = [
    '15 min',
    '30 min',
    '45 min',
    '60 min',
    '90 min',
  ];
  final List<String> _targets = [
    'Full Body',
    'Upper Body',
    'Lower Body',
    'Core',
    'Cardio',
  ];

  @override
  void dispose() {
    _workoutNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Custom Workout',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _selectedExercises.isNotEmpty ? _saveWorkout : null,
            child: Text(
              'Save',
              style: TextStyle(
                color: _selectedExercises.isNotEmpty
                    ? AppTheme.primaryColor
                    : AppTheme.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWorkoutDetails(),
                  const SizedBox(height: 24),
                  _buildWorkoutSettings(),
                  const SizedBox(height: 24),
                  _buildSelectedExercises(),
                  const SizedBox(height: 24),
                  _buildExerciseLibrary(),
                ],
              ),
            ),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildWorkoutDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Details',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _workoutNameController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              labelText: 'Workout Name',
              labelStyle: TextStyle(color: AppTheme.textSecondary),
              hintText: 'Enter workout name',
              hintStyle: TextStyle(color: AppTheme.textSecondary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.textSecondary),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.textSecondary),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutSettings() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Workout Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildSettingRow('Difficulty', _selectedDifficulty, _difficulties, (
            value,
          ) {
            setState(() => _selectedDifficulty = value);
          }),
          const SizedBox(height: 12),
          _buildSettingRow('Duration', _selectedDuration, _durations, (value) {
            setState(() => _selectedDuration = value);
          }),
          const SizedBox(height: 12),
          _buildSettingRow('Target', _selectedTarget, _targets, (value) {
            setState(() => _selectedTarget = value);
          }),
        ],
      ),
    );
  }

  Widget _buildSettingRow(
    String label,
    String value,
    List<String> options,
    Function(String) onChanged,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(color: AppTheme.textSecondary, fontSize: 14),
        ),
        GestureDetector(
          onTap: () => _showOptionsDialog(label, options, value, onChanged),
          behavior: HitTestBehavior.opaque, // Ensures entire area is tappable
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ), // Increased padding
            constraints: const BoxConstraints(
              minWidth: 88, // Minimum touch target width
              minHeight: 48, // Minimum touch target height
            ),
            decoration: BoxDecoration(
              color: AppTheme.backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.primaryColor),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedExercises() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Selected Exercises',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${_selectedExercises.length} exercises',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_selectedExercises.isEmpty)
            Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(
                    Icons.fitness_center,
                    size: 48,
                    color: AppTheme.textSecondary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No exercises selected',
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Add exercises from the library below',
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )
          else
            ReorderableListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _selectedExercises.length,
              onReorder: (oldIndex, newIndex) {
                setState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final item = _selectedExercises.removeAt(oldIndex);
                  _selectedExercises.insert(newIndex, item);
                });
              },
              itemBuilder: (context, index) {
                final exercise = _selectedExercises[index];
                return _buildSelectedExerciseItem(exercise, index);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildSelectedExerciseItem(Map<String, dynamic> exercise, int index) {
    return Container(
      key: ValueKey(exercise['name']),
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.drag_handle, color: AppTheme.textSecondary, size: 16),
          const SizedBox(width: 8),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.fitness_center,
              color: AppTheme.primaryColor,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise['name'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  exercise['muscleGroup'],
                  style: TextStyle(color: AppTheme.textSecondary, fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeExercise(index),
            icon: Icon(
              Icons.remove_circle_outline,
              color: AppTheme.errorColor,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseLibrary() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exercise Library',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...AppConstants.sampleExercises.take(5).map((exercise) {
            final isSelected = _selectedExercises.any(
              (e) => e['name'] == exercise['name'],
            );
            return _buildLibraryExerciseItem(exercise, isSelected);
          }),
          const SizedBox(height: 12),
          Center(
            child: TextButton(
              onPressed: () => _showFullExerciseLibrary(),
              child: Text(
                'View All Exercises',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLibraryExerciseItem(
    Map<String, dynamic> exercise,
    bool isSelected,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSelected
            ? AppTheme.primaryColor.withValues(alpha: 0.1)
            : AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: isSelected ? Border.all(color: AppTheme.primaryColor) : null,
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.fitness_center,
              color: AppTheme.primaryColor,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise['name'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${exercise['muscleGroup']} • ${exercise['equipment']}',
                  style: TextStyle(color: AppTheme.textSecondary, fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => isSelected
                ? _removeExerciseByName(exercise['name'])
                : _addExercise(exercise),
            icon: Icon(
              isSelected ? Icons.remove_circle : Icons.add_circle,
              color: isSelected ? AppTheme.errorColor : AppTheme.primaryColor,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _previewWorkout(),
              icon: const Icon(Icons.visibility),
              label: const Text('Preview'),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.primaryColor),
                foregroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _selectedExercises.isNotEmpty ? _startWorkout : null,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Workout'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _selectedExercises.isNotEmpty
                    ? AppTheme.primaryColor
                    : AppTheme.textSecondary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showOptionsDialog(
    String title,
    List<String> options,
    String currentValue,
    Function(String) onChanged,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select $title',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ...options.map(
              (option) => ListTile(
                title: Text(
                  option,
                  style: const TextStyle(color: Colors.white),
                ),
                trailing: currentValue == option
                    ? Icon(Icons.check, color: AppTheme.primaryColor)
                    : null,
                onTap: () {
                  onChanged(option);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addExercise(Map<String, dynamic> exercise) {
    setState(() {
      _selectedExercises.add(exercise);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${exercise['name']} added to workout'),
        backgroundColor: AppTheme.primaryColor,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _removeExercise(int index) {
    final exercise = _selectedExercises[index];
    setState(() {
      _selectedExercises.removeAt(index);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${exercise['name']} removed from workout'),
        backgroundColor: AppTheme.errorColor,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _removeExerciseByName(String name) {
    setState(() {
      _selectedExercises.removeWhere((exercise) => exercise['name'] == name);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$name removed from workout'),
        backgroundColor: AppTheme.errorColor,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showFullExerciseLibrary() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExerciseSelectionScreen(
          selectedExercises: _selectedExercises,
          onExercisesSelected: (exercises) {
            setState(() {
              _selectedExercises.clear();
              _selectedExercises.addAll(exercises);
            });
          },
        ),
      ),
    );
  }

  void _previewWorkout() {
    if (_selectedExercises.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add exercises to preview workout'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Workout Preview',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Name: ${_workoutNameController.text.isEmpty ? 'Custom Workout' : _workoutNameController.text}',
              style: const TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Difficulty: $_selectedDifficulty',
              style: const TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Duration: $_selectedDuration',
              style: const TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Target: $_selectedTarget',
              style: const TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Exercises: ${_selectedExercises.length}',
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Close',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  void _startWorkout() {
    if (_selectedExercises.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add exercises to start workout'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // Create custom workout and navigate to execution
    final customWorkout = CustomWorkout(
      name: _workoutNameController.text.isEmpty
          ? 'Custom Workout'
          : _workoutNameController.text,
      difficulty: _selectedDifficulty,
      duration: _selectedDuration,
      target: _selectedTarget,
      exercises: _selectedExercises.map((exerciseData) {
        return CustomWorkoutExercise(
          exercise: Exercise.fromJson(exerciseData),
          sets: 3, // Default sets
          reps: 12, // Default reps
          restSeconds: 60, // Default rest
        );
      }).toList(),
      createdAt: DateTime.now(),
    );

    // Navigate to workout execution
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            WorkoutExecutionScreen(customWorkout: customWorkout),
      ),
    );
  }

  void _saveWorkout() {
    if (_selectedExercises.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add exercises to save workout'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (dialogContext) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Save Workout',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Workout saved successfully!',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Close the dialog first
              Navigator.of(dialogContext).pop();
              // Then navigate back to the previous screen with a delay to ensure proper navigation
              Future.delayed(const Duration(milliseconds: 100), () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              });
            },
            child: Text('OK', style: TextStyle(color: AppTheme.primaryColor)),
          ),
        ],
      ),
    );
  }
}

class ExerciseSelectionScreen extends StatefulWidget {
  final List<Map<String, dynamic>> selectedExercises;
  final Function(List<Map<String, dynamic>>) onExercisesSelected;

  const ExerciseSelectionScreen({
    super.key,
    required this.selectedExercises,
    required this.onExercisesSelected,
  });

  @override
  State<ExerciseSelectionScreen> createState() =>
      _ExerciseSelectionScreenState();
}

class _ExerciseSelectionScreenState extends State<ExerciseSelectionScreen> {
  late List<Map<String, dynamic>> _tempSelectedExercises;

  @override
  void initState() {
    super.initState();
    _tempSelectedExercises = List.from(widget.selectedExercises);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Select Exercises',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              widget.onExercisesSelected(_tempSelectedExercises);
              Navigator.pop(context);
            },
            child: Text(
              'Done (${_tempSelectedExercises.length})',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: AppConstants.sampleExercises.length,
        itemBuilder: (context, index) {
          final exercise = AppConstants.sampleExercises[index];
          final isSelected = _tempSelectedExercises.any(
            (e) => e['name'] == exercise['name'],
          );

          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: CheckboxListTile(
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _tempSelectedExercises.add(exercise);
                  } else {
                    _tempSelectedExercises.removeWhere(
                      (e) => e['name'] == exercise['name'],
                    );
                  }
                });
              },
              title: Text(
                exercise['name'],
                style: const TextStyle(color: Colors.white),
              ),
              subtitle: Text(
                '${exercise['muscleGroup']} • ${exercise['equipment']}',
                style: TextStyle(color: AppTheme.textSecondary),
              ),
              activeColor: AppTheme.primaryColor,
              checkColor: Colors.white,
              tileColor: AppTheme.cardColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        },
      ),
    );
  }
}
