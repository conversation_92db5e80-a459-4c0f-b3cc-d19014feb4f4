import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../shared/models/user_model.dart';
import '../../features/workout/models/ai_workout_plan.dart';
import '../config/api_config.dart';

class AIPlanService {
  static String get _baseUrl => '${ApiConfig.baseUrl}/api';

  /// Generate AI-powered workout plan based on user profile
  Future<AIWorkoutPlan> generatePersonalizedPlan(UserModel user) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/ai/generate-plan'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_profile': {
            'age': user.metadata?['age'],
            'gender': user.metadata?['gender'],
            'height': user.metadata?['height'],
            'weight': user.metadata?['weight'],
            'fitness_level': user.metadata?['fitnessLevel'],
            'fitness_goals': user.metadata?['fitnessGoals'],
            'available_equipment': user.metadata?['availableEquipment'],
            'workout_frequency': user.metadata?['workoutFrequency'],
          },
          'plan_duration_days': 4,
          'video_library_size': 1000,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AIWorkoutPlan.fromJson(data);
      } else {
        throw Exception('Failed to generate AI plan: ${response.statusCode}');
      }
    } catch (e) {
      print('AI Plan Service Error: $e');
      // Return mock AI-generated plan for development
      return _generateMockAIPlan(user);
    }
  }

  /// Calculate calories burned for specific exercise based on user profile
  Future<double> calculateCaloriesBurned({
    required String exerciseId,
    required String videoUrl,
    required int durationMinutes,
    required UserModel user,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/ai/calculate-calories'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'exercise_id': exerciseId,
          'video_url': videoUrl,
          'duration_minutes': durationMinutes,
          'user_profile': {
            'age': user.metadata?['age'],
            'gender': user.metadata?['gender'],
            'height': user.metadata?['height'],
            'weight': user.metadata?['weight'],
            'fitness_level': user.metadata?['fitnessLevel'],
          },
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['calories_burned'].toDouble();
      } else {
        throw Exception('Failed to calculate calories: ${response.statusCode}');
      }
    } catch (e) {
      print('Calorie Calculation Error: $e');
      // Return mock calculation for development
      return _calculateMockCalories(durationMinutes, user);
    }
  }

  /// Get AI analysis of workout performance
  Future<Map<String, dynamic>> getWorkoutAnalysis({
    required List<Map<String, dynamic>> completedExercises,
    required UserModel user,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/ai/analyze-workout'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'completed_exercises': completedExercises,
          'user_profile': {
            'age': user.metadata?['age'],
            'gender': user.metadata?['gender'],
            'height': user.metadata?['height'],
            'weight': user.metadata?['weight'],
            'fitness_level': user.metadata?['fitnessLevel'],
            'fitness_goals': user.metadata?['fitnessGoals'],
          },
        }),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get AI analysis: ${response.statusCode}');
      }
    } catch (e) {
      print('AI Analysis Error: $e');
      return _generateMockAnalysis(completedExercises, user);
    }
  }

  /// Mock AI plan generation for development
  AIWorkoutPlan _generateMockAIPlan(UserModel user) {
    // AI-like logic based on user profile
    final fitnessLevel = user.metadata?['fitnessLevel'] as String?;
    final fitnessGoals = user.metadata?['fitnessGoals'] as List<dynamic>?;
    final isBeginnerLevel = fitnessLevel?.toLowerCase() == 'beginner';
    final hasWeightLossGoal = fitnessGoals?.contains('Weight Loss') ?? false;
    final hasMuscleGainGoal = fitnessGoals?.contains('Muscle Gain') ?? false;

    return AIWorkoutPlan(
      id: 'ai_plan_${DateTime.now().millisecondsSinceEpoch}',
      name: 'AI-Generated Plan for ${user.name}',
      description: _generatePlanDescription(user),
      totalDays: 4,
      estimatedCaloriesPerDay: _estimateCaloriesPerDay(user),
      difficultyLevel:
          (user.metadata?['fitnessLevel'] as String?) ?? 'Beginner',
      targetGoals:
          (user.metadata?['fitnessGoals'] as List<dynamic>?)?.cast<String>() ??
          ['General Fitness'],
      dailyWorkouts: _generateDailyWorkouts(user),
      aiInsights: _generateAIInsights(user),
      createdAt: DateTime.now(),
    );
  }

  String _generatePlanDescription(UserModel user) {
    final goals =
        (user.metadata?['fitnessGoals'] as List<dynamic>?)?.join(', ') ??
        'general fitness';
    final level =
        (user.metadata?['fitnessLevel'] as String?)?.toLowerCase() ??
        'beginner';
    final age = user.metadata?['age'] ?? 'unknown';
    final gender =
        (user.metadata?['gender'] as String?)?.toLowerCase() ?? 'unknown';
    final equipment =
        (user.metadata?['availableEquipment'] as List<dynamic>?)?.join(', ') ??
        'bodyweight';

    return 'Personalized 4-day workout plan designed by AI for $level level focusing on $goals. '
        'Tailored to your profile: $age years old, $gender, '
        'with available equipment: $equipment.';
  }

  int _estimateCaloriesPerDay(UserModel user) {
    // AI-like calculation based on user profile
    double baseCalories = 200; // Base calories for light workout

    // Adjust for fitness level
    final fitnessLevel = user.metadata?['fitnessLevel'] as String?;
    switch (fitnessLevel?.toLowerCase()) {
      case 'beginner':
        baseCalories *= 0.8;
        break;
      case 'intermediate':
        baseCalories *= 1.2;
        break;
      case 'advanced':
        baseCalories *= 1.5;
        break;
    }

    // Adjust for goals
    final fitnessGoals = user.metadata?['fitnessGoals'] as List<dynamic>?;
    if (fitnessGoals?.contains('Weight Loss') ?? false) {
      baseCalories *= 1.3; // Higher intensity for weight loss
    }
    if (fitnessGoals?.contains('Muscle Gain') ?? false) {
      baseCalories *= 1.1; // Moderate intensity for muscle gain
    }

    // Adjust for weight (heavier people burn more calories)
    final weight = user.metadata?['weight'] as double?;
    if (weight != null) {
      baseCalories *= (weight / 70); // Normalize to 70kg baseline
    }

    return baseCalories.round();
  }

  List<DailyWorkout> _generateDailyWorkouts(UserModel user) {
    // This would be replaced by actual AI logic
    return [
      DailyWorkout(
        day: 1,
        name: 'Upper Body Focus',
        description:
            'AI-selected exercises targeting chest, shoulders, and arms',
        exercises: _selectExercisesForDay(user, 'upper_body'),
        estimatedDuration: 30,
        estimatedCalories: _estimateCaloriesPerDay(user),
      ),
      DailyWorkout(
        day: 2,
        name: 'Lower Body Power',
        description: 'AI-optimized leg and glute strengthening routine',
        exercises: _selectExercisesForDay(user, 'lower_body'),
        estimatedDuration: 35,
        estimatedCalories: _estimateCaloriesPerDay(user),
      ),
      DailyWorkout(
        day: 3,
        name: 'Core & Cardio',
        description: 'AI-designed core strengthening with cardio intervals',
        exercises: _selectExercisesForDay(user, 'core_cardio'),
        estimatedDuration: 25,
        estimatedCalories: (_estimateCaloriesPerDay(user) * 1.2).round(),
      ),
      DailyWorkout(
        day: 4,
        name: 'Full Body Integration',
        description: 'AI-curated full body workout combining all muscle groups',
        exercises: _selectExercisesForDay(user, 'full_body'),
        estimatedDuration: 40,
        estimatedCalories: (_estimateCaloriesPerDay(user) * 1.1).round(),
      ),
    ];
  }

  List<AISelectedExercise> _selectExercisesForDay(
    UserModel user,
    String focus,
  ) {
    // Mock AI exercise selection - in real implementation, this would use ML models
    // to select from your 1000+ video library
    final exercises = <AISelectedExercise>[];

    switch (focus) {
      case 'upper_body':
        exercises.addAll([
          AISelectedExercise(
            exerciseId: 'push_ups',
            name: 'Push-ups',
            videoUrl:
                'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            duration: 3,
            sets: (user.metadata?['fitnessLevel'] as String?) == 'Beginner'
                ? 2
                : 3,
            reps: (user.metadata?['fitnessLevel'] as String?) == 'Beginner'
                ? 8
                : 12,
            estimatedCalories: _calculateMockCalories(3, user),
            aiReason:
                'Selected for upper body strength building based on your fitness level',
          ),
          // Add more exercises...
        ]);
        break;
      // Add other cases...
    }

    return exercises;
  }

  List<String> _generateAIInsights(UserModel user) {
    final insights = <String>[];
    final fitnessLevel = user.metadata?['fitnessLevel'] as String?;
    final fitnessGoals = user.metadata?['fitnessGoals'] as List<dynamic>?;
    final availableEquipment =
        user.metadata?['availableEquipment'] as List<dynamic>?;

    if (fitnessLevel == 'Beginner') {
      insights.add(
        '🤖 AI Insight: Starting with foundational movements to build proper form',
      );
      insights.add(
        '📈 Gradual progression planned to prevent injury and ensure consistency',
      );
    }

    if (fitnessGoals?.contains('Weight Loss') ?? false) {
      insights.add(
        '🔥 AI Optimization: Higher intensity intervals included for maximum calorie burn',
      );
    }

    if (availableEquipment?.contains('Bodyweight Only') ?? false) {
      insights.add(
        '💪 AI Adaptation: Bodyweight exercises optimized for your space and equipment',
      );
    }

    return insights;
  }

  double _calculateMockCalories(int durationMinutes, UserModel user) {
    // Mock calorie calculation - replace with actual AI model
    double caloriesPerMinute = 5.0; // Base rate

    // Adjust for user weight
    final weight = user.metadata?['weight'] as double?;
    if (weight != null) {
      caloriesPerMinute *= (weight / 70); // Normalize to 70kg
    }

    // Adjust for fitness level (higher level = more efficient = slightly fewer calories)
    final fitnessLevel = user.metadata?['fitnessLevel'] as String?;
    switch (fitnessLevel?.toLowerCase()) {
      case 'beginner':
        caloriesPerMinute *= 1.1;
        break;
      case 'advanced':
        caloriesPerMinute *= 0.9;
        break;
    }

    return caloriesPerMinute * durationMinutes;
  }

  Map<String, dynamic> _generateMockAnalysis(
    List<Map<String, dynamic>> completedExercises,
    UserModel user,
  ) {
    final totalCalories = completedExercises.fold<double>(
      0,
      (sum, exercise) => sum + (exercise['calories_burned'] ?? 0),
    );

    return {
      'total_calories_burned': totalCalories,
      'workout_efficiency': 85.5,
      'ai_feedback': [
        'Great job completing your AI-generated workout!',
        'Your form consistency improved by 12% compared to last session',
        'Consider increasing intensity for next week based on your progress',
      ],
      'next_recommendations': [
        'Add 2 more reps to push-ups next session',
        'Increase plank hold time by 10 seconds',
        'Ready to progress to intermediate level exercises',
      ],
      'performance_metrics': {
        'strength_improvement': '+8%',
        'endurance_gain': '+15%',
        'consistency_score': 92,
      },
    };
  }
}
