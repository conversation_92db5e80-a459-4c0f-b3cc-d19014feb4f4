import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import '../../../core/theme/app_theme.dart';
import '../../exercise/providers/exercise_provider.dart';
import '../../exercise/models/exercise_model.dart';
import 'rest_timer_screen.dart';

class ExerciseGuideScreen extends ConsumerStatefulWidget {
  final Exercise exercise;

  const ExerciseGuideScreen({super.key, required this.exercise});

  @override
  ConsumerState<ExerciseGuideScreen> createState() =>
      _ExerciseGuideScreenState();
}

class _ExerciseGuideScreenState extends ConsumerState<ExerciseGuideScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  VideoPlayerController? _videoController;
  bool _isPlaying = false;
  bool _isVideoInitialized = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeVideo();
  }

  void _initializeVideo() {
    // Check if exercise has video URL (prioritize Google Drive video)
    final videoUrl = widget.exercise.bestVideoUrl;

    print('🎥 Initializing video for ${widget.exercise.name}');
    print('🎥 Video URL: $videoUrl');
    print('🎥 Drive Video URL: ${widget.exercise.driveVideoUrl}');
    print('🎥 Regular Video URL: ${widget.exercise.videoUrl}');

    if (videoUrl != null && videoUrl.isNotEmpty) {
      if (videoUrl.startsWith('http')) {
        print('🌐 Loading network video: $videoUrl');
        // Network video - add headers for better compatibility and timeout
        _videoController = VideoPlayerController.networkUrl(
          Uri.parse(videoUrl),
          httpHeaders: {
            'User-Agent': 'WibeFit/1.0 (Mobile App)',
            'Connection': 'keep-alive',
          },
          videoPlayerOptions: VideoPlayerOptions(
            mixWithOthers: true,
            allowBackgroundPlayback: false,
          ),
        );
      } else {
        print('📁 Loading asset video: $videoUrl');
        // Asset video
        _videoController = VideoPlayerController.asset(videoUrl);
      }

      print('🎥 Starting video initialization...');
      _videoController!
          .initialize()
          .then((_) {
            print('✅ Video initialized successfully!');
            print('🎥 Video duration: ${_videoController!.value.duration}');
            print('🎥 Video size: ${_videoController!.value.size}');
            if (mounted) {
              setState(() {
                _isVideoInitialized = true;
              });
              // Add video state listener
              _videoController!.addListener(_videoListener);
              // Auto-play the video
              _videoController!.play();
              print('🎥 Auto-playing video...');
            }
          })
          .catchError((error) {
            print('Error initializing video: $error');
            if (mounted) {
              setState(() {
                _isVideoInitialized = false;
              });
            }
          });
    } else {
      print('⚠️ No video URL found for ${widget.exercise.name}');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.exercise.name,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        actions: [
          Consumer(
            builder: (context, ref, child) {
              final exerciseId = widget.exercise.id;
              final isFavorite = ref.watch(
                isExerciseFavoriteProvider(exerciseId),
              );
              final toggleFavorite = ref.watch(
                toggleFavoriteProvider(exerciseId),
              );

              return IconButton(
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? Colors.red : Colors.white,
                ),
                onPressed: () {
                  toggleFavorite();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isFavorite
                            ? 'Removed from favorites!'
                            : 'Added to favorites!',
                      ),
                      backgroundColor: AppTheme.primaryColor,
                      duration: const Duration(seconds: 1),
                    ),
                  );
                },
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () => _shareExercise(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.primaryColor,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondary,
          tabs: const [
            Tab(text: 'Guide'),
            Tab(text: 'Instructions'),
            Tab(text: 'Tips'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildGuideTab(), _buildInstructionsTab(), _buildTipsTab()],
      ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildGuideTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildVideoSection(),
          const SizedBox(height: 24),
          _buildExerciseInfo(),
          const SizedBox(height: 24),
          _buildMuscleGroups(),
          const SizedBox(height: 24),
          _buildEquipment(),
        ],
      ),
    );
  }

  Widget _buildVideoSection() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: _videoController != null && _isVideoInitialized
            ? _buildVideoPlayer()
            : _buildVideoPlaceholder(),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: FittedBox(
            fit: BoxFit.cover,
            child: SizedBox(
              width: _videoController!.value.size.width,
              height: _videoController!.value.size.height,
              child: VideoPlayer(_videoController!),
            ),
          ),
        ),
        // Video controls overlay
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.transparent, Colors.black.withValues(alpha: 0.3)],
            ),
          ),
        ),
        // Play/Pause button
        GestureDetector(
          onTap: _toggleVideo,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
        // Video progress indicator
        Positioned(
          bottom: 10,
          left: 10,
          right: 10,
          child: _buildVideoProgress(),
        ),
      ],
    );
  }

  Widget _buildVideoPlaceholder() {
    final hasVideoUrl =
        widget.exercise.bestVideoUrl != null &&
        widget.exercise.bestVideoUrl!.isNotEmpty;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.3),
            AppTheme.primaryColor.withValues(alpha: 0.1),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            hasVideoUrl ? Icons.video_library : Icons.fitness_center,
            size: 60,
            color: AppTheme.primaryColor.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 12),
          Text(
            hasVideoUrl ? 'Loading video...' : 'No video available',
            style: const TextStyle(color: AppTheme.textSecondary, fontSize: 16),
          ),
          if (hasVideoUrl) ...[
            const SizedBox(height: 8),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoProgress() {
    if (_videoController == null || !_isVideoInitialized) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder(
      valueListenable: _videoController!,
      builder: (context, VideoPlayerValue value, child) {
        final progress = value.duration.inMilliseconds > 0
            ? value.position.inMilliseconds / value.duration.inMilliseconds
            : 0.0;

        return Column(
          children: [
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.white.withValues(alpha: 0.3),
              valueColor: const AlwaysStoppedAnimation<Color>(
                AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDuration(value.position),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  _formatDuration(value.duration),
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  Widget _buildExerciseInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Exercise Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Difficulty', 'Intermediate'),
          _buildInfoRow('Type', 'Strength'),
          _buildInfoRow('Duration', '3-4 sets'),
          _buildInfoRow('Calories', '~15 per set'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(color: AppTheme.textSecondary, fontSize: 14),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMuscleGroups() {
    final muscles = ['Chest', 'Triceps', 'Shoulders'];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Target Muscles',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: muscles
                .map(
                  (muscle) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: AppTheme.primaryColor),
                    ),
                    child: Text(
                      muscle,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEquipment() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Equipment Needed',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Dumbbells',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Adjustable weight recommended',
                      style: TextStyle(
                        color: AppTheme.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionsTab() {
    final steps = [
      'Set up the bench at a 45-degree incline',
      'Hold dumbbells with palms facing forward',
      'Lower the weights slowly to chest level',
      'Press the weights up in a controlled motion',
      'Squeeze chest muscles at the top',
      'Repeat for desired repetitions',
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Step-by-Step Instructions',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;

            return Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      step,
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildTipsTab() {
    final tips = [
      {
        'title': 'Form Focus',
        'description':
            'Keep your core engaged and maintain proper posture throughout the movement.',
        'icon': Icons.accessibility_new,
      },
      {
        'title': 'Breathing',
        'description':
            'Exhale during the exertion phase and inhale during the lowering phase.',
        'icon': Icons.air,
      },
      {
        'title': 'Weight Selection',
        'description':
            'Start with lighter weights to master the form before increasing load.',
        'icon': Icons.fitness_center,
      },
      {
        'title': 'Rest Period',
        'description':
            'Rest 60-90 seconds between sets for optimal muscle recovery.',
        'icon': Icons.timer,
      },
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pro Tips',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...tips.map(
            (tip) => Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.cardColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      tip['icon'] as IconData,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tip['title'] as String,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          tip['description'] as String,
                          style: TextStyle(
                            color: AppTheme.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _addToWorkout(),
              icon: const Icon(Icons.add),
              label: const Text('Add to Workout'),
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.primaryColor),
                foregroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _startExercise(),
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Exercise'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleVideo() {
    if (_videoController == null || !_isVideoInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video not available'),
          duration: Duration(seconds: 2),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    print('🎥 Toggle video tapped');
    print(
      '🎥 Video controller: ${_videoController != null ? "Available" : "Null"}',
    );
    print('🎥 Video initialized: $_isVideoInitialized');
    print('🎥 Currently playing: $_isPlaying');

    setState(() {
      if (_isPlaying) {
        print('⏸️ Pausing video');
        _videoController!.pause();
        _isPlaying = false;
      } else {
        print('▶️ Playing video');
        _videoController!.play();
        _isPlaying = true;
      }
    });
  }

  void _videoListener() {
    if (_videoController != null && mounted) {
      final isPlaying = _videoController!.value.isPlaying;
      if (_isPlaying != isPlaying) {
        setState(() {
          _isPlaying = isPlaying;
        });
        print('🎥 Video state changed: ${isPlaying ? "Playing" : "Paused"}');
      }

      // Check for video errors
      if (_videoController!.value.hasError) {
        print('❌ Video error: ${_videoController!.value.errorDescription}');
      }
    }
  }

  void _shareExercise() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exercise shared!'),
        backgroundColor: AppTheme.primaryColor,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _addToWorkout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Add to Workout',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Exercise added to your current workout!',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK', style: TextStyle(color: AppTheme.primaryColor)),
          ),
        ],
      ),
    );
  }

  void _startExercise() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RestTimerScreen(exercise: widget.exercise),
      ),
    );
  }
}
