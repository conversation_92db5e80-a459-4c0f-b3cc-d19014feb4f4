import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/services/exercise_service.dart';
import '../../../core/services/user_drive_integration_service.dart';
import '../models/exercise_model.dart';

// Exercise state
class ExerciseState {
  final List<Exercise> exercises;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int total;

  const ExerciseState({
    this.exercises = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.total = 0,
  });

  ExerciseState copyWith({
    List<Exercise>? exercises,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? total,
  }) {
    return ExerciseState(
      exercises: exercises ?? this.exercises,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      total: total ?? this.total,
    );
  }
}

// Exercise filters
class ExerciseFilters {
  final String? search;
  final List<String>? muscleGroups;
  final List<String>? equipment;
  final List<String>? difficulty;
  final List<String>? category;
  final bool? isFavorite;

  const ExerciseFilters({
    this.search,
    this.muscleGroups,
    this.equipment,
    this.difficulty,
    this.category,
    this.isFavorite,
  });

  ExerciseFilters copyWith({
    String? search,
    List<String>? muscleGroups,
    List<String>? equipment,
    List<String>? difficulty,
    List<String>? category,
    bool? isFavorite,
  }) {
    return ExerciseFilters(
      search: search ?? this.search,
      muscleGroups: muscleGroups ?? this.muscleGroups,
      equipment: equipment ?? this.equipment,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

// Exercise provider
class ExerciseNotifier extends StateNotifier<ExerciseState> {
  final ExerciseService _exerciseService;
  ExerciseFilters _currentFilters = const ExerciseFilters();

  ExerciseNotifier(this._exerciseService) : super(const ExerciseState());

  // Load exercises with current filters
  Future<void> loadExercises({bool refresh = false}) async {
    if (state.isLoading) return;

    if (refresh) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      // Reduce debug logging for better performance
      final response = await _exerciseService.getExercises(
        search: _currentFilters.search,
        muscleGroups: _currentFilters.muscleGroups,
        equipment: _currentFilters.equipment,
        difficulty: _currentFilters.difficulty,
        category: _currentFilters.category,
        isFavorite: _currentFilters.isFavorite,
        limit: 10, // Reduced from 20 to 10 for better performance
        offset: refresh ? 0 : state.exercises.length,
      );

      final exerciseList = (response['exercises'] as List)
          .map((json) => Exercise.fromJson(json))
          .toList();

      final newExercises = refresh
          ? exerciseList
          : [...state.exercises, ...exerciseList];

      state = state.copyWith(
        exercises: newExercises,
        isLoading: false,
        hasMore: response['has_more'] ?? false,
        total: response['total'] ?? 0,
      );
    } catch (e) {
      // Load exercises from Google Drive integration first
      try {
        print('📊 Loading exercises from Google Drive integration...');
        final driveExercises =
            await UserDriveIntegrationService.loadUserExercises();

        if (driveExercises.isNotEmpty) {
          final newExercises = refresh
              ? driveExercises
              : [...state.exercises, ...driveExercises];

          state = state.copyWith(
            exercises: newExercises,
            isLoading: false,
            hasMore: false,
            total: driveExercises.length,
            error: null,
          );
          print(
            '📊 Successfully loaded ${driveExercises.length} exercises from Google Drive',
          );
          return;
        }
      } catch (driveError) {
        print('📊 Google Drive loading failed: $driveError');
      }

      // Fallback to mock data if Google Drive fails
      try {
        final mockExercises = [
          {
            'id': 1,
            'name': 'Push-ups',
            'slug': 'push-ups',
            'description':
                'Classic bodyweight exercise for chest, shoulders, and triceps',
            'muscle_groups': ['Chest', 'Shoulders', 'Triceps'],
            'equipment': ['Bodyweight'],
            'difficulty': 'Beginner',
            'exercise_type': 'strength',
            'category': 'upper_body',
            'instructions': [
              'Start in a plank position with hands slightly wider than shoulders',
              'Lower your body until chest nearly touches the floor',
              'Push back up to starting position',
              'Keep your body in a straight line throughout',
            ],
            'tips': [
              'Keep your core engaged',
              'Don\'t let your hips sag',
              'Control the movement - don\'t rush',
            ],
            'duration_minutes': null,
            'calories_per_minute': 8.0,
            'video_url':
                'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            'image_url': null,
            'is_active': true,
            'created_by': null,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': null,
            'is_favorite': false,
          },
          {
            'id': 2,
            'name': 'Squats',
            'slug': 'squats',
            'description':
                'Fundamental lower body exercise targeting quads, glutes, and hamstrings',
            'muscle_groups': ['Quadriceps', 'Glutes', 'Hamstrings'],
            'equipment': ['Bodyweight'],
            'difficulty': 'Beginner',
            'exercise_type': 'strength',
            'category': 'lower_body',
            'instructions': [
              'Stand with feet shoulder-width apart',
              'Lower your body as if sitting back into a chair',
              'Keep your chest up and knees behind toes',
              'Push through heels to return to starting position',
            ],
            'tips': [
              'Keep your weight on your heels',
              'Don\'t let knees cave inward',
              'Go as low as your mobility allows',
            ],
            'duration_minutes': null,
            'calories_per_minute': 7.0,
            'video_url':
                'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
            'image_url': null,
            'is_active': true,
            'created_by': null,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': null,
            'is_favorite': false,
          },
          {
            'id': 3,
            'name': 'Plank',
            'slug': 'plank',
            'description':
                'Core strengthening exercise that also engages shoulders and glutes',
            'muscle_groups': ['Core', 'Shoulders', 'Glutes'],
            'equipment': ['Bodyweight'],
            'difficulty': 'Beginner',
            'exercise_type': 'strength',
            'category': 'core',
            'instructions': [
              'Start in a push-up position',
              'Lower onto your forearms',
              'Keep your body in a straight line',
              'Hold the position',
            ],
            'tips': [
              'Don\'t let your hips sag or pike up',
              'Breathe normally',
              'Start with shorter holds and build up',
            ],
            'duration_minutes': 1,
            'calories_per_minute': 5.0,
            'video_url':
                'https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4',
            'image_url': null,
            'is_active': true,
            'created_by': null,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': null,
            'is_favorite': false,
          },
        ];

        final exerciseList = mockExercises
            .map((json) => Exercise.fromJson(json))
            .toList();

        final newExercises = refresh
            ? exerciseList
            : [...state.exercises, ...exerciseList];

        state = state.copyWith(
          exercises: newExercises,
          isLoading: false,
          hasMore: false,
          total: exerciseList.length,
          error: null,
        );

        print('📊 Successfully loaded ${exerciseList.length} mock exercises');
      } catch (mockError) {
        print('📊 Error loading mock exercises: $mockError');
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to load exercises: $e',
        );
      }
    }
  }

  // Apply filters and reload
  Future<void> applyFilters(ExerciseFilters filters) async {
    _currentFilters = filters;
    await loadExercises(refresh: true);
  }

  // Search exercises
  Future<void> searchExercises(String query) async {
    _currentFilters = _currentFilters.copyWith(search: query);
    await loadExercises(refresh: true);
  }

  // Load more exercises (pagination)
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;
    await loadExercises();
  }

  // Get specific exercise
  Future<Exercise?> getExercise(int exerciseId) async {
    try {
      final response = await _exerciseService.getExercise(exerciseId);
      return Exercise.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Clear filters
  void clearFilters() {
    _currentFilters = const ExerciseFilters();
    loadExercises(refresh: true);
  }
}

// Providers
final exerciseServiceProvider = Provider<ExerciseService>(
  (ref) => ExerciseService(),
);

final exerciseProvider = StateNotifierProvider<ExerciseNotifier, ExerciseState>(
  (ref) {
    final exerciseService = ref.watch(exerciseServiceProvider);
    return ExerciseNotifier(exerciseService);
  },
);

// Muscle groups provider
final muscleGroupsProvider = FutureProvider<List<MuscleGroup>>((ref) async {
  final exerciseService = ref.watch(exerciseServiceProvider);
  try {
    final response = await exerciseService.getMuscleGroups();
    return response.map((json) => MuscleGroup.fromJson(json)).toList();
  } catch (e) {
    throw Exception('Failed to load muscle groups: $e');
  }
});

// Equipment provider
final equipmentProvider = FutureProvider<List<Equipment>>((ref) async {
  final exerciseService = ref.watch(exerciseServiceProvider);
  try {
    final response = await exerciseService.getEquipment();
    return response.map((json) => Equipment.fromJson(json)).toList();
  } catch (e) {
    throw Exception('Failed to load equipment: $e');
  }
});

// Current filters provider
final exerciseFiltersProvider = StateProvider<ExerciseFilters>((ref) {
  return const ExerciseFilters();
});

// Search query provider
final exerciseSearchProvider = StateProvider<String>((ref) => '');

// Real favorites provider using backend API with local fallback
class FavoritesNotifier extends StateNotifier<Set<int>> {
  final ExerciseService _exerciseService;
  static const String _favoritesKey = 'local_favorites';

  FavoritesNotifier(this._exerciseService) : super(<int>{}) {
    _loadLocalFavorites();
  }

  // Load favorites from local storage
  Future<void> _loadLocalFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
      final favoriteIds = favoritesJson.map((id) => int.parse(id)).toSet();
      state = favoriteIds;
    } catch (e) {
      print('Error loading local favorites: $e');
    }
  }

  // Save favorites to local storage
  Future<void> _saveLocalFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = state.map((id) => id.toString()).toList();
      await prefs.setStringList(_favoritesKey, favoritesJson);
    } catch (e) {
      print('Error saving local favorites: $e');
    }
  }

  // Toggle favorite status
  Future<void> toggleFavorite(int exerciseId) async {
    final isCurrentlyFavorite = state.contains(exerciseId);

    // Always update local state immediately for better UX
    if (isCurrentlyFavorite) {
      state = {...state}..remove(exerciseId);
    } else {
      state = {...state, exerciseId};
    }

    // Save to local storage immediately
    await _saveLocalFavorites();

    // Try to sync with backend, but don't revert if it fails
    try {
      bool success;
      if (isCurrentlyFavorite) {
        success = await _exerciseService.removeFromFavorites(exerciseId);
      } else {
        success = await _exerciseService.addToFavorites(exerciseId);
      }

      if (!success) {
        print('Failed to sync favorite with backend, keeping local state');
      }
    } catch (e) {
      print('Error syncing favorite with backend: $e (keeping local state)');
    }
  }

  // Load user's favorites from backend
  Future<void> loadFavorites() async {
    try {
      final response = await _exerciseService.getFavoriteExercises(limit: 100);
      final exercises = response['exercises'] as List;
      final favoriteIds = exercises.map((e) => e['id'] as int).toSet();
      state = favoriteIds;
      // Save backend favorites to local storage
      await _saveLocalFavorites();
    } catch (e) {
      print('Error loading favorites from backend: $e (using local state)');
      // Load from local storage if backend fails
      await _loadLocalFavorites();
    }
  }
}

// Favorites provider
final favoritesProvider = StateNotifierProvider<FavoritesNotifier, Set<int>>((
  ref,
) {
  final exerciseService = ref.watch(exerciseServiceProvider);
  final notifier = FavoritesNotifier(exerciseService);
  // Load favorites when provider is created
  notifier.loadFavorites();
  return notifier;
});

// Toggle favorite function provider
final toggleFavoriteProvider = Provider.family<void Function(), int>((
  ref,
  exerciseId,
) {
  return () {
    ref.read(favoritesProvider.notifier).toggleFavorite(exerciseId);
  };
});

// Check if exercise is favorite
final isExerciseFavoriteProvider = Provider.family<bool, int>((
  ref,
  exerciseId,
) {
  final favorites = ref.watch(favoritesProvider);
  return favorites.contains(exerciseId);
});
