/// Performance configuration for the WibeFit app
/// This file contains optimizations to improve app performance and reduce lag
class PerformanceConfig {
  // Network timeouts - optimized for mobile devices
  static const Duration networkTimeout = Duration(seconds: 15);
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration authTimeout = Duration(seconds: 15);
  
  // Image loading optimizations
  static const int imageCacheWidth = 100;
  static const int imageCacheHeight = 100;
  static const int maxImageCacheSize = 50; // MB
  
  // ListView optimizations
  static const double listCacheExtent = 500.0;
  static const bool addAutomaticKeepAlives = false;
  static const bool addRepaintBoundaries = false;
  
  // Video player optimizations
  static const bool allowBackgroundPlayback = false;
  static const bool mixWithOthers = true;
  
  // Memory management
  static const int maxCachedPages = 3;
  static const Duration cacheTimeout = Duration(minutes: 5);
  
  // UI performance
  static const bool enableRepaintBoundaries = true;
  static const bool enableAutomaticKeepAlive = false;
  
  // Network retry configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Pagination settings
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Background task limits
  static const int maxConcurrentTasks = 3;
  static const Duration backgroundTaskTimeout = Duration(seconds: 30);
}
