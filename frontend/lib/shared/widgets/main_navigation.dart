import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/theme/app_theme.dart';

class MainNavigation extends StatelessWidget {
  final Widget child;

  const MainNavigation({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: const _OptimizedBottomNavigationBar(),
    );
  }
}

// Separate widget to prevent unnecessary rebuilds
class _OptimizedBottomNavigationBar extends StatelessWidget {
  const _OptimizedBottomNavigationBar();

  @override
  Widget build(BuildContext context) {
    return _buildBottomNavigationBar(context);
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    final currentLocation = GoRouterState.of(context).uri.path;

    int selectedIndex = 0;
    if (currentLocation.startsWith('/workout')) {
      selectedIndex = 0;
    } else if (currentLocation.startsWith('/analysis')) {
      selectedIndex = 1;
    } else if (currentLocation.startsWith('/community')) {
      selectedIndex = 2;
    } else if (currentLocation.startsWith('/profile')) {
      selectedIndex = 3;
    }

    // Use BottomNavigationBar for better performance
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppTheme.surfaceColor,
      selectedItemColor: AppTheme.primaryColor,
      unselectedItemColor: Colors.grey[600],
      currentIndex: selectedIndex,
      elevation: 8,
      onTap: (index) {
        switch (index) {
          case 0:
            context.go('/workout');
            break;
          case 1:
            context.go('/analysis');
            break;
          case 2:
            context.go('/community');
            break;
          case 3:
            context.go('/profile');
            break;
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.fitness_center),
          label: 'Workout',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics_outlined),
          label: 'Analysis',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.people_outline),
          label: 'Community',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person_outline),
          label: 'Profile',
        ),
      ],
    );
  }
}
