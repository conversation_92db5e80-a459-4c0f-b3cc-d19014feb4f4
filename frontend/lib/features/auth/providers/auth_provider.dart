import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/auth_service.dart';
import '../../../core/services/mock_api_service.dart';
import '../../../shared/models/user_model.dart';

// Auth state
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserModel? user;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    UserModel? user,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);

    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        // Also load profile data from mock API to merge with user data
        await _loadProfileDataFromMockAPI(user);

        state = state.copyWith(
          isAuthenticated: true,
          user: user,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isAuthenticated: false, isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Load profile data from mock API and merge with user data
  Future<void> _loadProfileDataFromMockAPI(UserModel user) async {
    try {
      // Import mock API service
      final mockProfile = await MockApiService.getUserProfile();
      if (mockProfile != null) {
        print('✅ Found profile data in mock API: $mockProfile');

        // Merge mock API data with user metadata
        final updatedMetadata = {...user.metadata ?? {}, ...mockProfile};

        // Update the user with merged data
        final updatedUser = user.copyWith(metadata: updatedMetadata);

        // Update current user in auth service
        await _authService.updateCurrentUserFromMockAPI(updatedUser);

        // Update the state with the merged user data
        state = state.copyWith(user: updatedUser);

        print('✅ User data updated with mock API profile data');
      } else {
        print('ℹ️ No profile data found in mock API');
      }
    } catch (e) {
      print('⚠️ Error loading profile data from mock API: $e');
      // Don't throw error, just continue without mock data
    }
  }

  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authResponse = await _authService.login(email, password);

      state = state.copyWith(
        isAuthenticated: true,
        user: authResponse.user,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> register({
    required String email,
    required String password,
    required String name,
    required String role,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authResponse = await _authService.register(
        email: email,
        password: password,
        name: name,
        role: role,
      );

      state = state.copyWith(
        isAuthenticated: true,
        user: authResponse.user,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final authResponse = await _authService.signInWithGoogle();

      state = state.copyWith(
        isAuthenticated: true,
        user: authResponse.user,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Check if user needs to complete profile setup
  bool get needsProfileSetup {
    final user = state.user;
    if (user == null) return false;

    // Check if user has completed profile setup
    final profileCompleted = user.metadata?['profileSetupCompleted'] as bool?;
    if (profileCompleted == true) return false;

    // Check if user has any fitness data
    final metadata = user.metadata ?? {};
    final hasAnyFitnessData =
        metadata['age'] != null ||
        metadata['gender'] != null ||
        metadata['height'] != null ||
        metadata['weight'] != null ||
        metadata['fitnessLevel'] != null ||
        metadata['fitnessGoals'] != null ||
        metadata['availableEquipment'] != null;

    // Only show prompt if user has no fitness data at all
    return !hasAnyFitnessData;
  }

  /// Update user profile data
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      // Save to backend database first
      await _authService.updateProfile(
        name: updatedUser.name,
        profileImageUrl: updatedUser.profileImageUrl,
        metadata: updatedUser.metadata,
      );

      // Update local state after successful backend save
      state = state.copyWith(user: updatedUser);
    } catch (e) {
      // Revert state on error
      await _checkAuthStatus();
      rethrow;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.logout();
      state = const AuthState();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

// Convenience providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
