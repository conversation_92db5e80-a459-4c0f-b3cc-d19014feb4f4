import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../exercise/models/exercise_model.dart';
import '../../exercise/providers/exercise_provider.dart';

class EditCustomPlanScreen extends ConsumerStatefulWidget {
  final String? planId; // null for new plan, planId for editing existing

  const EditCustomPlanScreen({super.key, this.planId});

  @override
  ConsumerState<EditCustomPlanScreen> createState() =>
      _EditCustomPlanScreenState();
}

class _EditCustomPlanScreenState extends ConsumerState<EditCustomPlanScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Plan basic info
  final TextEditingController _planNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String _difficulty = 'Beginner';
  int _durationWeeks = 4;
  int _workoutsPerWeek = 3;
  List<String> _targetGoals = [];
  List<String> _requiredEquipment = [];

  // Weekly schedule
  final Map<String, List<Exercise>> _weeklySchedule = {
    'Monday': [],
    'Tuesday': [],
    'Wednesday': [],
    'Thursday': [],
    'Friday': [],
    'Saturday': [],
    'Sunday': [],
  };

  String _selectedDay = 'Monday';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load existing plan if editing
    if (widget.planId != null) {
      _loadExistingPlan();
    } else {
      _planNameController.text = 'My Custom Plan';
      _descriptionController.text = 'A personalized workout plan';
    }

    // Load exercises
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(exerciseProvider.notifier).loadExercises();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _planNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _loadExistingPlan() {
    // TODO: Load existing plan data from backend/storage
    // For now, use sample data
    _planNameController.text = 'My Strength Plan';
    _descriptionController.text = 'Custom strength training plan';
    _difficulty = 'Intermediate';
    _durationWeeks = 8;
    _workoutsPerWeek = 4;
    _targetGoals = ['Strength', 'Muscle Growth'];
    _requiredEquipment = ['Dumbbells', 'Barbell'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
        title: Text(
          widget.planId == null ? 'Create Custom Plan' : 'Edit Custom Plan',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _savePlan,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppTheme.primaryColor,
          labelColor: AppTheme.primaryColor,
          unselectedLabelColor: AppTheme.textSecondary,
          tabs: const [
            Tab(text: 'Plan Info'),
            Tab(text: 'Schedule'),
            Tab(text: 'Preview'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPlanInfoTab(),
          _buildScheduleTab(),
          _buildPreviewTab(),
        ],
      ),
    );
  }

  Widget _buildPlanInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Basic Information'),
          const SizedBox(height: 16),

          // Plan Name
          TextField(
            controller: _planNameController,
            decoration: const InputDecoration(
              labelText: 'Plan Name',
              hintText: 'Enter your plan name',
              border: OutlineInputBorder(),
            ),
            style: const TextStyle(color: Colors.black),
          ),
          const SizedBox(height: 16),

          // Description
          TextField(
            controller: _descriptionController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Describe your workout plan',
              border: OutlineInputBorder(),
            ),
            style: const TextStyle(color: Colors.black),
          ),
          const SizedBox(height: 24),

          _buildSectionTitle('Plan Settings'),
          const SizedBox(height: 16),

          // Difficulty
          _buildDropdownField(
            'Difficulty Level',
            _difficulty,
            ['Beginner', 'Intermediate', 'Advanced'],
            (value) => setState(() => _difficulty = value!),
          ),
          const SizedBox(height: 16),

          // Duration
          _buildSliderField(
            'Duration (Weeks)',
            _durationWeeks.toDouble(),
            1,
            16,
            (value) => setState(() => _durationWeeks = value.round()),
            '$_durationWeeks weeks',
          ),
          const SizedBox(height: 16),

          // Workouts per week
          _buildSliderField(
            'Workouts per Week',
            _workoutsPerWeek.toDouble(),
            1,
            7,
            (value) => setState(() => _workoutsPerWeek = value.round()),
            '$_workoutsPerWeek times',
          ),
          const SizedBox(height: 24),

          _buildSectionTitle('Goals & Equipment'),
          const SizedBox(height: 16),

          // Target Goals
          _buildMultiSelectField(
            'Target Goals',
            _targetGoals,
            [
              'Weight Loss',
              'Muscle Growth',
              'Strength',
              'Endurance',
              'Flexibility',
              'General Fitness',
            ],
            (goals) => setState(() => _targetGoals = goals),
          ),
          const SizedBox(height: 16),

          // Required Equipment
          _buildMultiSelectField(
            'Required Equipment',
            _requiredEquipment,
            [
              'Bodyweight',
              'Dumbbells',
              'Barbell',
              'Kettlebell',
              'Resistance Bands',
              'Pull-up Bar',
              'Bench',
            ],
            (equipment) => setState(() => _requiredEquipment = equipment),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleTab() {
    final exerciseState = ref.watch(exerciseProvider);

    return Column(
      children: [
        // Day selector
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _weeklySchedule.keys.length,
            itemBuilder: (context, index) {
              final day = _weeklySchedule.keys.elementAt(index);
              final isSelected = day == _selectedDay;
              final hasExercises = _weeklySchedule[day]!.isNotEmpty;

              return GestureDetector(
                onTap: () => setState(() => _selectedDay = day),
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.grey.shade300,
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        day.substring(0, 3),
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (hasExercises)
                        Container(
                          width: 6,
                          height: 6,
                          margin: const EdgeInsets.only(top: 2),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.white
                                : AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '$_selectedDay Workout',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (_weeklySchedule[_selectedDay]!.isNotEmpty)
                      TextButton(
                        onPressed: () => _clearDayWorkout(),
                        child: const Text('Clear All'),
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Selected exercises for the day
                if (_weeklySchedule[_selectedDay]!.isEmpty)
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.fitness_center,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No exercises for $_selectedDay',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add exercises from the list below',
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  Expanded(
                    child: Column(
                      children: [
                        Expanded(flex: 2, child: _buildSelectedExercisesList()),
                        const SizedBox(height: 16),
                        Expanded(
                          flex: 3,
                          child: _buildAvailableExercisesList(exerciseState),
                        ),
                      ],
                    ),
                  ),

                if (_weeklySchedule[_selectedDay]!.isEmpty)
                  _buildAvailableExercisesList(exerciseState),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewTab() {
    final totalExercises = _weeklySchedule.values.fold(
      0,
      (sum, exercises) => sum + exercises.length,
    );
    final activeDays = _weeklySchedule.values
        .where((exercises) => exercises.isNotEmpty)
        .length;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Plan Overview'),
          const SizedBox(height: 16),

          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _planNameController.text.isEmpty
                        ? 'Untitled Plan'
                        : _planNameController.text,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _descriptionController.text.isEmpty
                        ? 'No description'
                        : _descriptionController.text,
                    style: const TextStyle(color: Colors.black87),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      _buildInfoChip(
                        Icons.trending_up,
                        _difficulty,
                        _getDifficultyColor(_difficulty),
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        Icons.schedule,
                        '$_durationWeeks weeks',
                        Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        Icons.fitness_center,
                        '$_workoutsPerWeek/week',
                        Colors.green,
                      ),
                    ],
                  ),

                  if (_targetGoals.isNotEmpty) ...[
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: _targetGoals
                          .map((goal) => _buildGoalChip(goal))
                          .toList(),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('Weekly Schedule'),
          const SizedBox(height: 16),

          Text(
            '$activeDays active days • $totalExercises total exercises',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
          ),
          const SizedBox(height: 12),

          ..._weeklySchedule.entries.map((entry) {
            final day = entry.key;
            final exercises = entry.value;

            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: exercises.isEmpty
                      ? Colors.grey.shade300
                      : AppTheme.primaryColor,
                  child: Text(
                    day.substring(0, 1),
                    style: TextStyle(
                      color: exercises.isEmpty
                          ? Colors.grey.shade600
                          : Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(
                  day,
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  exercises.isEmpty
                      ? 'Rest day'
                      : '${exercises.length} exercises',
                  style: const TextStyle(color: Colors.black87),
                ),
                trailing: exercises.isEmpty
                    ? null
                    : Text(
                        '${exercises.fold(0, (sum, ex) => sum + (ex.durationMinutes ?? 3))} min',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
        color: Colors.black,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildDropdownField(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: options
              .map(
                (option) => DropdownMenuItem(
                  value: option,
                  child: Text(
                    option,
                    style: const TextStyle(color: Colors.black),
                  ),
                ),
              )
              .toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSliderField(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    String displayValue,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              displayValue,
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).round(),
          activeColor: AppTheme.primaryColor,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildMultiSelectField(
    String label,
    List<String> selectedItems,
    List<String> allItems,
    ValueChanged<List<String>> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: allItems.map((item) {
            final isSelected = selectedItems.contains(item);
            return FilterChip(
              label: Text(
                item,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                ),
              ),
              selected: isSelected,
              selectedColor: AppTheme.primaryColor,
              onSelected: (selected) {
                final newList = List<String>.from(selectedItems);
                if (selected) {
                  newList.add(item);
                } else {
                  newList.remove(item);
                }
                onChanged(newList);
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSelectedExercisesList() {
    final exercises = _weeklySchedule[_selectedDay]!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Exercises (${exercises.length})',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: exercises.length,
            itemBuilder: (context, index) {
              final exercise = exercises[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 4),
                child: ListTile(
                  dense: true,
                  leading: CircleAvatar(
                    radius: 16,
                    backgroundColor: AppTheme.primaryColor,
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                  title: Text(
                    exercise.name,
                    style: const TextStyle(color: Colors.black, fontSize: 14),
                  ),
                  subtitle: Text(
                    '${exercise.durationMinutes ?? 3} min',
                    style: const TextStyle(color: Colors.black87, fontSize: 12),
                  ),
                  trailing: IconButton(
                    icon: const Icon(
                      Icons.remove_circle,
                      color: Colors.red,
                      size: 20,
                    ),
                    onPressed: () => _removeExerciseFromDay(exercise),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAvailableExercisesList(exerciseState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Exercises',
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: exerciseState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : exerciseState.exercises.isEmpty
              ? const Center(
                  child: Text(
                    'No exercises available',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: exerciseState.exercises.length,
                  itemBuilder: (context, index) {
                    final exercise = exerciseState.exercises[index];
                    final isAdded = _weeklySchedule[_selectedDay]!.contains(
                      exercise,
                    );

                    return Card(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: ListTile(
                        dense: true,
                        leading: Icon(
                          isAdded
                              ? Icons.check_circle
                              : Icons.add_circle_outline,
                          color: isAdded ? Colors.green : AppTheme.primaryColor,
                        ),
                        title: Text(
                          exercise.name,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                          ),
                        ),
                        subtitle: Text(
                          '${exercise.difficulty} • ${exercise.durationMinutes ?? 3} min',
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 12,
                          ),
                        ),
                        onTap: isAdded
                            ? null
                            : () => _addExerciseToDay(exercise),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalChip(String goal) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        goal,
        style: TextStyle(
          fontSize: 10,
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _addExerciseToDay(Exercise exercise) {
    setState(() {
      _weeklySchedule[_selectedDay]!.add(exercise);
    });
  }

  void _removeExerciseFromDay(Exercise exercise) {
    setState(() {
      _weeklySchedule[_selectedDay]!.remove(exercise);
    });
  }

  void _clearDayWorkout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Workout'),
        content: Text('Remove all exercises from $_selectedDay?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _weeklySchedule[_selectedDay]!.clear();
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Clear', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _savePlan() async {
    if (_planNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Please enter a plan name')));
      return;
    }

    setState(() => _isLoading = true);

    try {
      // TODO: Save plan to backend/storage
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.planId == null
                  ? 'Custom plan created successfully!'
                  : 'Custom plan updated successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving plan: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
