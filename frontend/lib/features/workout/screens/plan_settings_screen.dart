import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/constants/app_constants.dart';

class PlanSettingsScreen extends StatefulWidget {
  const PlanSettingsScreen({super.key});

  @override
  State<PlanSettingsScreen> createState() => _PlanSettingsScreenState();
}

class _PlanSettingsScreenState extends State<PlanSettingsScreen> {
  final Map<String, dynamic> _currentPlan = AppConstants.sampleWorkoutPlans[0];
  final int _workoutsPerWeek = 4;
  final String _warmupIntensity = 'Average';
  final int _stretchDuration = 20;
  bool _bodywightOnly = false;
  String _setsMode = 'Recommended';
  bool _warmupSet = true;
  final List<String> _excludedExercises = [];
  final List<String> _selectedEquipment = ['Bodyweight', 'Dumbbells'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Plan Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              context.push('/workout/change-plan');
            },
            child: const Text('Change Plan'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPlanOverview(),
            const SizedBox(height: 24),
            _buildTargetMuscles(),
            const SizedBox(height: 24),
            _buildWorkoutSettings(),
            const SizedBox(height: 24),
            _buildBodyInfoButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanOverview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.shield, color: Colors.black, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _currentPlan['name'],
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _currentPlan['description'],
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildTargetMuscles() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Target Muscles', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        ...(_currentPlan['targetMuscles'] as Map<String, dynamic>).entries.map(
          (entry) => _buildDayMuscles(entry.key, entry.value as List<String>),
        ),
      ],
    );
  }

  Widget _buildDayMuscles(String day, List<String> muscles) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(day, style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(width: 8),
              Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
              Container(
                width: 4,
                height: 4,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
              Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: muscles
                .map((muscle) => _buildMuscleChip(muscle))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMuscleChip(String muscle) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(muscle, style: Theme.of(context).textTheme.bodySmall),
    );
  }

  Widget _buildWorkoutSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Workout Setting', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),

        // Workouts per Week
        _buildSettingItem(
          'Workouts per Week',
          '$_workoutsPerWeek times',
          onTap: () => _showWorkoutsPerWeekDialog(),
        ),

        const SizedBox(height: 24),
        Text(
          'Warmup & Cooldown',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),

        _buildSettingItem(
          'Warm-up',
          _warmupIntensity,
          onTap: () => _showWarmupIntensityDialog(),
        ),

        _buildSettingItem(
          'Cool-down',
          _warmupIntensity,
          onTap: () => _showWarmupIntensityDialog(),
        ),

        _buildSettingItem(
          'Stretch Duration',
          '$_stretchDuration seconds',
          onTap: () => _showStretchDurationDialog(),
        ),

        const SizedBox(height: 24),
        Text('Main workout', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 12),

        _buildSwitchItem(
          'Bodyweight only workout',
          _bodywightOnly,
          (value) => setState(() => _bodywightOnly = value),
        ),

        if (!_bodywightOnly)
          _buildSettingItem(
            'Workout Equipment',
            _selectedEquipment.join(', '),
            enabled: !_bodywightOnly,
            onTap: () => _showEquipmentDialog(),
          ),

        _buildSettingItem(
          'Sets, Weight and Reps',
          _setsMode,
          onTap: () => _showSetsModeDialog(),
        ),

        _buildSwitchItem(
          'Warm-up Set',
          _warmupSet,
          (value) => setState(() => _warmupSet = value),
        ),

        _buildSettingItem(
          'Excluded exercises',
          _excludedExercises.isEmpty
              ? 'None'
              : '${_excludedExercises.length} exercises',
          onTap: () => _showExcludedExercisesDialog(),
        ),
      ],
    );
  }

  Widget _buildSettingItem(
    String title,
    String value, {
    VoidCallback? onTap,
    bool enabled = true,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: enabled ? AppTheme.textPrimary : AppTheme.textTertiary,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: enabled ? AppTheme.primaryColor : AppTheme.textTertiary,
              ),
            ),
            if (onTap != null) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: enabled ? AppTheme.textSecondary : AppTheme.textTertiary,
              ),
            ],
          ],
        ),
        onTap: enabled ? onTap : null,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildSwitchItem(
    String title,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(title, style: Theme.of(context).textTheme.bodyLarge),
        trailing: Switch(value: value, onChanged: onChanged),
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildBodyInfoButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.person, color: AppTheme.primaryColor),
          const SizedBox(width: 12),
          Text(
            'Change body info and fitness level',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppTheme.primaryColor),
          ),
          const Spacer(),
          Icon(Icons.chevron_right, color: AppTheme.primaryColor),
        ],
      ),
    );
  }

  void _showWorkoutsPerWeekDialog() {
    // Show dialog to select workouts per week
  }

  void _showWarmupIntensityDialog() {
    // Show dialog to select warmup intensity
  }

  void _showStretchDurationDialog() {
    // Show dialog to select stretch duration
  }

  void _showSetsModeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sets, Weight and Reps'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Recommended'),
              leading: Radio<String>(
                value: 'Recommended',
                groupValue: _setsMode,
                onChanged: (value) {
                  setState(() => _setsMode = value!);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              title: const Text('Previous Records'),
              leading: Radio<String>(
                value: 'Previous Records',
                groupValue: _setsMode,
                onChanged: (value) {
                  setState(() => _setsMode = value!);
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEquipmentDialog() {
    final availableEquipment = [
      'Bodyweight',
      'Dumbbells',
      'Barbell',
      'Kettlebell',
      'Resistance Bands',
      'Pull-up Bar',
      'Bench',
      'Cable Machine',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Equipment'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: availableEquipment.length,
            itemBuilder: (context, index) {
              final equipment = availableEquipment[index];
              return CheckboxListTile(
                title: Text(equipment),
                value: _selectedEquipment.contains(equipment),
                onChanged: (checked) {
                  setState(() {
                    if (checked == true) {
                      _selectedEquipment.add(equipment);
                    } else {
                      _selectedEquipment.remove(equipment);
                    }
                  });
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showExcludedExercisesDialog() {
    final sampleExercises = [
      'Push-ups',
      'Squats',
      'Deadlifts',
      'Bench Press',
      'Pull-ups',
      'Lunges',
      'Planks',
      'Burpees',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exclude Exercises'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: sampleExercises.length,
            itemBuilder: (context, index) {
              final exercise = sampleExercises[index];
              return CheckboxListTile(
                title: Text(exercise),
                value: _excludedExercises.contains(exercise),
                onChanged: (checked) {
                  setState(() {
                    if (checked == true) {
                      _excludedExercises.add(exercise);
                    } else {
                      _excludedExercises.remove(exercise);
                    }
                  });
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
