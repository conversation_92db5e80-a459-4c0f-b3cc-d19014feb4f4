import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/auth_provider.dart';

class GoogleSignInButton extends ConsumerWidget {
  final VoidCallback? onSuccess;
  final VoidCallback? onError;

  const GoogleSignInButton({super.key, this.onSuccess, this.onError});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: OutlinedButton.icon(
        onPressed: authState.isLoading
            ? null
            : () async {
                try {
                  // Show a debug message for now
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Google Sign-In button tapped! (Firebase setup needed)',
                        ),
                        backgroundColor: Colors.blue,
                      ),
                    );
                  }

                  // Uncomment when Firebase is fully configured
                  // await ref.read(authProvider.notifier).signInWithGoogle();
                  // onSuccess?.call();
                } catch (e) {
                  onError?.call();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Google Sign-In failed: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.grey),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        icon: authState.isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Image.asset(
                'assets/icons/google_logo.png',
                width: 20,
                height: 20,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to text if image not found
                  return const Icon(Icons.login, size: 20, color: Colors.blue);
                },
              ),
        label: Text(
          authState.isLoading ? 'Signing in...' : 'Continue with Google',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }
}

class GoogleSignInIconButton extends ConsumerWidget {
  final VoidCallback? onSuccess;
  final VoidCallback? onError;

  const GoogleSignInIconButton({super.key, this.onSuccess, this.onError});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: authState.isLoading
            ? null
            : () async {
                try {
                  await ref.read(authProvider.notifier).signInWithGoogle();
                  onSuccess?.call();
                } catch (e) {
                  onError?.call();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Google Sign-In failed: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
        icon: authState.isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Image.asset(
                'assets/icons/google_logo.png',
                width: 24,
                height: 24,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to icon if image not found
                  return const Icon(Icons.login, size: 24, color: Colors.blue);
                },
              ),
      ),
    );
  }
}
