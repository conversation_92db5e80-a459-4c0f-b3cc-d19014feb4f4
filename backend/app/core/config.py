"""
Configuration settings for WibeFit Backend
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    # App Configuration
    app_name: str = "WibeFit API"
    version: str = "1.0.0"
    debug: bool = True
    environment: str = "development"
    host: str = "0.0.0.0"
    port: int = 8000

    # Database Configuration (PostgreSQL)
    database_url: str = "postgresql://sanju@localhost:5432/wibefit_db"

    # JWT Configuration (for later Firebase integration)
    secret_key: str = "your-super-secret-jwt-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # CORS Configuration
    cors_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://localhost:*",  # For Flutter development
        "http://***********:*",  # For physical devices on local network
        "http://192.168.1.*:*",  # Allow all devices on local network
        "*",  # Allow all origins for development (remove in production)
    ]

    # Redis Configuration
    redis_url: str = "redis://localhost:6379"

    # Google Cloud Storage Configuration
    gcs_bucket_name: str = "wibefit-videos"
    gcs_project_id: str = ""
    google_application_credentials: str = "./gcs_credentials.json"

    # OpenRouter Configuration
    openrouter_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    openrouter_model: str = "anthropic/claude-3.5-sonnet"  # Default model

    # Firebase Configuration (for later)
    firebase_project_id: str = ""
    firebase_private_key_id: str = ""
    firebase_private_key: str = ""
    firebase_client_email: str = ""
    firebase_client_id: str = ""
    firebase_auth_uri: str = "https://accounts.google.com/o/oauth2/auth"
    firebase_token_uri: str = "https://oauth2.googleapis.com/token"

    # File Upload Configuration
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_video_extensions: List[str] = [".mp4", ".mov", ".avi", ".mkv"]
    allowed_image_extensions: List[str] = [".jpg", ".jpeg", ".png", ".gif"]

    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
