"""
Authentication API routes
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>
from typing import Optional
from pydantic import BaseModel

from ..core.config import settings
from ..core.database import get_db
from ..core.security import verify_password, get_password_hash, create_access_token, verify_token
from ..schemas.user import UserCreate, User<PERSON>ogin, User, Token, UserProfileUpdate
from ..models.user import User as UserModel, UserProfile

router = APIRouter(prefix="/auth", tags=["authentication"])
security = HTTPBearer()

# Database-only authentication (no more fake data)


def authenticate_user(email: str, password: str, db: Session):
    """Authenticate user with email and password using database only"""
    try:
        db_user = db.query(UserModel).filter(UserModel.email == email).first()
        if db_user and db_user.hashed_password and db_user.is_active:
            if verify_password(password, db_user.hashed_password):
                return {
                    "id": db_user.id,
                    "email": db_user.email,
                    "full_name": db_user.full_name,
                    "role": db_user.role,
                    "hashed_password": db_user.hashed_password,
                    "is_active": db_user.is_active,
                    "is_verified": db_user.is_verified,
                    "profile_picture": db_user.profile_picture,
                    "bio": db_user.bio,
                    "created_at": db_user.created_at.isoformat() if db_user.created_at else None
                }
        return False
    except Exception as e:
        print(f"Database authentication error: {e}")
        return False


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """Get current authenticated user from database"""
    try:
        payload = verify_token(credentials.credentials)
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Get user from database
        db_user = db.query(UserModel).filter(UserModel.email == email).first()
        if not db_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return {
            "id": db_user.id,
            "email": db_user.email,
            "full_name": db_user.full_name,
            "username": db_user.username,
            "role": db_user.role.value if hasattr(db_user.role, 'value') else str(db_user.role),
            "is_active": db_user.is_active,
            "is_verified": db_user.is_verified,
            "profile_picture": db_user.profile_picture,
            "bio": db_user.bio,
            "phone_number": db_user.phone_number,
            "timezone": db_user.timezone,
            "language": db_user.language,
            "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
            "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error getting current user: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)), db: Session = Depends(get_db)):
    """Get current authenticated user (optional - returns None if not authenticated)"""
    if credentials is None:
        return None

    try:
        payload = verify_token(credentials.credentials)
        email: str = payload.get("sub")
        if email is None:
            return None

        # Get user from database
        db_user = db.query(UserModel).filter(UserModel.email == email).first()
        if not db_user:
            return None

        return {
            "id": db_user.id,
            "email": db_user.email,
            "full_name": db_user.full_name,
            "username": db_user.username,
            "role": db_user.role.value if hasattr(db_user.role, 'value') else str(db_user.role),
            "is_active": db_user.is_active,
            "is_verified": db_user.is_verified,
            "profile_picture": db_user.profile_picture,
            "bio": db_user.bio,
            "phone_number": db_user.phone_number,
            "timezone": db_user.timezone,
            "language": db_user.language,
            "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
            "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
        }
    except:
        return None


@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, db: Session = Depends(get_db)):
    """Login user and return access token"""
    user = authenticate_user(user_credentials.email, user_credentials.password, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user["email"], "role": user["role"]},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "refresh_token": f"refresh_{user['id']}_{access_token[-10:]}",
        "user": {k: v for k, v in user.items() if k != "hashed_password"}
    }


@router.post("/register", response_model=User)
async def register(user: UserCreate, db: Session = Depends(get_db)):
    """Register new user"""
    # Check if user already exists in database
    existing_user = db.query(UserModel).filter(UserModel.email == user.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Hash password
    hashed_password = get_password_hash(user.password) if user.password else None

    # Create new user in database
    db_user = UserModel(
        email=user.email,
        full_name=user.full_name,
        username=user.username,
        hashed_password=hashed_password,
        role=user.role,
        bio=user.bio,
        phone_number=user.phone_number,
        timezone=user.timezone,
        language=user.language,
        is_active=True,
        is_verified=False
    )

    try:
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        # Create an empty user profile for the new user
        from ..models.user import UserProfile as UserProfileModel
        user_profile = UserProfileModel(user_id=db_user.id)
        db.add(user_profile)
        db.commit()
        db.refresh(user_profile)

        print(f"✅ User {user.email} successfully saved to database with ID: {db_user.id}")
        print(f"✅ User profile created for user ID: {db_user.id}")

        return User(
            id=db_user.id,
            email=db_user.email,
            full_name=db_user.full_name,
            username=db_user.username,
            role=db_user.role,
            is_active=db_user.is_active,
            is_verified=db_user.is_verified,
            profile_picture=db_user.profile_picture,
            bio=db_user.bio,
            phone_number=db_user.phone_number,
            timezone=db_user.timezone,
            language=db_user.language,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )

    except Exception as e:
        db.rollback()
        print(f"❌ Database error during user registration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {str(e)}"
        )


@router.get("/me", response_model=User)
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """Get current user profile"""
    return User(**{k: v for k, v in current_user.items() if k != "hashed_password"})


@router.put("/profile")
async def update_profile(profile_data: UserProfileUpdate, current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    """Update user profile data in database"""
    try:
        user_id = current_user["id"]

        # Get or create user profile
        user_profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()

        if not user_profile:
            # Create new profile if it doesn't exist
            user_profile = UserProfile(user_id=user_id)
            db.add(user_profile)

        # Update profile fields
        for field, value in profile_data.model_dump(exclude_unset=True).items():
            if hasattr(user_profile, field):
                setattr(user_profile, field, value)

        db.commit()
        db.refresh(user_profile)

        return {
            "message": "Profile updated successfully",
            "profile": {
                "id": user_profile.id,
                "user_id": user_profile.user_id,
                "age": user_profile.age,
                "height": user_profile.height,
                "current_weight": user_profile.current_weight,
                "target_weight": user_profile.target_weight,
                "fitness_level": user_profile.fitness_level,
                "activity_level": user_profile.activity_level,
                "primary_goals": user_profile.primary_goals,
                "medical_conditions": user_profile.medical_conditions,
                "dietary_restrictions": user_profile.dietary_restrictions,
                "preferred_workout_duration": user_profile.preferred_workout_duration,
                "preferred_workout_days": user_profile.preferred_workout_days,
                "equipment_access": user_profile.equipment_access,
                "created_at": user_profile.created_at.isoformat() if user_profile.created_at else None,
                "updated_at": user_profile.updated_at.isoformat() if user_profile.updated_at else None
            }
        }
    except Exception as e:
        db.rollback()
        print(f"Error updating profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )


@router.get("/profile")
async def get_profile(current_user: dict = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get user profile data from database"""
    try:
        user_id = current_user["id"]

        # Get user profile from database
        user_profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()

        if user_profile:
            return {
                "profile": {
                    "id": user_profile.id,
                    "user_id": user_profile.user_id,
                    "age": user_profile.age,
                    "height": user_profile.height,
                    "current_weight": user_profile.current_weight,
                    "target_weight": user_profile.target_weight,
                    "fitness_level": user_profile.fitness_level,
                    "activity_level": user_profile.activity_level,
                    "primary_goals": user_profile.primary_goals,
                    "medical_conditions": user_profile.medical_conditions,
                    "dietary_restrictions": user_profile.dietary_restrictions,
                    "preferred_workout_duration": user_profile.preferred_workout_duration,
                    "preferred_workout_days": user_profile.preferred_workout_days,
                    "equipment_access": user_profile.equipment_access,
                    "created_at": user_profile.created_at.isoformat() if user_profile.created_at else None,
                    "updated_at": user_profile.updated_at.isoformat() if user_profile.updated_at else None
                }
            }
        else:
            return {
                "profile": None,
                "message": "No profile data found. Profile will be created automatically."
            }
    except Exception as e:
        print(f"Error getting profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get profile: {str(e)}"
        )


# Firebase Authentication Schema
class FirebaseAuthRequest(BaseModel):
    id_token: str
    uid: str
    email: str
    name: str
    photo_url: Optional[str] = None
    role: Optional[str] = "member"


@router.post("/firebase", response_model=Token)
async def firebase_auth(firebase_data: FirebaseAuthRequest):
    """Authenticate user with Firebase ID token"""
    try:
        # In a real implementation, you would verify the Firebase ID token
        # For now, we'll create/login the user based on Firebase UID

        email = firebase_data.email
        name = firebase_data.name
        uid = firebase_data.uid

        # Check if user exists by email
        user = fake_users_db.get(email)

        if not user:
            # Create new user from Firebase data
            new_user = {
                "id": uid,  # Use Firebase UID as user ID
                "email": email,
                "full_name": name,
                "role": firebase_data.role,
                "hashed_password": None,  # No password for Firebase users
                "is_active": True,
                "is_verified": True,
                "profile_picture": firebase_data.photo_url,
                "bio": None,
                "created_at": "2024-01-01T00:00:00",
                "firebase_uid": uid
            }
            fake_users_db[email] = new_user
            user = new_user
        else:
            # Update existing user with Firebase UID if not set
            if "firebase_uid" not in user:
                user["firebase_uid"] = uid

        # Create access token
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user["email"], "role": user["role"], "firebase_uid": uid},
            expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "refresh_token": f"refresh_{user['id']}_{access_token[-10:]}",
            "user": {k: v for k, v in user.items() if k != "hashed_password"}
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Firebase authentication failed: {str(e)}"
        )


@router.post("/logout")
async def logout():
    """Logout user (client should remove token)"""
    return {"message": "Successfully logged out"}
