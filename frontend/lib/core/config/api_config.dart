import 'dart:io';

class ApiConfig {
  // Base URL for the API - platform-aware for development and production
  static String get baseUrl {
    // For development, use different URLs based on device type
    if (Platform.isAndroid) {
      // Check if running on emulator or physical device
      // For physical devices, use your computer's IP address
      // For emulators, use ********
      return _getAndroidBaseUrl();
    } else {
      // iOS simulator and other platforms can use localhost
      return 'http://localhost:8000';
    }
  }

  // Get the appropriate base URL for Android devices
  static String _getAndroidBaseUrl() {
    // Try to detect if running on emulator vs physical device
    // This is a simple heuristic - you may need to adjust based on your setup

    // Option 1: Use your computer's local IP address (works for physical devices)
    // Your computer's IP address on the local network
    const String localNetworkIp = '***********'; // Your actual computer's IP

    // Option 2: Use emulator localhost (works for emulators)
    const String emulatorIp = '********';

    // Try to detect if running on emulator vs physical device
    // For now, we'll use a smart fallback approach
    return 'http://$localNetworkIp:8000';
  }

  // Check if we should use offline mode
  static bool get useOfflineMode {
    // Always use offline mode for better performance
    return true;
  }

  // API endpoints
  static const String authEndpoint = '/api/auth';
  static const String workoutsEndpoint = '/api/workouts';
  static const String exercisesEndpoint = '/api/workouts/exercises';

  // Request timeout - reduced for better performance
  static const Duration requestTimeout = Duration(seconds: 3);
  static const Duration connectionTimeout = Duration(seconds: 2);
  static const Duration authTimeout = Duration(seconds: 3);

  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
}
