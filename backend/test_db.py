#!/usr/bin/env python3
"""
Test database connection and table creation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import create_tables, engine
from app.models import *  # Import all models
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database():
    """Test database connection and table creation"""
    try:
        logger.info("Testing database connection...")
        
        # Test engine connection
        with engine.connect() as conn:
            logger.info("✅ Database engine connection successful")
        
        # Create tables
        logger.info("Creating database tables...")
        create_tables()
        logger.info("✅ Database tables created successfully")
        
        # Check if database file was created (for SQLite)
        if "sqlite" in str(engine.url):
            db_file = str(engine.url).split("///")[-1]
            if os.path.exists(db_file):
                logger.info(f"✅ Database file created: {db_file}")
                file_size = os.path.getsize(db_file)
                logger.info(f"📊 Database file size: {file_size} bytes")
            else:
                logger.error(f"❌ Database file not found: {db_file}")
        
        logger.info("🎉 Database test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
