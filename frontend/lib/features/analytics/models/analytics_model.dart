class AnalyticsOverview {
  final int totalWorkouts;
  final int totalCalories;
  final int totalTimeMinutes;
  final int currentStreak;
  final List<int> weeklyProgress;
  final MonthlySummary monthlySummary;
  final List<Achievement> recentAchievements;

  const AnalyticsOverview({
    required this.totalWorkouts,
    required this.totalCalories,
    required this.totalTimeMinutes,
    required this.currentStreak,
    required this.weeklyProgress,
    required this.monthlySummary,
    required this.recentAchievements,
  });

  factory AnalyticsOverview.fromJson(Map<String, dynamic> json) {
    return AnalyticsOverview(
      totalWorkouts: json['total_workouts'],
      totalCalories: json['total_calories'],
      totalTimeMinutes: json['total_time_minutes'],
      currentStreak: json['current_streak'],
      weeklyProgress: List<int>.from(json['weekly_progress']),
      monthlySummary: MonthlySummary.fromJson(json['monthly_summary']),
      recentAchievements: (json['recent_achievements'] as List)
          .map((e) => Achievement.fromJson(e))
          .toList(),
    );
  }
}

class MonthlySummary {
  final int workoutsCompleted;
  final int caloriesBurned;
  final int averageWorkoutDuration;
  final String favoriteExerciseCategory;

  const MonthlySummary({
    required this.workoutsCompleted,
    required this.caloriesBurned,
    required this.averageWorkoutDuration,
    required this.favoriteExerciseCategory,
  });

  factory MonthlySummary.fromJson(Map<String, dynamic> json) {
    return MonthlySummary(
      workoutsCompleted: json['workouts_completed'],
      caloriesBurned: json['calories_burned'],
      averageWorkoutDuration: json['average_workout_duration'],
      favoriteExerciseCategory: json['favorite_exercise_category'],
    );
  }
}

class Achievement {
  final String title;
  final String description;
  final String icon;
  final DateTime earnedAt;

  const Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.earnedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      title: json['title'],
      description: json['description'],
      icon: json['icon'],
      earnedAt: DateTime.parse(json['earned_at']),
    );
  }
}

class WorkoutAnalytics {
  final String period;
  final int totalWorkouts;
  final int totalDurationMinutes;
  final int totalCalories;
  final int averageDuration;
  final List<DailyBreakdown>? dailyBreakdown;
  final List<WeeklyBreakdown>? weeklyBreakdown;
  final List<MonthlyBreakdown>? monthlyBreakdown;
  final Map<String, int> workoutTypes;
  final Map<String, int>? muscleGroupsTrained;
  final ImprovementMetrics improvementMetrics;

  const WorkoutAnalytics({
    required this.period,
    required this.totalWorkouts,
    required this.totalDurationMinutes,
    required this.totalCalories,
    required this.averageDuration,
    this.dailyBreakdown,
    this.weeklyBreakdown,
    this.monthlyBreakdown,
    required this.workoutTypes,
    this.muscleGroupsTrained,
    required this.improvementMetrics,
  });

  factory WorkoutAnalytics.fromJson(Map<String, dynamic> json) {
    return WorkoutAnalytics(
      period: json['period'],
      totalWorkouts: json['total_workouts'],
      totalDurationMinutes: json['total_duration_minutes'],
      totalCalories: json['total_calories'],
      averageDuration: json['average_duration'],
      dailyBreakdown: json['daily_breakdown'] != null
          ? (json['daily_breakdown'] as List)
              .map((e) => DailyBreakdown.fromJson(e))
              .toList()
          : null,
      weeklyBreakdown: json['weekly_breakdown'] != null
          ? (json['weekly_breakdown'] as List)
              .map((e) => WeeklyBreakdown.fromJson(e))
              .toList()
          : null,
      monthlyBreakdown: json['monthly_breakdown'] != null
          ? (json['monthly_breakdown'] as List)
              .map((e) => MonthlyBreakdown.fromJson(e))
              .toList()
          : null,
      workoutTypes: Map<String, int>.from(json['workout_types']),
      muscleGroupsTrained: json['muscle_groups_trained'] != null
          ? Map<String, int>.from(json['muscle_groups_trained'])
          : null,
      improvementMetrics: ImprovementMetrics.fromJson(json['improvement_metrics']),
    );
  }
}

class DailyBreakdown {
  final String date;
  final int workouts;
  final int duration;
  final int calories;

  const DailyBreakdown({
    required this.date,
    required this.workouts,
    required this.duration,
    required this.calories,
  });

  factory DailyBreakdown.fromJson(Map<String, dynamic> json) {
    return DailyBreakdown(
      date: json['date'],
      workouts: json['workouts'],
      duration: json['duration'],
      calories: json['calories'],
    );
  }
}

class WeeklyBreakdown {
  final int week;
  final int workouts;
  final int duration;
  final int calories;

  const WeeklyBreakdown({
    required this.week,
    required this.workouts,
    required this.duration,
    required this.calories,
  });

  factory WeeklyBreakdown.fromJson(Map<String, dynamic> json) {
    return WeeklyBreakdown(
      week: json['week'],
      workouts: json['workouts'],
      duration: json['duration'],
      calories: json['calories'],
    );
  }
}

class MonthlyBreakdown {
  final String month;
  final int workouts;
  final int duration;
  final int calories;

  const MonthlyBreakdown({
    required this.month,
    required this.workouts,
    required this.duration,
    required this.calories,
  });

  factory MonthlyBreakdown.fromJson(Map<String, dynamic> json) {
    return MonthlyBreakdown(
      month: json['month'],
      workouts: json['workouts'],
      duration: json['duration'],
      calories: json['calories'],
    );
  }
}

class ImprovementMetrics {
  final double consistency;
  final double durationTrend;
  final double intensityTrend;
  final double? strengthProgress;
  final double? enduranceProgress;

  const ImprovementMetrics({
    required this.consistency,
    required this.durationTrend,
    required this.intensityTrend,
    this.strengthProgress,
    this.enduranceProgress,
  });

  factory ImprovementMetrics.fromJson(Map<String, dynamic> json) {
    return ImprovementMetrics(
      consistency: json['consistency'].toDouble(),
      durationTrend: json['duration_trend'].toDouble(),
      intensityTrend: json['intensity_trend'].toDouble(),
      strengthProgress: json['strength_progress']?.toDouble(),
      enduranceProgress: json['endurance_progress']?.toDouble(),
    );
  }
}

class ProgressAnalytics {
  final String metric;
  final String period;
  final double? currentValue;
  final double? targetValue;
  final String? unit;
  final double? progressPercentage;
  final String? trend;
  final List<DataPoint> dataPoints;
  final List<String> insights;
  final Map<String, ExerciseProgress>? exercises;
  final Map<String, ActivityProgress>? activities;
  final Map<String, AssessmentProgress>? assessments;
  final int? vo2MaxEstimate;
  final int? restingHeartRate;
  final int? overallScore;

  const ProgressAnalytics({
    required this.metric,
    required this.period,
    this.currentValue,
    this.targetValue,
    this.unit,
    this.progressPercentage,
    this.trend,
    required this.dataPoints,
    required this.insights,
    this.exercises,
    this.activities,
    this.assessments,
    this.vo2MaxEstimate,
    this.restingHeartRate,
    this.overallScore,
  });

  factory ProgressAnalytics.fromJson(Map<String, dynamic> json) {
    return ProgressAnalytics(
      metric: json['metric'],
      period: json['period'],
      currentValue: json['current_value']?.toDouble(),
      targetValue: json['target_value']?.toDouble(),
      unit: json['unit'],
      progressPercentage: json['progress_percentage']?.toDouble(),
      trend: json['trend'],
      dataPoints: (json['data_points'] as List)
          .map((e) => DataPoint.fromJson(e))
          .toList(),
      insights: List<String>.from(json['insights']),
      exercises: json['exercises'] != null
          ? (json['exercises'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, ExerciseProgress.fromJson(value)))
          : null,
      activities: json['activities'] != null
          ? (json['activities'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, ActivityProgress.fromJson(value)))
          : null,
      assessments: json['assessments'] != null
          ? (json['assessments'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key, AssessmentProgress.fromJson(value)))
          : null,
      vo2MaxEstimate: json['vo2_max_estimate'],
      restingHeartRate: json['resting_heart_rate'],
      overallScore: json['overall_strength_score'] ?? json['overall_flexibility_score'],
    );
  }
}

class DataPoint {
  final String date;
  final double value;

  const DataPoint({
    required this.date,
    required this.value,
  });

  factory DataPoint.fromJson(Map<String, dynamic> json) {
    return DataPoint(
      date: json['date'],
      value: json['value'].toDouble(),
    );
  }
}

class ExerciseProgress {
  final double currentMax;
  final double startingMax;
  final String unit;
  final double improvement;
  final List<DataPoint> dataPoints;

  const ExerciseProgress({
    required this.currentMax,
    required this.startingMax,
    required this.unit,
    required this.improvement,
    required this.dataPoints,
  });

  factory ExerciseProgress.fromJson(Map<String, dynamic> json) {
    return ExerciseProgress(
      currentMax: json['current_max'].toDouble(),
      startingMax: json['starting_max'].toDouble(),
      unit: json['unit'],
      improvement: json['improvement'].toDouble(),
      dataPoints: (json['data_points'] as List)
          .map((e) => DataPoint.fromJson(e))
          .toList(),
    );
  }
}

class ActivityProgress {
  final String? bestTime;
  final String? startingTime;
  final double? maxDistance;
  final double? startingDistance;
  final String unit;
  final double improvement;
  final List<DataPoint> dataPoints;

  const ActivityProgress({
    this.bestTime,
    this.startingTime,
    this.maxDistance,
    this.startingDistance,
    required this.unit,
    required this.improvement,
    required this.dataPoints,
  });

  factory ActivityProgress.fromJson(Map<String, dynamic> json) {
    return ActivityProgress(
      bestTime: json['best_5k_time'],
      startingTime: json['starting_5k_time'],
      maxDistance: json['max_distance']?.toDouble(),
      startingDistance: json['starting_distance']?.toDouble(),
      unit: json['unit'] ?? 'minutes',
      improvement: json['improvement'].toDouble(),
      dataPoints: (json['data_points'] as List)
          .map((e) => DataPoint.fromJson(e))
          .toList(),
    );
  }
}

class AssessmentProgress {
  final double currentScore;
  final double startingScore;
  final String unit;
  final double improvement;
  final List<DataPoint> dataPoints;

  const AssessmentProgress({
    required this.currentScore,
    required this.startingScore,
    required this.unit,
    required this.improvement,
    required this.dataPoints,
  });

  factory AssessmentProgress.fromJson(Map<String, dynamic> json) {
    return AssessmentProgress(
      currentScore: json['current_score'].toDouble(),
      startingScore: json['starting_score'].toDouble(),
      unit: json['unit'],
      improvement: json['improvement'].toDouble(),
      dataPoints: (json['data_points'] as List)
          .map((e) => DataPoint.fromJson(e))
          .toList(),
    );
  }
}

class GoalAnalytics {
  final List<ActiveGoal> activeGoals;
  final List<CompletedGoal> completedGoals;
  final List<String> goalInsights;

  const GoalAnalytics({
    required this.activeGoals,
    required this.completedGoals,
    required this.goalInsights,
  });

  factory GoalAnalytics.fromJson(Map<String, dynamic> json) {
    return GoalAnalytics(
      activeGoals: (json['active_goals'] as List)
          .map((e) => ActiveGoal.fromJson(e))
          .toList(),
      completedGoals: (json['completed_goals'] as List)
          .map((e) => CompletedGoal.fromJson(e))
          .toList(),
      goalInsights: List<String>.from(json['goal_insights']),
    );
  }
}

class ActiveGoal {
  final int id;
  final String title;
  final String type;
  final double targetValue;
  final double currentProgress;
  final double progressPercentage;
  final String targetDate;
  final int daysRemaining;
  final bool onTrack;
  final List<double> weeklyProgress;

  const ActiveGoal({
    required this.id,
    required this.title,
    required this.type,
    required this.targetValue,
    required this.currentProgress,
    required this.progressPercentage,
    required this.targetDate,
    required this.daysRemaining,
    required this.onTrack,
    required this.weeklyProgress,
  });

  factory ActiveGoal.fromJson(Map<String, dynamic> json) {
    return ActiveGoal(
      id: json['id'],
      title: json['title'],
      type: json['type'],
      targetValue: json['target_value'].toDouble(),
      currentProgress: json['current_progress'].toDouble(),
      progressPercentage: json['progress_percentage'].toDouble(),
      targetDate: json['target_date'],
      daysRemaining: json['days_remaining'],
      onTrack: json['on_track'],
      weeklyProgress: List<double>.from(json['weekly_progress']),
    );
  }
}

class CompletedGoal {
  final int id;
  final String title;
  final String type;
  final String completedDate;
  final int achievementPoints;

  const CompletedGoal({
    required this.id,
    required this.title,
    required this.type,
    required this.completedDate,
    required this.achievementPoints,
  });

  factory CompletedGoal.fromJson(Map<String, dynamic> json) {
    return CompletedGoal(
      id: json['id'],
      title: json['title'],
      type: json['type'],
      completedDate: json['completed_date'],
      achievementPoints: json['achievement_points'],
    );
  }
}

class SocialAnalytics {
  final int friendsCount;
  final int followersCount;
  final int followingCount;
  final int workoutShares;
  final int likesReceived;
  final int commentsReceived;
  final int leaderboardPosition;
  final int challengesCompleted;
  final List<ActiveChallenge> activeChallenges;
  final List<FriendActivity> friendActivity;

  const SocialAnalytics({
    required this.friendsCount,
    required this.followersCount,
    required this.followingCount,
    required this.workoutShares,
    required this.likesReceived,
    required this.commentsReceived,
    required this.leaderboardPosition,
    required this.challengesCompleted,
    required this.activeChallenges,
    required this.friendActivity,
  });

  factory SocialAnalytics.fromJson(Map<String, dynamic> json) {
    return SocialAnalytics(
      friendsCount: json['friends_count'],
      followersCount: json['followers_count'],
      followingCount: json['following_count'],
      workoutShares: json['workout_shares'],
      likesReceived: json['likes_received'],
      commentsReceived: json['comments_received'],
      leaderboardPosition: json['leaderboard_position'],
      challengesCompleted: json['challenges_completed'],
      activeChallenges: (json['active_challenges'] as List)
          .map((e) => ActiveChallenge.fromJson(e))
          .toList(),
      friendActivity: (json['friend_activity'] as List)
          .map((e) => FriendActivity.fromJson(e))
          .toList(),
    );
  }
}

class ActiveChallenge {
  final int id;
  final String title;
  final int progress;
  final int target;
  final int participants;
  final int yourRank;
  final int daysRemaining;

  const ActiveChallenge({
    required this.id,
    required this.title,
    required this.progress,
    required this.target,
    required this.participants,
    required this.yourRank,
    required this.daysRemaining,
  });

  factory ActiveChallenge.fromJson(Map<String, dynamic> json) {
    return ActiveChallenge(
      id: json['id'],
      title: json['title'],
      progress: json['progress'],
      target: json['target'],
      participants: json['participants'],
      yourRank: json['your_rank'],
      daysRemaining: json['days_remaining'],
    );
  }
}

class FriendActivity {
  final String friendName;
  final String activity;
  final String time;
  final String? achievement;

  const FriendActivity({
    required this.friendName,
    required this.activity,
    required this.time,
    this.achievement,
  });

  factory FriendActivity.fromJson(Map<String, dynamic> json) {
    return FriendActivity(
      friendName: json['friend_name'],
      activity: json['activity'],
      time: json['time'],
      achievement: json['achievement'],
    );
  }
}

class UserStats {
  final int totalWorkouts;
  final double totalCaloriesBurned;
  final double totalWorkoutTimeMinutes;
  final int currentStreakDays;
  final int longestStreakDays;
  final String? favoriteExerciseCategory;
  final double? averageWorkoutDuration;
  final int achievementsCount;
  final int friendsCount;

  const UserStats({
    required this.totalWorkouts,
    required this.totalCaloriesBurned,
    required this.totalWorkoutTimeMinutes,
    required this.currentStreakDays,
    required this.longestStreakDays,
    this.favoriteExerciseCategory,
    this.averageWorkoutDuration,
    required this.achievementsCount,
    required this.friendsCount,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalWorkouts: json['total_workouts'],
      totalCaloriesBurned: json['total_calories_burned'].toDouble(),
      totalWorkoutTimeMinutes: json['total_workout_time_minutes'].toDouble(),
      currentStreakDays: json['current_streak_days'],
      longestStreakDays: json['longest_streak_days'],
      favoriteExerciseCategory: json['favorite_exercise_category'],
      averageWorkoutDuration: json['average_workout_duration']?.toDouble(),
      achievementsCount: json['achievements_count'],
      friendsCount: json['friends_count'],
    );
  }
}

class UserAnalytics {
  final List<int> weeklyWorkouts;
  final Map<String, dynamic> monthlyProgress;
  final List<Map<String, dynamic>> goalProgress;
  final List<Map<String, dynamic>> recentAchievements;

  const UserAnalytics({
    required this.weeklyWorkouts,
    required this.monthlyProgress,
    required this.goalProgress,
    required this.recentAchievements,
  });

  factory UserAnalytics.fromJson(Map<String, dynamic> json) {
    return UserAnalytics(
      weeklyWorkouts: List<int>.from(json['weekly_workouts']),
      monthlyProgress: Map<String, dynamic>.from(json['monthly_progress']),
      goalProgress: List<Map<String, dynamic>>.from(json['goal_progress']),
      recentAchievements: List<Map<String, dynamic>>.from(json['recent_achievements']),
    );
  }
}
