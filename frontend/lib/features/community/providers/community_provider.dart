import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/community_service.dart';

// Community service provider
final communityServiceProvider = Provider<CommunityService>((ref) {
  return CommunityService();
});

// Community posts provider
final communityPostsProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final communityService = ref.read(communityServiceProvider);
  return await communityService.getCommunityPosts();
});

// Active challenges provider
final activeChallengesProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final communityService = ref.read(communityServiceProvider);
  return await communityService.getActiveChallenges();
});

// Leaderboard provider
final leaderboardProvider = FutureProvider<List<Map<String, dynamic>>>((
  ref,
) async {
  final communityService = ref.read(communityServiceProvider);
  return await communityService.getLeaderboard();
});

// User friends provider
final userFriendsProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final communityService = ref.read(communityServiceProvider);
  return await communityService.getUserFriends();
});

// Post like action provider
final postLikeProvider = StateNotifierProvider.family<PostLikeNotifier, bool, String>(
  (ref, postId) => PostLikeNotifier(ref.read(communityServiceProvider), postId),
);

class PostLikeNotifier extends StateNotifier<bool> {
  final CommunityService _communityService;
  final String _postId;

  PostLikeNotifier(this._communityService, this._postId) : super(false);

  Future<void> toggleLike() async {
    final success = await _communityService.likePost(_postId);
    if (success) {
      state = !state;
    }
  }
}

// Challenge join action provider
final challengeJoinProvider = StateNotifierProvider.family<ChallengeJoinNotifier, bool, String>(
  (ref, challengeId) => ChallengeJoinNotifier(ref.read(communityServiceProvider), challengeId),
);

class ChallengeJoinNotifier extends StateNotifier<bool> {
  final CommunityService _communityService;
  final String _challengeId;

  ChallengeJoinNotifier(this._communityService, this._challengeId) : super(false);

  Future<void> toggleJoin() async {
    final success = await _communityService.joinChallenge(_challengeId);
    if (success) {
      state = !state;
    }
  }
}
