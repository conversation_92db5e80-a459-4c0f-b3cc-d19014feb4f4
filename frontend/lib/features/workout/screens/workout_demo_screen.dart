import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../../exercise/models/exercise_model.dart';
import '../../exercise/providers/exercise_provider.dart';
import 'workout_execution_screen.dart';

class WorkoutDemoScreen extends StatelessWidget {
  const WorkoutDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const _OptimizedWorkoutDemoContent();
  }
}

class _OptimizedWorkoutDemoContent extends ConsumerStatefulWidget {
  const _OptimizedWorkoutDemoContent();

  @override
  ConsumerState<_OptimizedWorkoutDemoContent> createState() =>
      _OptimizedWorkoutDemoContentState();
}

class _OptimizedWorkoutDemoContentState
    extends ConsumerState<_OptimizedWorkoutDemoContent> {
  bool _exercisesLoaded = false;

  @override
  void initState() {
    super.initState();
    // Load exercises only once, in background
    _loadExercisesAsync();
  }

  void _loadExercisesAsync() async {
    if (!_exercisesLoaded) {
      _exercisesLoaded = true;
      // Load in background without blocking UI
      Future.microtask(() {
        if (mounted) {
          ref.read(exerciseProvider.notifier).loadExercises();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final exerciseState = ref.watch(exerciseProvider);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        title: const Text(
          'Workout Demo',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Test the Complete Workout System',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'This demo includes timer, calorie tracking, exercise logging, and progress tracking.',
                style: TextStyle(color: Colors.black87, fontSize: 16),
              ),
              const SizedBox(height: 24),
              _buildDemoWorkoutCard(
                context,
                title: 'Quick Upper Body Workout',
                description: '3 exercises • 15 min • ~120 calories',
                exercises: _getUpperBodyExercises(exerciseState.exercises),
                icon: Icons.fitness_center,
                color: Colors.blue,
              ),
              const SizedBox(height: 16),
              _buildDemoWorkoutCard(
                context,
                title: 'Core Strength Session',
                description: '2 exercises • 10 min • ~80 calories',
                exercises: _getCoreExercises(exerciseState.exercises),
                icon: Icons.self_improvement,
                color: Colors.green,
              ),
              const SizedBox(height: 16),
              _buildDemoWorkoutCard(
                context,
                title: 'Full Body Quick Burn',
                description: '4 exercises • 20 min • ~180 calories',
                exercises: _getFullBodyExercises(exerciseState.exercises),
                icon: Icons.local_fire_department,
                color: Colors.orange,
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.cardColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🏋️ Features Included:',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• Real-time timer with pause/resume\n'
                      '• Automatic calorie calculation\n'
                      '• Exercise performance logging\n'
                      '• Progress tracking & statistics\n'
                      '• Rest timer between sets\n'
                      '• Workout completion tracking',
                      style: TextStyle(color: Colors.black87, fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDemoWorkoutCard(
    BuildContext context, {
    required String title,
    required String description,
    required List<Exercise> exercises,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _startWorkout(context, title, exercises),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 30),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _startWorkout(
    BuildContext context,
    String workoutName,
    List<Exercise> exercises,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutExecutionScreen(
          exercises: exercises,
          workoutName: workoutName,
        ),
      ),
    );
  }

  List<Exercise> _getUpperBodyExercises(List<Exercise> allExercises) {
    final upperBodyExercises = allExercises
        .where((exercise) => exercise.category == 'upper_body')
        .take(3)
        .toList();

    // If no database exercises, return fallback
    if (upperBodyExercises.isEmpty) {
      return [
        Exercise(
          id: 1,
          name: 'Push-ups',
          slug: 'push-ups',
          description: 'Classic bodyweight chest exercise',
          instructions: [
            'Start in plank position',
            'Lower chest to ground',
            'Push back up',
          ],
          tips: ['Keep core tight', 'Full range of motion'],
          muscleGroups: ['Chest', 'Arms', 'Core'],
          equipment: ['None'],
          difficulty: 'beginner',
          exerciseType: 'strength',
          category: 'upper_body',
          durationMinutes: 5,
          caloriesPerMinute: 8.0,
          videoUrl:
              'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
          isActive: true,
          createdAt: DateTime.now(),
        ),
        Exercise(
          id: 3,
          name: 'Dumbbell Bench Press',
          slug: 'dumbbell-bench-press',
          description: 'Upper body strength exercise',
          instructions: [
            'Lie on bench with dumbbells',
            'Press weights up',
            'Lower with control',
          ],
          tips: ['Keep feet on floor', 'Squeeze chest at top'],
          muscleGroups: ['Chest', 'Arms', 'Shoulders'],
          equipment: ['Dumbbells'],
          difficulty: 'intermediate',
          exerciseType: 'strength',
          category: 'upper_body',
          durationMinutes: 10,
          caloriesPerMinute: 7.0,
          videoUrl:
              'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
          isActive: true,
          createdAt: DateTime.now(),
        ),
        Exercise(
          id: 6,
          name: 'Barbell Larsen Bench Press',
          slug: 'barbell-larsen-bench-press',
          description: 'Advanced bench press variation with feet elevated',
          instructions: [
            'Lie on bench with feet elevated on bench',
            'Grip barbell with hands wider than shoulders',
            'Lower bar to chest with control',
            'Press bar up explosively',
            'Keep core tight throughout movement',
          ],
          tips: [
            'Keep feet elevated throughout set',
            'Focus on chest activation',
            'Control the negative portion',
            'Don\'t bounce bar off chest',
          ],
          muscleGroups: ['Chest', 'Shoulders', 'Triceps'],
          equipment: ['Barbell', 'Bench'],
          difficulty: 'advanced',
          exerciseType: 'strength',
          category: 'upper_body',
          durationMinutes: 8,
          caloriesPerMinute: 9.0,
          videoUrl: null,
          isActive: true,
          createdAt: DateTime.now(),
        ),
      ];
    }

    return upperBodyExercises;
  }

  List<Exercise> _getCoreExercises(List<Exercise> allExercises) {
    final coreExercises = allExercises
        .where((exercise) => exercise.category == 'core')
        .take(2)
        .toList();

    if (coreExercises.isNotEmpty) return coreExercises;

    // Fallback exercises
    return [
      Exercise(
        id: 5,
        name: 'Plank',
        slug: 'plank',
        description: 'Core stability exercise',
        instructions: [
          'Start in push-up position',
          'Hold position',
          'Keep body straight',
        ],
        tips: ['Engage core', 'Don\'t let hips sag'],
        muscleGroups: ['Core', 'Shoulders'],
        equipment: ['None'],
        difficulty: 'beginner',
        exerciseType: 'strength',
        category: 'core',
        durationMinutes: 3,
        caloriesPerMinute: 4.0,
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4',
        isActive: true,
        createdAt: DateTime.now(),
      ),
    ];
  }

  List<Exercise> _getFullBodyExercises(List<Exercise> allExercises) {
    final fullBodyExercises = allExercises
        .where(
          (exercise) =>
              exercise.category == 'full_body' ||
              exercise.category == 'lower_body',
        )
        .take(4)
        .toList();

    if (fullBodyExercises.isNotEmpty) return fullBodyExercises;

    // Fallback exercises
    return [
      Exercise(
        id: 2,
        name: 'Squats',
        slug: 'squats',
        description: 'Fundamental lower body exercise',
        instructions: [
          'Stand with feet shoulder-width apart',
          'Lower hips back and down',
          'Return to standing',
        ],
        tips: ['Keep knees behind toes', 'Chest up'],
        muscleGroups: ['Legs', 'Glutes', 'Core'],
        equipment: ['None'],
        difficulty: 'beginner',
        exerciseType: 'strength',
        category: 'lower_body',
        durationMinutes: 5,
        caloriesPerMinute: 6.0,
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
        isActive: true,
        createdAt: DateTime.now(),
      ),
      Exercise(
        id: 4,
        name: 'Deadlifts',
        slug: 'deadlifts',
        description: 'Compound full-body exercise',
        instructions: [
          'Stand with feet hip-width apart',
          'Grip barbell',
          'Lift by extending hips and knees',
        ],
        tips: ['Keep back straight', 'Drive through heels'],
        muscleGroups: ['Back', 'Legs', 'Glutes', 'Core'],
        equipment: ['Barbell'],
        difficulty: 'intermediate',
        exerciseType: 'strength',
        category: 'full_body',
        durationMinutes: 15,
        caloriesPerMinute: 9.0,
        videoUrl:
            'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_2mb.mp4',
        isActive: true,
        createdAt: DateTime.now(),
      ),
    ];
  }
}
