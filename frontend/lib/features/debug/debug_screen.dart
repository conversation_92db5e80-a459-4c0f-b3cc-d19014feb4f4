import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../../core/services/mock_api_service.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  Map<String, dynamic>? _debugInfo;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() => _isLoading = true);
    try {
      final debugInfo = await MockApiService.getDebugInfo();
      setState(() => _debugInfo = debugInfo);
    } catch (e) {
      print('Error loading debug info: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearAllData() async {
    await MockApiService.clearProfile();
    await _loadDebugInfo();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All data cleared'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Information'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.science),
            onPressed: () {
              context.push('/test-storage');
            },
            tooltip: 'Test Storage',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
          ),
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: _clearAllData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Cards
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatusCard(
                          'Profile Data',
                          _debugInfo?['profile_exists'] == true,
                          _debugInfo?['profile_exists'] == true
                              ? 'Stored'
                              : 'Not Found',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildStatusCard(
                          'Auth Data',
                          _debugInfo?['auth_exists'] == true,
                          _debugInfo?['auth_exists'] == true
                              ? 'Stored'
                              : 'Not Found',
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Profile Data Section
                  if (_debugInfo?['profile_data'] != null) ...[
                    _buildSectionHeader('Profile Data'),
                    _buildDataCard(
                      'Fitness Profile',
                      _debugInfo!['profile_data'],
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Auth Data Section
                  if (_debugInfo?['auth_data'] != null) ...[
                    _buildSectionHeader('Authentication Data'),
                    _buildDataCard(
                      'User Information',
                      _debugInfo!['auth_data'],
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Debug Info Section
                  _buildSectionHeader('Debug Information'),
                  _buildDataCard('System Info', {
                    'timestamp': _debugInfo?['timestamp'],
                    'profile_exists': _debugInfo?['profile_exists'],
                    'auth_exists': _debugInfo?['auth_exists'],
                  }),

                  const SizedBox(height: 24),

                  // Instructions
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'How to Use This Debug Screen:',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade800,
                              ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '1. Complete your fitness profile setup\n'
                          '2. Return to this screen to verify data is stored\n'
                          '3. Tap any data card to copy to clipboard\n'
                          '4. Use refresh button to reload data\n'
                          '5. Use delete button to clear all stored data',
                          style: TextStyle(height: 1.5),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusCard(String title, bool isSuccess, String status) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSuccess ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSuccess ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Column(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? Colors.green : Colors.red,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          Text(
            status,
            style: TextStyle(
              color: isSuccess ? Colors.green.shade700 : Colors.red.shade700,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildDataCard(String title, Map<String, dynamic> data) {
    final dataString = _formatData(data);

    return Card(
      child: InkWell(
        onTap: () => _copyToClipboard(dataString),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const Icon(Icons.copy, size: 16, color: Colors.grey),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  dataString,
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatData(Map<String, dynamic> data) {
    final buffer = StringBuffer();
    data.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString().trim();
  }
}
