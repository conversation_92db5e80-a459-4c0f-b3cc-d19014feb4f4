"""
WibeFit Backend API
FastAPI application for the WibeFit fitness app with PostgreSQL, OpenRouter, and Google Cloud Storage
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import logging
from datetime import datetime

from app.core.config import settings
from app.core.database import create_tables
from app.models import *  # Import all models to register them

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="Backend API for WibeFit - AI-powered fitness companion with PostgreSQL, OpenRouter AI, and Google Cloud Storage",
    version=settings.version,
    debug=settings.debug
)

# CORS middleware for Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Startup initialization
def initialize_services():
    """Initialize database and services"""
    try:
        # Create database tables
        create_tables()
        logger.info("Database tables created successfully")

        # Initialize services
        from app.core.gcs_storage import gcs_service
        from app.core.openrouter import openrouter_service

        if gcs_service.client:
            logger.info("Google Cloud Storage service initialized")
        else:
            logger.warning("Google Cloud Storage service not available")

        if openrouter_service.client:
            logger.info("OpenRouter AI service initialized")
        else:
            logger.warning("OpenRouter AI service not available")

    except Exception as e:
        logger.error(f"Startup error: {e}")

# Initialize on import
initialize_services()


# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {
        "message": "WibeFit API is running!",
        "version": settings.version,
        "environment": settings.environment,
        "services": {
            "database": "PostgreSQL",
            "ai": "OpenRouter",
            "storage": "Google Cloud Storage"
        }
    }


@app.get("/health")
async def health_check():
    """Detailed health check endpoint"""
    from app.core.gcs_storage import gcs_service
    from app.core.openrouter import openrouter_service

    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "database": "connected",
            "gcs": "connected" if gcs_service.client else "disconnected",
            "openrouter": "connected" if openrouter_service.client else "disconnected"
        }
    }

# Include API routers
from app.api.auth import router as auth_router
from app.api.users import router as users_router
from app.api.analytics import router as analytics_router
from app.api.community import router as community_router

# Include routers
app.include_router(auth_router, prefix="/api")
app.include_router(users_router)
app.include_router(analytics_router)
app.include_router(community_router)
# Note: exercises router is included via workouts router

# Legacy endpoints removed to avoid conflicts with routers


@app.get("/api/workouts")
async def get_workouts_legacy():
    """Legacy workout endpoint"""
    workouts = [
        {
            "id": 1,
            "name": "Morning Cardio",
            "description": "High-intensity cardio workout to start your day",
            "difficulty": "intermediate",
            "duration_minutes": 30,
            "category": "cardio",
            "exercises": [
                {"exercise_id": 1, "sets": 3, "reps": 15},
                {"exercise_id": 2, "sets": 3, "reps": 20}
            ]
        }
    ]
    return {"workouts": workouts}


if __name__ == "__main__":
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
