# WibeFit App - Comprehensive .gitignore
# This file specifies which files and directories should be ignored by Git

# ============================================================================
# SYSTEM FILES
# ============================================================================
# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
*.tmp
*.temp
*.swp
*.swo
*~

# ============================================================================
# DEVELOPMENT ENVIRONMENT
# ============================================================================
# IDE and Editor Files
.vscode/
.idea/
*.iml
*.ipr
*.iws
.atom/
.sublime-project
.sublime-workspace

# ============================================================================
# FLUTTER & DART
# ============================================================================
# Flutter Build Files
**/build/
**/android/app/debug/
**/android/app/profile/
**/android/app/release/

# Flutter Generated Files
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
**/doc/api/
**/ios/Flutter/.last_build_id
.flutter-plugins-dependencies

# Flutter Web
**/web/build/

# Flutter Desktop
**/linux/flutter/ephemeral/
**/macos/Flutter/ephemeral/
**/windows/flutter/ephemeral/

# ============================================================================
# MOBILE PLATFORMS
# ============================================================================
# Android
**/android/.gradle/
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
*.jks
*.keystore

# iOS
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/.last_build_id
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# ============================================================================
# BACKEND & DATABASE
# ============================================================================
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.env
.venv

# Database Files
*.db
*.sqlite
*.sqlite3
*.db-journal

# Backend Logs
*.log
logs/

# Backend Build/Distribution
dist/
build/
*.egg-info/

# ============================================================================
# DEPENDENCIES & PACKAGES
# ============================================================================
# Node.js (if using)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package Managers
.pnp
.pnp.js

# ============================================================================
# SECURITY & CREDENTIALS
# ============================================================================
# API Keys and Secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config/secrets.yaml
config/keys.yaml
*.pem
*.key
*.crt
*.p12
*.pfx

# Firebase
google-services.json
GoogleService-Info.plist
firebase-debug.log

# ============================================================================
# TESTING & COVERAGE
# ============================================================================
# Test Coverage
coverage/
*.lcov
.nyc_output

# Test Results
test-results/
junit.xml

# ============================================================================
# DEPLOYMENT & CI/CD
# ============================================================================
# Docker
.dockerignore
Dockerfile.prod

# CI/CD
.github/workflows/secrets/

# ============================================================================
# DOCUMENTATION & ASSETS
# ============================================================================
# Generated Documentation
**/doc/api/
docs/build/

# Large Media Files (optional - uncomment if needed)
# *.mp4
# *.mov
# *.avi
# *.mkv
# *.webm

# ============================================================================
# PROJECT SPECIFIC
# ============================================================================
# WibeFit Specific Files
**/wibefit_*.db
**/test_*.db
backend/alembic/versions/*.py
!backend/alembic/versions/__init__.py

# APK Files (uncomment if you don't want to track APKs)
# *.apk
# *.aab

# Development Scripts
run_dev.*
setup_*.sh

# Backup Files
*.backup
*.bak
*_backup.*

# ============================================================================
# IMPORTANT FILES TO TRACK
# ============================================================================
# These files should always be included in version control:
# - requirements.txt (project requirements)
# - setup.sh (Unix/Linux/macOS setup script)
# - setup.bat (Windows setup script)
# - pubspec.yaml (Flutter dependencies)
# - README.md (project documentation)
# - .gitignore (this file)

# Make sure setup scripts are tracked
!setup.sh
!setup.bat
!requirements.txt
