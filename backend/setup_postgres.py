#!/usr/bin/env python3
"""
Setup PostgreSQL database for WibeFit
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database():
    """Create the WibeFit database if it doesn't exist"""
    
    # Database connection parameters
    # Update these if your PostgreSQL setup is different
    DB_HOST = "localhost"
    DB_PORT = "5432"
    DB_USER = "sanju"  # Your macOS username
    DB_PASSWORD = ""   # No password needed for local Postgres.app
    DB_NAME = "wibefit_db"
    
    try:
        # Connect to PostgreSQL server (not to a specific database)
        logger.info("Connecting to PostgreSQL server...")
        if DB_PASSWORD:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                password=DB_PASSWORD,
                database="postgres"  # Connect to default postgres database
            )
        else:
            conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                database="postgres"  # Connect to default postgres database
            )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (DB_NAME,))
        exists = cursor.fetchone()
        
        if exists:
            logger.info(f"✅ Database '{DB_NAME}' already exists")
        else:
            # Create database
            cursor.execute(f'CREATE DATABASE "{DB_NAME}"')
            logger.info(f"✅ Database '{DB_NAME}' created successfully")
        
        cursor.close()
        conn.close()
        
        # Test connection to the new database
        logger.info("Testing connection to WibeFit database...")
        if DB_PASSWORD:
            test_conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME
            )
        else:
            test_conn = psycopg2.connect(
                host=DB_HOST,
                port=DB_PORT,
                user=DB_USER,
                database=DB_NAME
            )
        test_conn.close()
        logger.info("✅ Successfully connected to WibeFit database")
        
        return True
        
    except psycopg2.Error as e:
        logger.error(f"❌ PostgreSQL error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

def print_connection_info():
    """Print connection information"""
    print("\n" + "="*60)
    print("🐘 PostgreSQL Connection Information")
    print("="*60)
    print("Database URL: postgresql://sanju@localhost:5432/wibefit_db")
    print("Host: localhost")
    print("Port: 5432")
    print("Database: wibefit_db")
    print("User: sanju")
    print("\n📝 Note: No password required for local Postgres.app setup")
    print("="*60)

def main():
    """Main setup function"""
    logger.info("🚀 Setting up PostgreSQL database for WibeFit...")
    
    if create_database():
        print_connection_info()
        logger.info("🎉 PostgreSQL setup completed successfully!")
        logger.info("Next steps:")
        logger.info("1. Update database password in backend/app/core/config.py if needed")
        logger.info("2. Run: python3 run_server.py")
        logger.info("3. Database tables will be created automatically")
        return True
    else:
        logger.error("❌ PostgreSQL setup failed!")
        logger.error("Please check:")
        logger.error("1. PostgreSQL is installed and running")
        logger.error("2. Username and password are correct")
        logger.error("3. PostgreSQL is accepting connections on localhost:5432")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
