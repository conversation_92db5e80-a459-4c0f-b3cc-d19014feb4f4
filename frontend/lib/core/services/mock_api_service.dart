import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

/// Mock API service for testing when backend is not available
class MockApiService {
  static const String _profileKey = 'mock_user_profile';
  static const String _userKey = 'mock_user_data';

  /// Save user profile data locally (simulating database save)
  static Future<bool> saveUserProfile(Map<String, dynamic> profileData) async {
    try {
      print('🔄 MockApiService: Saving profile data...');
      print('📝 Profile data: $profileData');

      final prefs = await SharedPreferences.getInstance();

      // Add timestamp and ID
      final enrichedData = {
        ...profileData,
        'id': Random().nextInt(1000),
        'user_id': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Save to local storage
      await prefs.setString(_profileKey, json.encode(enrichedData));

      print('✅ MockApiService: Profile saved successfully');
      print('💾 Saved data: $enrichedData');

      return true;
    } catch (e) {
      print('❌ MockApiService: Error saving profile: $e');
      return false;
    }
  }

  /// Get user profile data from local storage
  static Future<Map<String, dynamic>?> getUserProfile() async {
    try {
      print('🔍 MockApiService: Loading profile data...');

      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_profileKey);

      if (profileJson != null) {
        final profileData = json.decode(profileJson) as Map<String, dynamic>;
        print('✅ MockApiService: Profile loaded successfully');
        print('📄 Loaded data: $profileData');
        return profileData;
      } else {
        print('ℹ️ MockApiService: No profile data found');
        return null;
      }
    } catch (e) {
      print('❌ MockApiService: Error loading profile: $e');
      return null;
    }
  }

  /// Check if profile exists
  static Future<bool> hasProfile() async {
    final profile = await getUserProfile();
    return profile != null;
  }

  /// Clear profile data
  static Future<void> clearProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_profileKey);
      print('🗑️ MockApiService: Profile data cleared');
    } catch (e) {
      print('❌ MockApiService: Error clearing profile: $e');
    }
  }

  /// Save user authentication data
  static Future<bool> saveUserAuth(Map<String, dynamic> userData) async {
    try {
      print('🔄 MockApiService: Saving user auth data...');

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, json.encode(userData));

      print('✅ MockApiService: User auth saved successfully');
      return true;
    } catch (e) {
      print('❌ MockApiService: Error saving user auth: $e');
      return false;
    }
  }

  /// Get user authentication data
  static Future<Map<String, dynamic>?> getUserAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);

      if (userJson != null) {
        return json.decode(userJson) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('❌ MockApiService: Error loading user auth: $e');
      return null;
    }
  }

  /// Mock login
  static Future<Map<String, dynamic>> mockLogin(
    String email,
    String password,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    final userData = {
      'id': 1,
      'email': email,
      'full_name': 'Demo User',
      'role': 'member',
      'is_active': true,
      'is_verified': true,
      'created_at': DateTime.now().toIso8601String(),
    };

    await saveUserAuth(userData);

    return {
      'access_token': 'mock_token_${Random().nextInt(10000)}',
      'token_type': 'bearer',
      'user': userData,
    };
  }

  /// Mock registration
  static Future<Map<String, dynamic>> mockRegister(
    String email,
    String password,
    String name,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    final userData = {
      'id': 1,
      'email': email,
      'full_name': name,
      'role': 'member',
      'is_active': true,
      'is_verified': true,
      'created_at': DateTime.now().toIso8601String(),
    };

    await saveUserAuth(userData);

    return {
      'access_token': 'mock_token_${Random().nextInt(10000)}',
      'token_type': 'bearer',
      'user': userData,
    };
  }

  /// Save workout session data
  static Future<bool> saveWorkoutSession(
    Map<String, dynamic> sessionData,
  ) async {
    try {
      print('🔄 MockApiService: Saving workout session...');
      print('📝 Session data: $sessionData');

      final prefs = await SharedPreferences.getInstance();

      // Get existing sessions
      final existingSessions = await getWorkoutHistory(
        sessionData['user_id'] ?? '',
      );

      // Add new session
      existingSessions.add(sessionData);

      // Save updated sessions
      final sessionsKey = 'workout_sessions_${sessionData['user_id']}';
      await prefs.setString(sessionsKey, json.encode(existingSessions));

      print('✅ MockApiService: Workout session saved successfully');
      return true;
    } catch (e) {
      print('❌ MockApiService: Error saving workout session: $e');
      return false;
    }
  }

  /// Get workout history for user
  static Future<List<Map<String, dynamic>>> getWorkoutHistory(
    String userId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsKey = 'workout_sessions_$userId';
      final sessionsJson = prefs.getString(sessionsKey);

      if (sessionsJson != null) {
        final sessionsList = json.decode(sessionsJson) as List<dynamic>;
        return sessionsList.map((s) => s as Map<String, dynamic>).toList();
      }
      return [];
    } catch (e) {
      print('❌ MockApiService: Error loading workout history: $e');
      return [];
    }
  }

  /// Save body weight measurement
  static Future<bool> saveBodyWeight(String userId, double weight) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final weightsKey = 'body_weights_$userId';

      // Get existing weights
      final existingWeights = await getBodyWeights(userId);

      // Add new weight measurement
      final weightEntry = {
        'weight': weight,
        'date': DateTime.now().toIso8601String(),
      };
      existingWeights.add(weightEntry);

      // Keep only last 30 measurements
      if (existingWeights.length > 30) {
        existingWeights.removeRange(0, existingWeights.length - 30);
      }

      await prefs.setString(weightsKey, json.encode(existingWeights));

      print('✅ MockApiService: Body weight saved successfully');
      return true;
    } catch (e) {
      print('❌ MockApiService: Error saving body weight: $e');
      return false;
    }
  }

  /// Get body weight history
  static Future<List<Map<String, dynamic>>> getBodyWeights(
    String userId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final weightsKey = 'body_weights_$userId';
      final weightsJson = prefs.getString(weightsKey);

      if (weightsJson != null) {
        final weightsList = json.decode(weightsJson) as List<dynamic>;
        return weightsList.map((w) => w as Map<String, dynamic>).toList();
      }
      return [];
    } catch (e) {
      print('❌ MockApiService: Error loading body weights: $e');
      return [];
    }
  }

  /// Get workout analytics
  static Future<Map<String, dynamic>> getWorkoutAnalytics(String userId) async {
    try {
      final sessions = await getWorkoutHistory(userId);
      final weights = await getBodyWeights(userId);

      // Calculate analytics
      final totalWorkouts = sessions.length;
      final totalMinutes = sessions.fold<int>(
        0,
        (sum, session) => sum + (session['duration_minutes'] as int? ?? 0),
      );
      final totalCalories = sessions.fold<int>(
        0,
        (sum, session) => sum + (session['calories_burned'] as int? ?? 0),
      );

      final averageDuration = totalWorkouts > 0
          ? totalMinutes / totalWorkouts
          : 0.0;

      // Calculate streak (simplified)
      int currentStreak = 0;
      if (sessions.isNotEmpty) {
        final lastSession = sessions.last;
        final lastWorkoutDate = DateTime.parse(lastSession['start_time']);
        final daysSinceLastWorkout = DateTime.now()
            .difference(lastWorkoutDate)
            .inDays;
        currentStreak = daysSinceLastWorkout <= 1 ? 1 : 0;
      }

      return {
        'total_workouts': totalWorkouts,
        'total_minutes': totalMinutes,
        'total_calories': totalCalories,
        'average_workout_duration': averageDuration,
        'current_streak': currentStreak,
        'longest_streak': currentStreak, // Simplified
        'weekly_weights': weights.take(7).map((w) => w['weight']).toList(),
        'weekly_calories': sessions
            .take(7)
            .map((s) => s['calories_burned'] ?? 0)
            .toList(),
        'last_workout': sessions.isNotEmpty
            ? sessions.last['start_time']
            : DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ MockApiService: Error calculating analytics: $e');
      return {
        'total_workouts': 0,
        'total_minutes': 0,
        'total_calories': 0,
        'average_workout_duration': 0.0,
        'current_streak': 0,
        'longest_streak': 0,
        'weekly_weights': [],
        'weekly_calories': [],
        'last_workout': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get all stored data for debugging
  static Future<Map<String, dynamic>> getDebugInfo() async {
    final profile = await getUserProfile();
    final auth = await getUserAuth();
    final userId = auth?['id']?.toString() ?? 'demo_user';
    final workouts = await getWorkoutHistory(userId);
    final analytics = await getWorkoutAnalytics(userId);

    return {
      'profile_exists': profile != null,
      'auth_exists': auth != null,
      'profile_data': profile,
      'auth_data': auth,
      'workout_sessions': workouts.length,
      'analytics': analytics,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
