import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/optimized_image.dart';
import '../../exercise/providers/exercise_provider.dart';
import '../../exercise/models/exercise_model.dart';
import '../../exercise/screens/exercise_detail_screen.dart';

class ExerciseListScreen extends ConsumerWidget {
  const ExerciseListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const _ExerciseListContent();
  }
}

class _ExerciseListContent extends ConsumerStatefulWidget {
  const _ExerciseListContent();

  @override
  ConsumerState<_ExerciseListContent> createState() =>
      _ExerciseListContentState();
}

class _ExerciseListContentState extends ConsumerState<_ExerciseListContent> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedTab; // No default selection, MY is just a label
  String? _selectedAreaFilter; // No default selection
  String? _selectedEquipmentFilter; // No default selection
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load exercises and favorites when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(exerciseProvider.notifier).loadExercises(refresh: true);
      // Initialize favorites provider to ensure it's loaded
      ref.read(favoritesProvider);
    });

    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      // Load more when 80% scrolled
      ref.read(exerciseProvider.notifier).loadMore();
    }
  }

  void _applyFilters() {
    final filters = ExerciseFilters(
      search: _searchController.text.isNotEmpty ? _searchController.text : null,
      muscleGroups: _selectedAreaFilter != null
          ? [_selectedAreaFilter!.toLowerCase()]
          : null,
      equipment: _selectedEquipmentFilter != null
          ? [_selectedEquipmentFilter!.toLowerCase()]
          : null,
      isFavorite: _selectedTab == 'Favorites' ? true : null,
    );

    ref.read(exerciseProvider.notifier).applyFilters(filters);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.textPrimary),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Exercise List',
          style: TextStyle(
            color: AppTheme.textPrimary,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          // Light divider line after search bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            height: 1,
            color: AppTheme.dividerColor.withValues(alpha: 0.3),
          ),
          _buildFilterTabs(),
          // Light divider line between MY section and Area section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            height: 1,
            color: AppTheme.dividerColor.withValues(alpha: 0.3),
          ),
          _buildFilterChips(),
          _buildTotalCount(),
          Expanded(child: _buildExerciseList()),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search exercise by name',
          prefixIcon: Icon(Icons.search, color: AppTheme.textSecondary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: AppTheme.textSecondary),
                  onPressed: () {
                    _searchController.clear();
                    _applyFilters();
                  },
                )
              : null,
          filled: true,
          fillColor: AppTheme.surfaceColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppTheme.dividerColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppTheme.dividerColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: AppTheme.primaryColor),
          ),
        ),
        onChanged: (value) => _applyFilters(),
      ),
    );
  }

  Widget _buildFilterTabs() {
    final tabs = ['Favorites', 'Recent']; // Removed 'MY' as it's just a label

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          // Static "MY" label
          Container(
            height: 44, // Explicit height to ensure proper sizing
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Center(
              child: Text(
                'MY',
                style: TextStyle(
                  color: AppTheme.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Interactive filter tabs
          ...tabs.map((tab) {
            final isSelected = _selectedTab == tab;
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    // Allow deselecting if already selected
                    if (_selectedTab == tab) {
                      _selectedTab = null; // Deselect
                    } else {
                      _selectedTab = tab;
                    }
                  });
                  _applyFilters();
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      tab,
                      style: TextStyle(
                        color: isSelected ? Colors.white : AppTheme.textPrimary,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    // Area filters (muscle groups)
    final areaFilters = ['Chest', 'Back', 'Leg', 'Shoulder', 'Triceps'];
    // Equipment filters
    final equipmentFilters = ['Barbell', 'Dumbbell', 'Kettlebell'];

    return Column(
      children: [
        // Area filters row
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Static "Area" label
              Container(
                height: 60, // Match parent container height
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Center(
                  child: Text(
                    'Area',
                    style: TextStyle(
                      color: AppTheme.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Scrollable area filter buttons
              Expanded(
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: areaFilters.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 12),
                  itemBuilder: (context, index) {
                    final filter = areaFilters[index];
                    final isSelected = _selectedAreaFilter == filter;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          // Allow deselecting if already selected
                          if (_selectedAreaFilter == filter) {
                            _selectedAreaFilter = null; // Deselect
                          } else {
                            _selectedAreaFilter = filter;
                          }
                        });
                        _applyFilters();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 18,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppTheme.primaryColor
                              : AppTheme.surfaceColor,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? AppTheme.primaryColor
                                : AppTheme.dividerColor,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              filter,
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : AppTheme.textPrimary,
                                fontSize: 14,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 6),
                              Icon(Icons.close, size: 14, color: Colors.white),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),

        // Light divider line
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          height: 1,
          color: AppTheme.dividerColor.withValues(alpha: 0.3),
        ),

        // Equipment filters row
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Static "Equipment" label
              Container(
                height: 60, // Match parent container height
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Center(
                  child: Text(
                    'Equipment',
                    style: TextStyle(
                      color: AppTheme.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Scrollable equipment filter buttons
              Expanded(
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: equipmentFilters.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(width: 12),
                  itemBuilder: (context, index) {
                    final filter = equipmentFilters[index];
                    final isSelected = _selectedEquipmentFilter == filter;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          // Allow deselecting if already selected
                          if (_selectedEquipmentFilter == filter) {
                            _selectedEquipmentFilter = null; // Deselect
                          } else {
                            _selectedEquipmentFilter = filter;
                          }
                        });
                        _applyFilters();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 18,
                          vertical: 10,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppTheme.secondaryColor
                              : AppTheme.surfaceColor,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? AppTheme.secondaryColor
                                : AppTheme.dividerColor,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              filter,
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : AppTheme.textPrimary,
                                fontSize: 14,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(width: 6),
                              Icon(Icons.close, size: 14, color: Colors.white),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTotalCount() {
    final exerciseState = ref.watch(exerciseProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Text(
              _selectedAreaFilter != null && _selectedEquipmentFilter != null
                  ? '$_selectedAreaFilter • $_selectedEquipmentFilter'
                  : _selectedAreaFilter ??
                        _selectedEquipmentFilter ??
                        'All Exercises',
              style: TextStyle(
                color: AppTheme.textPrimary,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Total ${exerciseState.total}',
            style: TextStyle(color: AppTheme.textSecondary, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseList() {
    final exerciseState = ref.watch(exerciseProvider);

    if (exerciseState.isLoading && exerciseState.exercises.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (exerciseState.error != null && exerciseState.exercises.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.textSecondary),
            const SizedBox(height: 16),
            Text(
              'Failed to load exercises',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Please check your connection and try again',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref
                  .read(exerciseProvider.notifier)
                  .loadExercises(refresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (exerciseState.exercises.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: AppTheme.textSecondary),
            const SizedBox(height: 16),
            Text(
              'No exercises found',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      itemCount:
          exerciseState.exercises.length + (exerciseState.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= exerciseState.exercises.length) {
          // Loading indicator for pagination
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final exercise = exerciseState.exercises[index];
        return _buildExerciseItem(exercise);
      },
    );
  }

  Widget _buildExerciseItem(Exercise exercise) {
    final isFavorite = ref.watch(isExerciseFavoriteProvider(exercise.id));
    final toggleFavorite = ref.watch(toggleFavoriteProvider(exercise.id));

    return GestureDetector(
      onTap: () => _navigateToExerciseGuide(exercise),
      behavior: HitTestBehavior.opaque, // Ensures entire area is tappable
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(
          24,
        ), // Increased padding for better touch area
        constraints: const BoxConstraints(
          minHeight: 80, // Minimum touch target height
        ),
        decoration: BoxDecoration(
          color: AppTheme.cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            // Exercise Image/Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: exercise.hasImage
                  ? OptimizedImage(
                      imageUrl: exercise.imageUrl,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      borderRadius: BorderRadius.circular(8),
                      cacheWidth: 100,
                      cacheHeight: 100,
                      errorWidget: Icon(
                        _getExerciseIcon(exercise.name),
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                      placeholder: Icon(
                        _getExerciseIcon(exercise.name),
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    )
                  : Icon(
                      _getExerciseIcon(exercise.name),
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
            ),
            const SizedBox(width: 16),

            // Exercise Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: TextStyle(
                      color: AppTheme.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${exercise.primaryMuscleGroup} • ${exercise.primaryEquipment}',
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Flexible(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getDifficultyColor(
                              exercise.difficulty,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            exercise.difficultyFormatted,
                            style: TextStyle(
                              color: _getDifficultyColor(exercise.difficulty),
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      if (exercise.hasVideo) ...[
                        const SizedBox(width: 6),
                        Icon(
                          Icons.play_circle_outline,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            // Favorite Button
            SizedBox(
              width: 48, // Fixed width to prevent overflow
              child: IconButton(
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite
                      ? AppTheme.errorColor
                      : AppTheme.textSecondary,
                  size: 20,
                ),
                onPressed: () {
                  print(
                    'Toggling favorite for exercise ${exercise.id}: $isFavorite -> ${!isFavorite}',
                  );
                  toggleFavorite();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToExerciseGuide(Exercise exercise) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExerciseDetailScreen(exercise: exercise),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      case 'expert':
        return Colors.purple;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _getExerciseIcon(String exerciseName) {
    // Return different icons based on exercise type
    final name = exerciseName.toLowerCase();
    if (name.contains('bench') || name.contains('press')) {
      return Icons.fitness_center;
    } else if (name.contains('pullover') || name.contains('fly')) {
      return Icons.accessibility_new;
    } else if (name.contains('push')) {
      return Icons.pan_tool;
    } else if (name.contains('dips')) {
      return Icons.keyboard_arrow_down;
    } else if (name.contains('cable') || name.contains('crossover')) {
      return Icons.cable;
    } else if (name.contains('machine')) {
      return Icons.precision_manufacturing;
    } else if (name.contains('decline')) {
      return Icons.trending_down;
    } else {
      return Icons.fitness_center;
    }
  }
}
