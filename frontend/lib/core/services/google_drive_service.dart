import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

/// Service for integrating with Google Drive to fetch exercise videos and data
class GoogleDriveService {
  // Google Drive API configuration
  static const String _driveApiBase = 'https://www.googleapis.com/drive/v3';
  static const String _driveFileBase = 'https://drive.google.com/uc?export=download&id=';
  
  // You'll need to set up Google Drive API credentials
  static String? _apiKey;
  static String? _accessToken;
  
  /// Initialize with API credentials
  static void initialize({String? apiKey, String? accessToken}) {
    _apiKey = apiKey;
    _accessToken = accessToken;
  }
  
  /// Get list of video files from a Google Drive folder
  static Future<List<DriveVideoFile>> getVideoFilesFromFolder(
    String folderId, {
    List<String> supportedFormats = const ['mp4', 'mov', 'avi', 'mkv', 'webm'],
  }) async {
    print('📹 Fetching video files from Google Drive folder: $folderId');
    
    try {
      // For demo purposes, return sample data
      // In production, implement actual Google Drive API calls
      return _getDemoVideoFiles();
      
      /* Actual implementation would be:
      final url = '$_driveApiBase/files?q=\'$folderId\' in parents and trashed=false';
      final headers = {
        'Authorization': 'Bearer $_accessToken',
        'Content-Type': 'application/json',
      };
      
      final response = await http.get(Uri.parse(url), headers: headers);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final files = data['files'] as List;
        
        return files
            .where((file) => _isVideoFile(file['name'], supportedFormats))
            .map((file) => DriveVideoFile.fromJson(file))
            .toList();
      } else {
        throw Exception('Failed to fetch files: ${response.statusCode}');
      }
      */
    } catch (e) {
      print('❌ Error fetching video files: $e');
      rethrow;
    }
  }
  
  /// Download and parse PDF file for exercise descriptions
  static Future<Map<String, ExerciseDescription>> getExerciseDescriptionsFromPdf(
    String pdfFileId,
  ) async {
    print('📄 Fetching exercise descriptions from PDF: $pdfFileId');
    
    try {
      // For demo purposes, return sample descriptions
      // In production, implement PDF parsing
      return _getDemoDescriptions();
      
      /* Actual implementation would be:
      final downloadUrl = '$_driveFileBase$pdfFileId';
      final response = await http.get(Uri.parse(downloadUrl));
      
      if (response.statusCode == 200) {
        // Parse PDF content using a PDF parsing library
        // Extract exercise names and descriptions
        return _parsePdfContent(response.bodyBytes);
      } else {
        throw Exception('Failed to download PDF: ${response.statusCode}');
      }
      */
    } catch (e) {
      print('❌ Error fetching PDF descriptions: $e');
      rethrow;
    }
  }
  
  /// Check if file is a video based on extension
  static bool _isVideoFile(String filename, List<String> supportedFormats) {
    final extension = path.extension(filename).toLowerCase().replaceAll('.', '');
    return supportedFormats.contains(extension);
  }
  
  /// Parse exercise name and metadata from filename
  static ExerciseMetadata parseExerciseFromFilename(String filename) {
    // Expected format: "ExerciseName_Level_BodyPart_Type.mp4"
    // Example: "Push_Up_Beginner_Chest_Strength.mp4"
    
    final nameWithoutExt = path.basenameWithoutExtension(filename);
    final parts = nameWithoutExt.split('_');
    
    String exerciseName = 'Unknown Exercise';
    String level = 'Beginner';
    String bodyPart = 'Full Body';
    String exerciseType = 'Strength';
    
    if (parts.isNotEmpty) {
      // First parts are exercise name (join with spaces)
      final nameParts = <String>[];
      for (int i = 0; i < parts.length; i++) {
        final part = parts[i];
        
        // Check if this part looks like a level, body part, or type
        if (_isLevel(part) || _isBodyPart(part) || _isExerciseType(part)) {
          // Extract metadata from remaining parts
          if (i < parts.length - 2) level = parts[i];
          if (i < parts.length - 1) bodyPart = parts[i + 1];
          if (i < parts.length) exerciseType = parts[i + 2];
          break;
        } else {
          nameParts.add(part);
        }
      }
      
      if (nameParts.isNotEmpty) {
        exerciseName = nameParts.join(' ');
      }
    }
    
    return ExerciseMetadata(
      name: exerciseName,
      level: level,
      bodyPart: bodyPart,
      exerciseType: exerciseType,
      originalFilename: filename,
    );
  }
  
  /// Check if string represents a difficulty level
  static bool _isLevel(String text) {
    const levels = ['beginner', 'intermediate', 'advanced', 'expert'];
    return levels.contains(text.toLowerCase());
  }
  
  /// Check if string represents a body part
  static bool _isBodyPart(String text) {
    const bodyParts = [
      'chest', 'back', 'shoulders', 'arms', 'legs', 'core', 'abs',
      'biceps', 'triceps', 'glutes', 'quads', 'hamstrings', 'calves'
    ];
    return bodyParts.contains(text.toLowerCase());
  }
  
  /// Check if string represents an exercise type
  static bool _isExerciseType(String text) {
    const types = [
      'strength', 'cardio', 'flexibility', 'balance', 'endurance',
      'hiit', 'yoga', 'pilates', 'stretching'
    ];
    return types.contains(text.toLowerCase());
  }
  
  /// Get demo video files for testing
  static List<DriveVideoFile> _getDemoVideoFiles() {
    return [
      DriveVideoFile(
        id: 'demo_video_1',
        name: 'Push_Up_Beginner_Chest_Strength.mp4',
        size: 15728640, // 15MB
        mimeType: 'video/mp4',
        downloadUrl: 'https://drive.google.com/uc?export=download&id=demo_video_1',
        thumbnailUrl: 'https://drive.google.com/thumbnail?id=demo_video_1&sz=w400-h300',
      ),
      DriveVideoFile(
        id: 'demo_video_2',
        name: 'Squat_Intermediate_Legs_Strength.mp4',
        size: 22020096, // 21MB
        mimeType: 'video/mp4',
        downloadUrl: 'https://drive.google.com/uc?export=download&id=demo_video_2',
        thumbnailUrl: 'https://drive.google.com/thumbnail?id=demo_video_2&sz=w400-h300',
      ),
      DriveVideoFile(
        id: 'demo_video_3',
        name: 'Deadlift_Advanced_Back_Strength.mp4',
        size: 31457280, // 30MB
        mimeType: 'video/mp4',
        downloadUrl: 'https://drive.google.com/uc?export=download&id=demo_video_3',
        thumbnailUrl: 'https://drive.google.com/thumbnail?id=demo_video_3&sz=w400-h300',
      ),
      DriveVideoFile(
        id: 'demo_video_4',
        name: 'Plank_Beginner_Core_Strength.mp4',
        size: 12582912, // 12MB
        mimeType: 'video/mp4',
        downloadUrl: 'https://drive.google.com/uc?export=download&id=demo_video_4',
        thumbnailUrl: 'https://drive.google.com/thumbnail?id=demo_video_4&sz=w400-h300',
      ),
      DriveVideoFile(
        id: 'demo_video_5',
        name: 'Burpee_Advanced_Full_Body_HIIT.mp4',
        size: 25165824, // 24MB
        mimeType: 'video/mp4',
        downloadUrl: 'https://drive.google.com/uc?export=download&id=demo_video_5',
        thumbnailUrl: 'https://drive.google.com/thumbnail?id=demo_video_5&sz=w400-h300',
      ),
    ];
  }
  
  /// Get demo exercise descriptions
  static Map<String, ExerciseDescription> _getDemoDescriptions() {
    return {
      'push_up': ExerciseDescription(
        name: 'Push Up',
        description: 'A fundamental upper body exercise that targets the chest, shoulders, and triceps. Start in a plank position with hands shoulder-width apart, lower your body until your chest nearly touches the ground, then push back up.',
        instructions: [
          'Start in a plank position with hands shoulder-width apart',
          'Keep your body in a straight line from head to heels',
          'Lower your body until your chest nearly touches the ground',
          'Push back up to the starting position',
          'Repeat for desired number of repetitions'
        ],
        tips: [
          'Keep your core engaged throughout the movement',
          'Don\'t let your hips sag or pike up',
          'Control the descent - don\'t drop down quickly',
          'Breathe in on the way down, out on the way up'
        ],
        muscleGroups: ['Chest', 'Triceps', 'Shoulders', 'Core'],
        equipment: ['None'],
      ),
      'squat': ExerciseDescription(
        name: 'Squat',
        description: 'A compound lower body exercise that targets the quadriceps, hamstrings, and glutes. Stand with feet shoulder-width apart and lower your body as if sitting back into a chair.',
        instructions: [
          'Stand with feet shoulder-width apart',
          'Keep your chest up and core engaged',
          'Lower your body by bending at the hips and knees',
          'Go down until your thighs are parallel to the ground',
          'Push through your heels to return to standing'
        ],
        tips: [
          'Keep your knees in line with your toes',
          'Don\'t let your knees cave inward',
          'Keep your weight on your heels',
          'Maintain a neutral spine throughout'
        ],
        muscleGroups: ['Quadriceps', 'Hamstrings', 'Glutes', 'Core'],
        equipment: ['None'],
      ),
      // Add more descriptions as needed...
    };
  }
}

/// Represents a video file from Google Drive
class DriveVideoFile {
  final String id;
  final String name;
  final int size;
  final String mimeType;
  final String downloadUrl;
  final String thumbnailUrl;
  
  const DriveVideoFile({
    required this.id,
    required this.name,
    required this.size,
    required this.mimeType,
    required this.downloadUrl,
    required this.thumbnailUrl,
  });
  
  factory DriveVideoFile.fromJson(Map<String, dynamic> json) {
    return DriveVideoFile(
      id: json['id'],
      name: json['name'],
      size: int.parse(json['size'] ?? '0'),
      mimeType: json['mimeType'],
      downloadUrl: 'https://drive.google.com/uc?export=download&id=${json['id']}',
      thumbnailUrl: 'https://drive.google.com/thumbnail?id=${json['id']}&sz=w400-h300',
    );
  }
}

/// Represents exercise metadata parsed from filename
class ExerciseMetadata {
  final String name;
  final String level;
  final String bodyPart;
  final String exerciseType;
  final String originalFilename;
  
  const ExerciseMetadata({
    required this.name,
    required this.level,
    required this.bodyPart,
    required this.exerciseType,
    required this.originalFilename,
  });
}

/// Represents exercise description from PDF
class ExerciseDescription {
  final String name;
  final String description;
  final List<String> instructions;
  final List<String> tips;
  final List<String> muscleGroups;
  final List<String> equipment;
  
  const ExerciseDescription({
    required this.name,
    required this.description,
    required this.instructions,
    required this.tips,
    required this.muscleGroups,
    required this.equipment,
  });
}
