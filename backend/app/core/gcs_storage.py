"""
Google Cloud Storage service for video and image uploads
"""

import os
import uuid
from typing import Op<PERSON>, BinaryIO
from google.cloud import storage
from google.cloud.exceptions import NotFound
import logging

from .config import settings

logger = logging.getLogger(__name__)


class GCSService:
    """Google Cloud Storage service for file operations"""
    
    def __init__(self):
        """Initialize GCS client"""
        try:
            # Initialize the client
            if os.path.exists(settings.google_application_credentials):
                self.client = storage.Client.from_service_account_json(
                    settings.google_application_credentials
                )
            else:
                # Use default credentials (for production with service account)
                self.client = storage.Client(project=settings.gcs_project_id)
            
            self.bucket_name = settings.gcs_bucket_name
            self.bucket = self.client.bucket(self.bucket_name)
            logger.info(f"GCS client initialized for bucket: {self.bucket_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            self.client = None
            self.bucket = None
    
    def upload_file(
        self, 
        file_data: BinaryIO, 
        filename: str, 
        content_type: str,
        folder: str = "uploads"
    ) -> Optional[str]:
        """
        Upload a file to Google Cloud Storage
        
        Args:
            file_data: Binary file data
            filename: Original filename
            content_type: MIME type of the file
            folder: Folder path in the bucket
            
        Returns:
            Public URL of the uploaded file or None if failed
        """
        if not self.client or not self.bucket:
            logger.error("GCS client not initialized")
            return None
        
        try:
            # Generate unique filename
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            blob_name = f"{folder}/{unique_filename}"
            
            # Create blob and upload
            blob = self.bucket.blob(blob_name)
            blob.upload_from_file(file_data, content_type=content_type)
            
            # Make the blob publicly accessible
            blob.make_public()
            
            logger.info(f"File uploaded successfully: {blob_name}")
            return blob.public_url
            
        except Exception as e:
            logger.error(f"Failed to upload file: {e}")
            return None
    
    def upload_video(self, file_data: BinaryIO, filename: str) -> Optional[str]:
        """Upload a video file to the videos folder"""
        return self.upload_file(file_data, filename, "video/mp4", "videos")
    
    def upload_image(self, file_data: BinaryIO, filename: str) -> Optional[str]:
        """Upload an image file to the images folder"""
        content_type = "image/jpeg"
        if filename.lower().endswith('.png'):
            content_type = "image/png"
        elif filename.lower().endswith('.gif'):
            content_type = "image/gif"
        
        return self.upload_file(file_data, filename, content_type, "images")
    
    def upload_exercise_video(self, file_data: BinaryIO, filename: str, exercise_id: str) -> Optional[str]:
        """Upload an exercise instruction video"""
        return self.upload_file(file_data, filename, "video/mp4", f"exercises/{exercise_id}")
    
    def delete_file(self, file_url: str) -> bool:
        """
        Delete a file from Google Cloud Storage
        
        Args:
            file_url: Public URL of the file to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        if not self.client or not self.bucket:
            logger.error("GCS client not initialized")
            return False
        
        try:
            # Extract blob name from URL
            blob_name = file_url.split(f"{self.bucket_name}/")[-1]
            blob = self.bucket.blob(blob_name)
            blob.delete()
            
            logger.info(f"File deleted successfully: {blob_name}")
            return True
            
        except NotFound:
            logger.warning(f"File not found for deletion: {file_url}")
            return False
        except Exception as e:
            logger.error(f"Failed to delete file: {e}")
            return False
    
    def get_signed_url(self, blob_name: str, expiration_minutes: int = 60) -> Optional[str]:
        """
        Generate a signed URL for private file access
        
        Args:
            blob_name: Name of the blob in the bucket
            expiration_minutes: URL expiration time in minutes
            
        Returns:
            Signed URL or None if failed
        """
        if not self.client or not self.bucket:
            logger.error("GCS client not initialized")
            return None
        
        try:
            blob = self.bucket.blob(blob_name)
            url = blob.generate_signed_url(
                expiration=expiration_minutes * 60,  # Convert to seconds
                method="GET"
            )
            return url
            
        except Exception as e:
            logger.error(f"Failed to generate signed URL: {e}")
            return None
    
    def list_files(self, prefix: str = "") -> list:
        """
        List files in the bucket with optional prefix filter
        
        Args:
            prefix: Prefix to filter files
            
        Returns:
            List of file names
        """
        if not self.client or not self.bucket:
            logger.error("GCS client not initialized")
            return []
        
        try:
            blobs = self.bucket.list_blobs(prefix=prefix)
            return [blob.name for blob in blobs]
            
        except Exception as e:
            logger.error(f"Failed to list files: {e}")
            return []


# Global GCS service instance
gcs_service = GCSService()
