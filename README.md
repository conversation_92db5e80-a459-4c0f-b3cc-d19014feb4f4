# 🏋️ WibeFit - AI-Powered Fitness Tracking App

<div align="center">

![WibeFit Logo](https://img.shields.io/badge/WibeFit-v1.0-blue?style=for-the-badge&logo=flutter)
[![Flutter](https://img.shields.io/badge/Flutter-3.x-blue?style=flat-square&logo=flutter)](https://flutter.dev)
[![Python](https://img.shields.io/badge/Python-3.8+-green?style=flat-square&logo=python)](https://python.org)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue?style=flat-square&logo=postgresql)](https://postgresql.org)

**A comprehensive fitness tracking application with AI-powered workout recommendations, real-time analytics, and social features.**

[Features](#-features) • [Quick Start](#-quick-start) • [Installation](#-installation) • [API Documentation](#-api-documentation) • [Contributing](#-contributing)

</div>

---

## 🌟 Features

### 🔐 **Authentication & User Management**
- **Multi-platform Login**: Email/password, Google Sign-In, Firebase integration
- **Profile Setup**: Comprehensive fitness profile with goals, equipment, preferences
- **Offline Support**: Works without internet connection with local data storage

### 🏃 **Workout Tracking & Analytics**
- **Real-time Timer**: Live workout tracking with exercise-by-exercise timing
- **Calorie Calculation**: Automatic calorie burn calculation based on body weight and duration
- **Progress Analytics**: Detailed workout history, statistics, and progress visualization
- **AI Recommendations**: Personalized 4-day workout plans based on user profile

### 📊 **Data Management**
- **Local Storage**: SQLite database for offline functionality
- **Cloud Sync**: PostgreSQL backend for data synchronization
- **Export/Import**: Backup and restore workout data
- **Real-time Updates**: Live data synchronization across devices

### 🎯 **Exercise Library**
- **1000+ Exercises**: Comprehensive exercise database with instructions
- **Video Demonstrations**: Exercise videos with proper form guidance
- **Equipment Filtering**: Filter exercises by available equipment
- **Difficulty Levels**: Beginner to advanced exercise variations

### 👥 **Community Features**
- **Social Feed**: Share workouts and achievements with community
- **Challenges**: Participate in fitness challenges and competitions
- **Leaderboards**: Track rankings and compete with friends
- **Achievement System**: Unlock badges and milestones

---

## 🚀 Quick Start

### **Option 1: Install APK (Fastest)**
1. Download the latest APK from releases
2. Install on Android device
3. Open app and create account
4. Complete profile setup
5. Start your first workout!

### **Option 2: Run from Source**
```bash
# Clone repository
git clone <your-repo-url>
cd wibefit_app

# Install dependencies
cd frontend && flutter pub get

# Run app
flutter run
```

---

## 🛠 Installation

### **Prerequisites**
- **Flutter SDK**: 3.0+ ([Install Guide](https://docs.flutter.dev/get-started/install))
- **Python**: 3.8+ ([Download](https://python.org/downloads/))
- **PostgreSQL**: 13+ (Optional - for backend database)
- **Android Studio** or **Xcode** for mobile development

### **1. Clone Repository**
```bash
git clone <your-repository-url>
cd wibefit_app
```

### **2. Frontend Setup (Flutter)**
```bash
cd frontend

# Install dependencies
flutter pub get

# Run on device/emulator
flutter run

# Build APK for Android
flutter build apk --release

# Build for iOS
flutter build ios --release
```

### **3. Backend Setup (Python)**
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run PostgreSQL server
python wibefit_postgresql_server.py

# Or run SQLite server
python wibefit_v1_server.py
```

### **4. Database Setup (Optional)**
```bash
# PostgreSQL setup
createdb wibefit_db

# Update database credentials in backend/wibefit_postgresql_server.py
DB_CONFIG = {
    'host': 'localhost',
    'database': 'wibefit_db',
    'user': 'your_username',
    'password': 'your_password',
    'port': 5432
}
```

---

## 📁 Project Structure

```
wibefit_app/
├── 📱 frontend/                    # Flutter Mobile App
│   ├── lib/
│   │   ├── core/                   # Core functionality
│   │   │   ├── config/             # App configuration
│   │   │   ├── services/           # API services
│   │   │   └── utils/              # Utility functions
│   │   ├── features/               # Feature modules
│   │   │   ├── auth/               # Authentication
│   │   │   ├── profile/            # User profile
│   │   │   ├── workouts/           # Workout tracking
│   │   │   ├── exercises/          # Exercise library
│   │   │   ├── analytics/          # Progress analytics
│   │   │   └── community/          # Social features
│   │   ├── shared/                 # Shared components
│   │   │   ├── models/             # Data models
│   │   │   ├── widgets/            # Reusable widgets
│   │   │   └── providers/          # State management
│   │   └── main.dart               # App entry point
│   ├── assets/                     # Images, fonts, etc.
│   ├── android/                    # Android configuration
│   ├── ios/                        # iOS configuration
│   └── pubspec.yaml                # Flutter dependencies
│
├── 🖥️ backend/                     # Python API Server
│   ├── app/                        # FastAPI application
│   ├── wibefit_postgresql_server.py # PostgreSQL server
│   ├── wibefit_v1_server.py        # SQLite server
│   ├── requirements.txt            # Python dependencies
│   └── README.md                   # Backend documentation
│
├── 📋 test_registration.py         # Registration testing script
├── 🔧 .gitignore                   # Git ignore rules
└── 📖 README.md                    # This file
```

---

## 🔧 Tech Stack

### **Frontend (Flutter)**
| Component | Technology | Purpose |
|-----------|------------|---------|
| **Framework** | Flutter 3.x | Cross-platform mobile development |
| **Language** | Dart | Programming language |
| **State Management** | Riverpod | Reactive state management |
| **Local Database** | SQLite | Offline data storage |
| **Authentication** | Firebase Auth | User authentication |
| **UI Framework** | Material Design 3 | Modern UI components |
| **HTTP Client** | Dio | API communication |
| **Local Storage** | SharedPreferences | Simple key-value storage |

### **Backend (Python)**
| Component | Technology | Purpose |
|-----------|------------|---------|
| **Framework** | Custom HTTP Server | Lightweight API server |
| **Language** | Python 3.8+ | Server-side programming |
| **Database** | PostgreSQL / SQLite | Data persistence |
| **Authentication** | JWT Tokens | Secure authentication |
| **API Style** | RESTful | Standard API architecture |
| **Data Format** | JSON | Data exchange format |

---

## 📱 App Features in Detail

### **🔐 Authentication System**
- **Multiple Login Options**: Email/password, Google Sign-In
- **Secure Storage**: JWT tokens with refresh mechanism
- **Offline Mode**: Works without internet connection
- **Profile Management**: Comprehensive user profile setup

### **🏋️ Workout Tracking**
- **Real-time Timer**: Live tracking during workouts
- **Exercise Library**: 1000+ exercises with instructions
- **Progress Analytics**: Detailed statistics and charts
- **AI Recommendations**: Personalized workout plans

### **📊 Data & Analytics**
- **Workout History**: Complete exercise logs
- **Progress Tracking**: Weight, reps, duration trends
- **Calorie Calculation**: Automatic burn estimation
- **Export Options**: Data backup and sharing

### **👥 Social Features**
- **Community Feed**: Share achievements and workouts
- **Challenges**: Participate in fitness competitions
- **Leaderboards**: Compare progress with friends
- **Achievement System**: Unlock badges and milestones

---

## 🌐 API Documentation

### **Authentication Endpoints**
```http
POST /api/auth/register    # User registration
POST /api/auth/login       # User login
GET  /api/auth/me          # Get current user
POST /api/auth/profile     # Update user profile
```

### **Workout Endpoints**
```http
GET  /api/workouts         # Get user workouts
POST /api/workout/session  # Save workout session
GET  /api/exercises        # Get exercise library
POST /api/body-weight      # Log body weight
```

### **Community Endpoints**
```http
GET  /api/community/posts      # Get community posts
GET  /api/community/challenges # Get active challenges
GET  /api/community/leaderboard # Get leaderboard
```

---

## 🚀 Development

### **Running in Development Mode**
```bash
# Frontend (Flutter)
cd frontend
flutter run --debug

# Backend (Python)
cd backend
python wibefit_postgresql_server.py
```

### **Building for Production**
```bash
# Android APK
flutter build apk --release

# iOS App
flutter build ios --release
```

### **Testing**
```bash
# Flutter tests
cd frontend
flutter test

# Python tests
cd backend
python -m pytest
```

---

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and test thoroughly
4. **Commit your changes**: `git commit -m 'Add amazing feature'`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### **Development Guidelines**
- Follow Flutter/Dart style guidelines
- Write tests for new features
- Update documentation as needed
- Ensure cross-platform compatibility

---

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

---

## 🆘 Support

- **Issues**: [GitHub Issues](../../issues)
- **Discussions**: [GitHub Discussions](../../discussions)
- **Email**: <EMAIL>

---

## 🙏 Acknowledgments

- **Flutter Team** for the amazing framework
- **Firebase** for authentication services
- **PostgreSQL** for robust database support
- **Material Design** for beautiful UI components

---

<div align="center">

**Made with ❤️ by the WibeFit Team**

[![GitHub stars](https://img.shields.io/github/stars/your-username/wibefit_app?style=social)](../../stargazers)
[![GitHub forks](https://img.shields.io/github/forks/your-username/wibefit_app?style=social)](../../network/members)

</div>
