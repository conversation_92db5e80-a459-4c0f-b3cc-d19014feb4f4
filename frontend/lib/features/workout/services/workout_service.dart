import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/config/api_config.dart';
import '../models/workout_models.dart';
import '../../exercise/models/exercise_model.dart';

class WorkoutService {
  static String get _baseUrl => ApiConfig.baseUrl;

  // Create a new workout session
  Future<WorkoutSession> createWorkoutSession({
    int? workoutPlanId,
    String? customWorkoutName,
    List<CustomWorkoutExercise>? customExercises,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      final body = {
        'workout_plan_id': workoutPlanId,
        'name': customWorkoutName,
        'started_at': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/api/workouts/sessions'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        return WorkoutSession.fromJson(json.decode(response.body));
      } else {
        // Fallback to local session for offline mode
        return _createLocalWorkoutSession(
          workoutPlanId: workoutPlanId,
          customWorkoutName: customWorkoutName,
          customExercises: customExercises,
        );
      }
    } catch (e) {
      print('Error creating workout session: $e');
      // Fallback to local session
      return _createLocalWorkoutSession(
        workoutPlanId: workoutPlanId,
        customWorkoutName: customWorkoutName,
        customExercises: customExercises,
      );
    }
  }

  // Create local workout session (offline mode)
  WorkoutSession _createLocalWorkoutSession({
    int? workoutPlanId,
    String? customWorkoutName,
    List<CustomWorkoutExercise>? customExercises,
  }) {
    return WorkoutSession(
      id: DateTime.now().millisecondsSinceEpoch, // Use timestamp as ID
      workoutPlanId: workoutPlanId,
      name: customWorkoutName ?? 'Custom Workout',
      startedAt: DateTime.now(),
    );
  }

  // Update workout session (pause, resume, complete)
  Future<WorkoutSession> updateWorkoutSession(
    int sessionId,
    WorkoutSession session,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      final body = {
        'completed_at': session.completedAt?.toIso8601String(),
        'paused_at': session.pausedAt?.toIso8601String(),
        'duration_minutes': session.durationMinutes,
        'calories_burned': session.caloriesBurned,
        'difficulty_rating': session.difficultyRating,
        'satisfaction_rating': session.satisfactionRating,
        'notes': session.notes,
        'is_completed': session.isCompleted,
      };

      final response = await http.put(
        Uri.parse('$_baseUrl/api/workouts/sessions/$sessionId'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 200) {
        return WorkoutSession.fromJson(json.decode(response.body));
      } else {
        // Return the session as-is for offline mode
        return session;
      }
    } catch (e) {
      print('Error updating workout session: $e');
      // Return the session as-is for offline mode
      return session;
    }
  }

  // Log exercise performance
  Future<ExerciseLog> logExercise({
    required int workoutSessionId,
    required int exerciseId,
    required int setNumber,
    int? reps,
    double? weightKg,
    int? durationSeconds,
    double? distanceMeters,
    int? restSeconds,
    String? notes,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      final body = {
        'workout_session_id': workoutSessionId,
        'exercise_id': exerciseId,
        'set_number': setNumber,
        'reps': reps,
        'weight_kg': weightKg,
        'duration_seconds': durationSeconds,
        'distance_meters': distanceMeters,
        'rest_seconds': restSeconds,
        'notes': notes,
        'completed_at': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/api/workouts/exercise-logs'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode(body),
      );

      if (response.statusCode == 201) {
        return ExerciseLog.fromJson(json.decode(response.body));
      } else {
        // Create local log for offline mode
        return _createLocalExerciseLog(
          workoutSessionId: workoutSessionId,
          exerciseId: exerciseId,
          setNumber: setNumber,
          reps: reps,
          weightKg: weightKg,
          durationSeconds: durationSeconds,
          distanceMeters: distanceMeters,
          restSeconds: restSeconds,
          notes: notes,
        );
      }
    } catch (e) {
      print('Error logging exercise: $e');
      // Create local log for offline mode
      return _createLocalExerciseLog(
        workoutSessionId: workoutSessionId,
        exerciseId: exerciseId,
        setNumber: setNumber,
        reps: reps,
        weightKg: weightKg,
        durationSeconds: durationSeconds,
        distanceMeters: distanceMeters,
        restSeconds: restSeconds,
        notes: notes,
      );
    }
  }

  // Create local exercise log
  ExerciseLog _createLocalExerciseLog({
    required int workoutSessionId,
    required int exerciseId,
    required int setNumber,
    int? reps,
    double? weightKg,
    int? durationSeconds,
    double? distanceMeters,
    int? restSeconds,
    String? notes,
  }) {
    return ExerciseLog(
      id: DateTime.now().millisecondsSinceEpoch,
      workoutSessionId: workoutSessionId,
      exerciseId: exerciseId,
      setNumber: setNumber,
      reps: reps,
      weightKg: weightKg,
      durationSeconds: durationSeconds,
      distanceMeters: distanceMeters,
      restSeconds: restSeconds,
      notes: notes,
      completedAt: DateTime.now(),
    );
  }

  // Calculate calories burned
  double calculateCaloriesBurned({
    required Exercise exercise,
    required int durationMinutes,
    double userWeightKg = 70.0, // Default weight
    int? reps,
    int? sets,
  }) {
    // Base metabolic rate per minute (varies by exercise type)
    double baseCaloriesPerMinute = exercise.caloriesPerMinute ?? 5.0;
    
    // Adjust for user weight (heavier people burn more calories)
    double weightMultiplier = userWeightKg / 70.0; // 70kg as baseline
    
    // Adjust for exercise intensity
    double intensityMultiplier = 1.0;
    switch (exercise.difficulty.toLowerCase()) {
      case 'beginner':
        intensityMultiplier = 0.8;
        break;
      case 'intermediate':
        intensityMultiplier = 1.0;
        break;
      case 'advanced':
        intensityMultiplier = 1.3;
        break;
    }

    // Adjust for exercise type
    double typeMultiplier = 1.0;
    switch (exercise.category.toLowerCase()) {
      case 'cardio':
        typeMultiplier = 1.4;
        break;
      case 'upper_body':
      case 'lower_body':
        typeMultiplier = 1.1;
        break;
      case 'core':
        typeMultiplier = 0.9;
        break;
      case 'full_body':
        typeMultiplier = 1.2;
        break;
    }

    // Calculate total calories
    double totalCalories = baseCaloriesPerMinute * 
                          durationMinutes * 
                          weightMultiplier * 
                          intensityMultiplier * 
                          typeMultiplier;

    // Add bonus for high-rep exercises
    if (reps != null && sets != null) {
      double repBonus = (reps * sets) / 100.0; // Small bonus for volume
      totalCalories += repBonus;
    }

    return totalCalories.roundToDouble();
  }

  // Get workout statistics
  Future<WorkoutStats> getWorkoutStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      final response = await http.get(
        Uri.parse('$_baseUrl/api/workouts/stats'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        return WorkoutStats.fromJson(json.decode(response.body));
      } else {
        // Return mock stats for offline mode
        return _getMockWorkoutStats();
      }
    } catch (e) {
      print('Error fetching workout stats: $e');
      // Return mock stats for offline mode
      return _getMockWorkoutStats();
    }
  }

  // Get mock workout stats
  WorkoutStats _getMockWorkoutStats() {
    return WorkoutStats(
      totalWorkouts: 12,
      totalSessions: 15,
      totalTimeMinutes: 450.0,
      totalCaloriesBurned: 2800.0,
      averageSessionDuration: 30.0,
      favoriteWorkoutCategory: 'strength',
      workoutsByDifficulty: {
        'beginner': 5,
        'intermediate': 8,
        'advanced': 2,
      },
      workoutsByCategory: {
        'strength': 8,
        'cardio': 4,
        'flexibility': 3,
      },
      recentSessions: [],
    );
  }

  // Save workout locally (for offline mode)
  Future<void> saveWorkoutLocally(WorkoutSession session) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final workoutsJson = prefs.getStringList('local_workouts') ?? [];
      
      workoutsJson.add(json.encode(session.toJson()));
      await prefs.setStringList('local_workouts', workoutsJson);
    } catch (e) {
      print('Error saving workout locally: $e');
    }
  }

  // Get local workouts
  Future<List<WorkoutSession>> getLocalWorkouts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final workoutsJson = prefs.getStringList('local_workouts') ?? [];
      
      return workoutsJson
          .map((json) => WorkoutSession.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('Error loading local workouts: $e');
      return [];
    }
  }
}
