import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';
import '../../../core/theme/app_theme.dart';

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({super.key});

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  final ApiService _apiService = ApiService();
  bool _isLoading = false;
  String _status = 'Not connected';
  List<dynamic> _exercises = [];
  Map<String, dynamic>? _userToken;
  String? _error;

  @override
  void initState() {
    super.initState();
    _testDatabaseConnection();
  }

  Future<void> _testDatabaseConnection() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Test 1: Health check
      try {
        await _apiService.healthCheck();
      } catch (e) {
        throw Exception('Backend server is not running');
      }

      setState(() {
        _status = 'Backend server is running ✅';
      });

      // Test 2: Try to register a test user
      try {
        await _apiService.register(
          email: '<EMAIL>',
          fullName: 'Test User',
          password: 'testpassword123',
          username: 'testuser',
        );
      } catch (e) {
        // User might already exist, that's okay
        print('Registration note: $e');
      }

      // Test 3: Login with test user
      try {
        final loginResponse = await _apiService.login(
          email: '<EMAIL>',
          password: 'testpassword123',
        );

        setState(() {
          _userToken = loginResponse;
          _status = 'Authenticated successfully ✅';
        });

        // Test 4: Get exercises from database
        final exercisesResponse = await _apiService.getExercises();

        setState(() {
          _exercises = exercisesResponse['exercises'] ?? exercisesResponse;
          _status =
              'Database connected! Found ${_exercises.length} exercises ✅';
        });
      } catch (e) {
        throw Exception('Authentication failed: $e');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _status = 'Connection failed ❌';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Database Connection Test'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              color: AppTheme.cardColor,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _error != null ? Icons.error : Icons.storage,
                          color: _error != null
                              ? Colors.red
                              : AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'Database Status',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (_isLoading)
                      const Row(
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Testing connection...',
                            style: TextStyle(color: Colors.white70),
                          ),
                        ],
                      )
                    else
                      Text(
                        _status,
                        style: TextStyle(
                          color: _error != null ? Colors.red : Colors.green,
                          fontSize: 16,
                        ),
                      ),
                    if (_error != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: $_error',
                        style: const TextStyle(color: Colors.red, fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Authentication Info
            if (_userToken != null)
              Card(
                color: AppTheme.cardColor,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.verified_user, color: Colors.green),
                          SizedBox(width: 8),
                          Text(
                            'Authentication',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Token Type: ${_userToken!['token_type']}',
                        style: const TextStyle(color: Colors.white70),
                      ),
                      Text(
                        'Token: ${_userToken!['access_token']?.substring(0, 20)}...',
                        style: const TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Exercises from Database
            if (_exercises.isNotEmpty) ...[
              const Text(
                'Exercises from Database:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _exercises.length,
                  itemBuilder: (context, index) {
                    final exercise = _exercises[index];
                    return Card(
                      color: AppTheme.cardColor,
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: AppTheme.primaryColor,
                          child: Text(
                            '${index + 1}',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(
                          exercise['name'] ?? 'Unknown Exercise',
                          style: const TextStyle(color: Colors.white),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              exercise['description'] ?? 'No description',
                              style: const TextStyle(color: Colors.white70),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Difficulty: ${exercise['difficulty'] ?? 'Unknown'}',
                              style: TextStyle(color: AppTheme.primaryColor),
                            ),
                          ],
                        ),
                        trailing: Icon(
                          Icons.fitness_center,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],

            // Retry Button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testDatabaseConnection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  _isLoading ? 'Testing...' : 'Test Connection Again',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
