import 'package:flutter/material.dart';
import '../../core/services/mock_api_service.dart';

class TestStorageScreen extends StatefulWidget {
  const TestStorageScreen({super.key});

  @override
  State<TestStorageScreen> createState() => _TestStorageScreenState();
}

class _TestStorageScreenState extends State<TestStorageScreen> {
  String _status = 'Ready to test';
  bool _isLoading = false;

  Future<void> _testSaveProfile() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing profile save...';
    });

    try {
      // Test data
      final testProfile = {
        'user_id': 'test_user_123',
        'email': '<EMAIL>',
        'full_name': 'Test User',
        'age': 25,
        'height': 175.0,
        'current_weight': 70.0,
        'fitness_level': 'beginner',
        'primary_goals': ['weight_loss', 'muscle_gain'],
        'equipment_access': ['dumbbells', 'bodyweight'],
        'preferred_workout_days': ['monday', 'wednesday', 'friday'],
        'gender': 'male',
        'profile_setup_completed': true,
        'profile_setup_date': DateTime.now().toIso8601String(),
      };

      final success = await MockApiService.saveUserProfile(testProfile);
      
      if (success) {
        setState(() {
          _status = '✅ SUCCESS: Profile saved to device storage!';
        });
        
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Test profile saved successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else {
        setState(() {
          _status = '❌ FAILED: Could not save profile';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ ERROR: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testLoadProfile() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing profile load...';
    });

    try {
      final profile = await MockApiService.getUserProfile();
      
      if (profile != null) {
        setState(() {
          _status = '✅ SUCCESS: Profile loaded from device storage!\n'
                   'Data: ${profile.toString()}';
        });
      } else {
        setState(() {
          _status = '⚠️ No profile found in storage';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ ERROR loading profile: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testSaveAuth() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing auth save...';
    });

    try {
      final testAuth = {
        'id': 'test_user_123',
        'email': '<EMAIL>',
        'full_name': 'Test User',
        'role': 'member',
        'is_active': true,
        'is_verified': true,
        'created_at': DateTime.now().toIso8601String(),
        'registration_method': 'test',
      };

      final success = await MockApiService.saveUserAuth(testAuth);
      
      if (success) {
        setState(() {
          _status = '✅ SUCCESS: Auth data saved to device storage!';
        });
      } else {
        setState(() {
          _status = '❌ FAILED: Could not save auth data';
        });
      }
    } catch (e) {
      setState(() {
        _status = '❌ ERROR: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearAllData() async {
    setState(() {
      _isLoading = true;
      _status = 'Clearing all data...';
    });

    try {
      await MockApiService.clearProfile();
      setState(() {
        _status = '🗑️ All data cleared from device storage';
      });
    } catch (e) {
      setState(() {
        _status = '❌ ERROR clearing data: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Storage System'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Status:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _status,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Test Buttons
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              ElevatedButton.icon(
                onPressed: _testSaveProfile,
                icon: const Icon(Icons.save),
                label: const Text('Test Save Profile'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
              
              const SizedBox(height: 12),
              
              ElevatedButton.icon(
                onPressed: _testLoadProfile,
                icon: const Icon(Icons.download),
                label: const Text('Test Load Profile'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
              
              const SizedBox(height: 12),
              
              ElevatedButton.icon(
                onPressed: _testSaveAuth,
                icon: const Icon(Icons.person),
                label: const Text('Test Save Auth'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
              
              const SizedBox(height: 12),
              
              ElevatedButton.icon(
                onPressed: _clearAllData,
                icon: const Icon(Icons.delete_forever),
                label: const Text('Clear All Data'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How to Test:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. Tap "Test Save Profile" - should show SUCCESS\n'
                    '2. Tap "Test Load Profile" - should show the saved data\n'
                    '3. Tap "Test Save Auth" - should show SUCCESS\n'
                    '4. If any test fails, the storage system needs fixing\n'
                    '5. Use "Clear All Data" to reset for testing',
                    style: TextStyle(height: 1.5),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
