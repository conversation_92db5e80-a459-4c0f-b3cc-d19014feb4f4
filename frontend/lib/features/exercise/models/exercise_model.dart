class Exercise {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final List<String>? instructions;
  final List<String>? tips;
  final List<String> muscleGroups;
  final List<String>? equipment;
  final String difficulty;
  final String exerciseType;
  final String category;
  final int? durationMinutes;
  final double? caloriesPerMinute;
  final String? videoUrl;
  final String? imageUrl;
  final bool isActive;
  final int? createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isFavorite;

  const Exercise({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.instructions,
    this.tips,
    required this.muscleGroups,
    this.equipment,
    required this.difficulty,
    required this.exerciseType,
    required this.category,
    this.durationMinutes,
    this.caloriesPerMinute,
    this.videoUrl,
    this.imageUrl,
    required this.isActive,
    this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.isFavorite = false,
  });

  factory Exercise.fromJson(Map<String, dynamic> json) {
    return Exercise(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      instructions: json['instructions'] != null
          ? List<String>.from(json['instructions'])
          : null,
      tips: json['tips'] != null ? List<String>.from(json['tips']) : null,
      muscleGroups: List<String>.from(json['muscle_groups']),
      equipment: json['equipment'] != null
          ? List<String>.from(json['equipment'])
          : null,
      difficulty: json['difficulty'],
      exerciseType: json['exercise_type'],
      category: json['category'],
      durationMinutes: json['duration_minutes'],
      caloriesPerMinute: json['calories_per_minute']?.toDouble(),
      videoUrl: json['video_url'],
      imageUrl: json['image_url'],
      isActive: json['is_active'] ?? true,
      createdBy: json['created_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      isFavorite: json['is_favorite'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'instructions': instructions,
      'tips': tips,
      'muscle_groups': muscleGroups,
      'equipment': equipment,
      'difficulty': difficulty,
      'exercise_type': exerciseType,
      'category': category,
      'duration_minutes': durationMinutes,
      'calories_per_minute': caloriesPerMinute,
      'video_url': videoUrl,
      'image_url': imageUrl,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_favorite': isFavorite,
    };
  }

  // Convenience getters
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasVideo => videoUrl != null && videoUrl!.isNotEmpty;

  String get difficultyFormatted =>
      difficulty[0].toUpperCase() + difficulty.substring(1);
  String get categoryFormatted => category
      .replaceAll('_', ' ')
      .split(' ')
      .map((word) => word[0].toUpperCase() + word.substring(1))
      .join(' ');
  String get exerciseTypeFormatted =>
      exerciseType[0].toUpperCase() + exerciseType.substring(1);

  // Helper getters
  String get primaryMuscleGroup =>
      muscleGroups.isNotEmpty ? muscleGroups.first : 'Unknown';
  String get primaryEquipment =>
      equipment?.isNotEmpty == true ? equipment!.first : 'Bodyweight';
}

class MuscleGroup {
  final int id;
  final String name;
  final String category;
  final String? description;
  final DateTime createdAt;

  const MuscleGroup({
    required this.id,
    required this.name,
    required this.category,
    this.description,
    required this.createdAt,
  });

  factory MuscleGroup.fromJson(Map<String, dynamic> json) {
    return MuscleGroup(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get nameCapitalized => name[0].toUpperCase() + name.substring(1);
}

class Equipment {
  final int id;
  final String name;
  final String category;
  final String? description;
  final bool isCommon;
  final String? imageUrl;
  final DateTime createdAt;

  const Equipment({
    required this.id,
    required this.name,
    required this.category,
    this.description,
    required this.isCommon,
    this.imageUrl,
    required this.createdAt,
  });

  factory Equipment.fromJson(Map<String, dynamic> json) {
    return Equipment(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      description: json['description'],
      isCommon: json['is_common'] ?? false,
      imageUrl: json['image_url'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'is_common': isCommon,
      'image_url': imageUrl,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get nameFormatted => name
      .replaceAll('_', ' ')
      .split(' ')
      .map((word) => word[0].toUpperCase() + word.substring(1))
      .join(' ');
}

class ExerciseStats {
  final int totalExercises;
  final Map<String, int> exercisesByCategory;
  final Map<String, int> exercisesByDifficulty;
  final Map<String, int> exercisesByEquipment;
  final List<PopularExercise> mostPopularExercises;
  final int userFavoritesCount;

  const ExerciseStats({
    required this.totalExercises,
    required this.exercisesByCategory,
    required this.exercisesByDifficulty,
    required this.exercisesByEquipment,
    required this.mostPopularExercises,
    required this.userFavoritesCount,
  });

  factory ExerciseStats.fromJson(Map<String, dynamic> json) {
    return ExerciseStats(
      totalExercises: json['total_exercises'],
      exercisesByCategory: Map<String, int>.from(json['exercises_by_category']),
      exercisesByDifficulty: Map<String, int>.from(
        json['exercises_by_difficulty'],
      ),
      exercisesByEquipment: Map<String, int>.from(
        json['exercises_by_equipment'],
      ),
      mostPopularExercises: (json['most_popular_exercises'] as List)
          .map((e) => PopularExercise.fromJson(e))
          .toList(),
      userFavoritesCount: json['user_favorites_count'],
    );
  }
}

class PopularExercise {
  final String name;
  final int completions;

  const PopularExercise({required this.name, required this.completions});

  factory PopularExercise.fromJson(Map<String, dynamic> json) {
    return PopularExercise(
      name: json['name'],
      completions: json['completions'],
    );
  }
}
