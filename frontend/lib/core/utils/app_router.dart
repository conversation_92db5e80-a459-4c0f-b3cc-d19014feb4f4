import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/onboarding_screen.dart';
import '../../features/auth/screens/profile_setup_screen.dart';
import '../../features/workout/screens/workout_home_screen.dart';
import '../../features/workout/screens/exercise_list_screen.dart';
import '../../features/workout/screens/exercise_guide_screen.dart';
import '../../features/exercise/models/exercise_model.dart';
import '../../features/workout/screens/workout_builder_screen.dart';
import '../../features/workout/screens/plan_settings_screen.dart';
import '../../features/workout/screens/change_plan_screen.dart';
import '../../features/workout/screens/edit_custom_plan_screen.dart';
import '../../features/debug/debug_screen.dart';
import '../../features/debug/test_storage_screen.dart';
import '../../features/analysis/screens/analysis_screen.dart' as v1_analysis;
import '../../features/workout/screens/workout_timer_screen.dart';
import '../../features/workout/screens/ai_plan_screen.dart';
import '../../features/workout/screens/workout_execution_screen.dart';
import '../../features/workout/screens/workout_preview_screen.dart';
import '../../features/workout/screens/test_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/community/screens/community_screen.dart';
import '../../features/database/screens/database_test_screen.dart';
import '../../shared/widgets/main_navigation.dart';
import '../services/auth_service.dart';

class AppRouter {
  static GoRouter get router => _router;

  static final GoRouter _router = GoRouter(
    initialLocation: '/login',
    redirect: (context, state) {
      // Always allow navigation to login screen during startup
      final currentPath = state.uri.path;
      final isAuthRoute =
          currentPath == '/login' ||
          currentPath == '/register' ||
          currentPath == '/onboarding' ||
          currentPath == '/profile-setup';

      // During startup, always allow auth routes
      if (isAuthRoute) {
        return null;
      }

      // For non-auth routes, check authentication safely
      try {
        final authService = AuthService();
        final isAuthenticated = authService.isAuthenticated;

        // If not authenticated and not on auth routes, redirect to login
        if (!isAuthenticated) {
          return '/login';
        }

        return null; // No redirect needed
      } catch (e) {
        // If any error occurs, redirect to login
        return '/login';
      }
    },
    routes: [
      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: '/profile-setup',
        name: 'profile-setup',
        builder: (context, state) => const ProfileSetupScreen(),
      ),

      // Test Route
      GoRoute(
        path: '/test',
        name: 'test',
        builder: (context, state) => const TestScreen(),
      ),

      // Debug Route
      GoRoute(
        path: '/debug',
        name: 'debug',
        builder: (context, state) => const DebugScreen(),
      ),

      // Test Storage Route
      GoRoute(
        path: '/test-storage',
        name: 'test-storage',
        builder: (context, state) => const TestStorageScreen(),
      ),

      // Workout Preview Route (outside shell)
      GoRoute(
        path: '/workout-preview',
        name: 'workout-preview',
        builder: (context, state) => const WorkoutPreviewScreen(),
      ),

      // Exercise Guide Route (outside shell for full-screen video experience)
      GoRoute(
        path: '/exercise-guide',
        name: 'exercise-guide',
        builder: (context, state) {
          final exercise = state.extra as Exercise?;
          if (exercise == null) {
            return const Scaffold(
              body: Center(child: Text('Exercise not found')),
            );
          }
          return ExerciseGuideScreen(exercise: exercise);
        },
      ),

      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainNavigation(child: child),
        routes: [
          // Workout Routes
          GoRoute(
            path: '/workout',
            name: 'workout',
            builder: (context, state) => const WorkoutHomeScreen(),
            routes: [
              GoRoute(
                path: 'exercises',
                name: 'exercises',
                builder: (context, state) => const ExerciseListScreen(),
              ),
              GoRoute(
                path: 'builder',
                name: 'workout-builder',
                builder: (context, state) => const WorkoutBuilderScreen(),
              ),
              GoRoute(
                path: 'settings',
                name: 'plan-settings',
                builder: (context, state) => const PlanSettingsScreen(),
              ),
              GoRoute(
                path: 'change-plan',
                name: 'change-plan',
                builder: (context, state) => const ChangePlanScreen(),
              ),
              GoRoute(
                path: 'edit-custom-plan',
                name: 'edit-custom-plan',
                builder: (context, state) {
                  final planId = state.uri.queryParameters['planId'];
                  return EditCustomPlanScreen(planId: planId);
                },
              ),
              GoRoute(
                path: 'ai-plan',
                name: 'ai-plan',
                builder: (context, state) => const AIPlanScreen(),
              ),

              GoRoute(
                path: 'execute/:workoutId',
                name: 'workout-execution',
                builder: (context, state) {
                  final workoutId = state.pathParameters['workoutId']!;
                  // For now, pass empty exercises list - this will be enhanced later
                  return const WorkoutExecutionScreen(
                    exercises: [],
                    workoutName: 'Custom Workout',
                  );
                },
              ),
            ],
          ),

          // Analysis Routes
          GoRoute(
            path: '/analysis',
            name: 'analysis',
            builder: (context, state) => const v1_analysis.AnalysisScreen(),
          ),

          // Workout Timer Route
          GoRoute(
            path: '/workout-timer/:workoutName/:planId',
            name: 'workout-timer',
            builder: (context, state) {
              final workoutName = state.pathParameters['workoutName']!;
              final planId = state.pathParameters['planId']!;
              return WorkoutTimerScreen(
                workoutName: workoutName,
                workoutPlanId: planId,
              );
            },
          ),

          // Community Routes
          GoRoute(
            path: '/community',
            name: 'community',
            builder: (context, state) => const CommunityScreen(),
          ),

          // Profile Routes
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),

          // Database Test Route
          GoRoute(
            path: '/database-test',
            name: 'database-test',
            builder: (context, state) => const DatabaseTestScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/workout'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}

// Route Names for easy navigation
class Routes {
  static const String login = '/login';
  static const String register = '/register';
  static const String onboarding = '/onboarding';
  static const String profileSetup = '/profile-setup';
  static const String workout = '/workout';
  static const String exercises = '/workout/exercises';
  static const String exerciseGuide = '/exercise-guide';
  static const String workoutBuilder = '/workout/builder';
  static const String planSettings = '/workout/settings';
  static const String changePlan = '/workout/change-plan';
  static const String editCustomPlan = '/workout/edit-custom-plan';
  static const String aiPlan = '/workout/ai-plan';
  static const String analysis = '/analysis';
  static const String community = '/community';
  static const String profile = '/profile';

  static String workoutExecution(String workoutId) =>
      '/workout/execute/$workoutId';

  static String editCustomPlanWithId(String planId) =>
      '/workout/edit-custom-plan?planId=$planId';
}

// Extension for easy navigation
extension GoRouterExtension on BuildContext {
  void pushNamed(
    String name, {
    Map<String, String>? pathParameters,
    Map<String, dynamic>? queryParameters,
  }) {
    GoRouter.of(this).pushNamed(
      name,
      pathParameters: pathParameters ?? {},
      queryParameters: queryParameters ?? {},
    );
  }

  void goNamed(
    String name, {
    Map<String, String>? pathParameters,
    Map<String, dynamic>? queryParameters,
  }) {
    GoRouter.of(this).goNamed(
      name,
      pathParameters: pathParameters ?? {},
      queryParameters: queryParameters ?? {},
    );
  }

  void pop() {
    GoRouter.of(this).pop();
  }
}
