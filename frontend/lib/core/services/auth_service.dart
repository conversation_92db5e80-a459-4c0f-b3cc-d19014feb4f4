import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../../shared/models/user_model.dart';
import '../constants/app_constants.dart';
import '../config/api_config.dart';
import 'firebase_auth_service.dart';
import 'mock_api_service.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _refreshTokenKey = 'refresh_token';

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  UserModel? _currentUser;
  String? _accessToken;
  String? _refreshToken;

  // Firebase Auth Service
  final FirebaseAuthService _firebaseAuth = FirebaseAuthService();

  UserModel? get currentUser => _currentUser;
  String? get accessToken => _accessToken;
  bool get isAuthenticated => _currentUser != null && _accessToken != null;

  // Fast initialization - only load stored credentials (non-blocking)
  void initializeAsync() {
    // Run initialization in background without blocking app startup
    _initializeInBackground();
  }

  // Background initialization method
  Future<void> _initializeInBackground() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final tokenData = prefs.getString(_tokenKey);
      final userData = prefs.getString(_userKey);
      final refreshToken = prefs.getString(_refreshTokenKey);

      if (tokenData != null && userData != null) {
        try {
          _accessToken = tokenData;
          _refreshToken = refreshToken;
          _currentUser = UserModel.fromJson(jsonDecode(userData));

          // Load user profile data from backend (in background)
          _loadUserProfileFromDatabase().catchError((e) {
            print('Background profile load failed: $e');
            // Don't block app if profile loading fails
          });
        } catch (e) {
          // Clear invalid stored data
          await clearStoredAuth();
        }
      }
    } catch (e) {
      print('Background auth initialization failed: $e');
      // Don't block app if initialization fails
    }
  }

  // Initialize auth service - check for stored credentials (legacy method)
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();

    final tokenData = prefs.getString(_tokenKey);
    final userData = prefs.getString(_userKey);
    final refreshToken = prefs.getString(_refreshTokenKey);

    if (tokenData != null && userData != null) {
      try {
        _accessToken = tokenData;
        _refreshToken = refreshToken;
        _currentUser = UserModel.fromJson(jsonDecode(userData));

        // Load user profile data from backend
        await _loadUserProfileFromDatabase();
      } catch (e) {
        // Clear invalid stored data
        await clearStoredAuth();
      }
    }
  }

  // Load user profile data from database
  Future<void> _loadUserProfileFromDatabase() async {
    if (_currentUser == null) return;

    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/auth/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_accessToken',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['profile'] != null) {
          final profileData = data['profile'];

          // Merge profile data into user metadata
          final updatedMetadata = Map<String, dynamic>.from(
            _currentUser!.metadata ?? {},
          );
          updatedMetadata.addAll({
            'age': profileData['age'],
            'height': profileData['height'],
            'weight': profileData['current_weight'],
            'fitnessLevel': profileData['fitness_level'],
            'fitnessGoals': profileData['primary_goals'],
            'availableEquipment': profileData['equipment_access'],
            'workoutFrequency': profileData['preferred_workout_days'],
            'gender': profileData['gender'],
            'profileSetupCompleted': true,
          });

          _currentUser = _currentUser!.copyWith(metadata: updatedMetadata);

          // Update local storage
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(_userKey, jsonEncode(_currentUser!.toJson()));

          print('✅ Profile data loaded from database successfully');
        }
      }
    } catch (e) {
      print('❌ Error loading profile from database: $e');
      // Continue with local data
    }
  }

  // Login with email and password (hybrid Firebase + demo accounts)
  Future<AuthResponse> login(String email, String password) async {
    try {
      // Check if this is a demo account
      if (_isDemoAccount(email, password)) {
        return await _loginWithDemoAccount(email, password);
      }

      // Try Firebase authentication for regular users
      try {
        final user = await _firebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Get Firebase ID token for backend verification
        final String? idToken = await _firebaseAuth.getIdToken();
        if (idToken == null) {
          throw Exception('Failed to get Firebase ID token');
        }

        // Send Firebase ID token to backend for verification
        final response = await http.post(
          Uri.parse('${ApiConfig.baseUrl}/api/auth/firebase'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({
            'id_token': idToken,
            'uid': user.id,
            'email': user.email,
            'name': user.name,
          }),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);

          // Extract backend token
          _accessToken = data['access_token'];
          _refreshToken = data['refresh_token'];
          _currentUser = user;

          final authResponse = AuthResponse(
            user: user,
            accessToken: _accessToken!,
            refreshToken: _refreshToken ?? '',
            expiresAt: DateTime.now().add(const Duration(hours: 24)),
          );

          // Store authentication data
          await _storeAuthData(authResponse);

          // Load user profile data
          await _loadUserProfileFromDatabase();

          return authResponse;
        } else {
          final errorData = json.decode(response.body);
          throw Exception(
            errorData['detail'] ?? 'Backend authentication failed',
          );
        }
      } catch (firebaseError) {
        // If Firebase fails, try backend authentication as fallback
        print('Firebase auth failed, trying backend: $firebaseError');
        return await _loginWithBackend(email, password);
      }
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  // Check if credentials match demo accounts
  bool _isDemoAccount(String email, String password) {
    return (email == AppConstants.defaultEmail &&
            password == AppConstants.defaultPassword) ||
        (email == AppConstants.trainerEmail &&
            password == AppConstants.trainerPassword) ||
        (email == AppConstants.adminEmail &&
            password == AppConstants.adminPassword);
  }

  // Login with demo account using backend
  Future<AuthResponse> _loginWithDemoAccount(
    String email,
    String password,
  ) async {
    return await _loginWithBackend(email, password);
  }

  // Backend authentication (for demo accounts and fallback)
  Future<AuthResponse> _loginWithBackend(String email, String password) async {
    // Always use offline mode for instant login and better performance
    print('🚀 Using offline mode for instant login');
    return _createOfflineDemoUser(email);
  }

  // Create offline demo user for testing when backend is unavailable
  Future<AuthResponse> _createOfflineDemoUser(String email) async {
    final demoUser = UserModel(
      id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      name: email == AppConstants.defaultEmail ? 'Demo User' : 'Test User',
      role: AppConstants.roleMember,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      metadata: {
        'age': '25',
        'gender': 'Male',
        'height': '175',
        'weight': '70',
        'fitnessLevel': 'intermediate',
        'fitnessGoals': ['Weight Loss', 'Muscle Gain'],
        'offline_mode': 'true',
      },
    );

    _currentUser = demoUser;
    _accessToken = 'demo_token_${DateTime.now().millisecondsSinceEpoch}';
    _refreshToken = 'demo_refresh_${DateTime.now().millisecondsSinceEpoch}';

    final authResponse = AuthResponse(
      user: demoUser,
      accessToken: _accessToken!,
      refreshToken: _refreshToken!,
      expiresAt: DateTime.now().add(const Duration(hours: 24)),
    );

    // Store demo data locally
    await _storeAuthData(authResponse);

    print('✅ Offline demo mode activated for: ${demoUser.email}');
    return authResponse;
  }

  // Register new user (backend-first with Firebase fallback)
  Future<AuthResponse> register({
    required String email,
    required String password,
    required String name,
    String role = AppConstants.roleMember,
    String? gymId,
  }) async {
    try {
      // Try backend registration first (faster and more reliable)
      return await _registerWithBackend(email, password, name, role);
    } catch (e) {
      // If backend fails, try Firebase registration as fallback
      if (e.toString().contains('Connection refused') ||
          e.toString().contains('SocketException') ||
          e.toString().contains('timeout')) {
        // Create offline demo user for registration
        return await _createOfflineRegisteredUser(email, password, name, role);
      }

      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  // Backend registration (primary method)
  Future<AuthResponse> _registerWithBackend(
    String email,
    String password,
    String name,
    String role,
  ) async {
    // Always use offline mode for instant registration
    print('🚀 Using offline mode for instant registration');
    return _createOfflineRegisteredUser(email, password, name, role);
  }

  // Create offline registered user for testing when backend unavailable
  Future<AuthResponse> _createOfflineRegisteredUser(
    String email,
    String password,
    String name,
    String role,
  ) async {
    print('🔄 Creating offline registered user...');
    print('📧 Email: $email');
    print('👤 Name: $name');

    final registeredUser = UserModel(
      id: 'registered_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      name: name,
      role: role,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      metadata: {
        'offline_mode': 'true',
        'registration_date': DateTime.now().toIso8601String(),
      },
    );

    _currentUser = registeredUser;
    _accessToken = 'registered_token_${DateTime.now().millisecondsSinceEpoch}';
    _refreshToken =
        'registered_refresh_${DateTime.now().millisecondsSinceEpoch}';

    final authResponse = AuthResponse(
      user: registeredUser,
      accessToken: _accessToken!,
      refreshToken: _refreshToken!,
      expiresAt: DateTime.now().add(const Duration(hours: 24)),
    );

    // Store registration data locally
    await _storeAuthData(authResponse);

    // Also save to mock API for persistence
    await MockApiService.saveUserAuth({
      'id': registeredUser.id,
      'email': registeredUser.email,
      'full_name': registeredUser.name,
      'role': registeredUser.role,
      'is_active': true,
      'is_verified': true,
      'created_at': registeredUser.createdAt.toIso8601String(),
      'registration_method': 'offline',
      'registration_date': DateTime.now().toIso8601String(),
    });

    print('✅ Offline registration mode activated for: ${registeredUser.email}');
    print('💾 User data saved to mock API for persistence');
    return authResponse;
  }

  // Firebase Google Sign-In
  Future<AuthResponse> signInWithGoogle() async {
    try {
      // Sign in with Firebase Google Auth
      final user = await _firebaseAuth.signInWithGoogle();

      // Get Firebase ID token for backend verification
      final String? idToken = await _firebaseAuth.getIdToken();
      if (idToken == null) {
        throw Exception('Failed to get Firebase ID token');
      }

      // Send Firebase ID token to backend for verification
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/auth/firebase'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'id_token': idToken,
          'uid': user.id,
          'email': user.email,
          'name': user.name,
          'photo_url': user.profileImageUrl,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Extract backend token
        _accessToken = data['access_token'];
        _refreshToken = data['refresh_token'];
        _currentUser = user;

        final authResponse = AuthResponse(
          user: user,
          accessToken: _accessToken!,
          refreshToken: _refreshToken ?? '',
          expiresAt: DateTime.now().add(const Duration(hours: 24)),
        );

        // Store authentication data
        await _storeAuthData(authResponse);

        // Load user profile data
        await _loadUserProfileFromDatabase();

        return authResponse;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['detail'] ?? 'Firebase authentication failed',
        );
      }
    } catch (e) {
      // Clean up Firebase auth state on error
      await _firebaseAuth.signOut();
      throw Exception('Google Sign-In error: ${e.toString()}');
    }
  }

  // Logout
  Future<void> logout() async {
    await clearStoredAuth();
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      // Firebase sign out failed, but continue with local logout
      print('Firebase sign out error: $e');
    }
    _currentUser = null;
    _accessToken = null;
    _refreshToken = null;
  }

  // Refresh token (mock implementation)
  Future<String> refreshAccessToken() async {
    if (_refreshToken == null) {
      throw Exception('No refresh token available');
    }

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    final newAccessToken =
        'refreshed_token_${DateTime.now().millisecondsSinceEpoch}';
    _accessToken = newAccessToken;

    // Update stored token
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, newAccessToken);

    return newAccessToken;
  }

  // Store authentication data locally
  Future<void> _storeAuthData(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(_tokenKey, authResponse.accessToken);
    await prefs.setString(_userKey, jsonEncode(authResponse.user.toJson()));
    await prefs.setString(_refreshTokenKey, authResponse.refreshToken);
  }

  // Clear stored authentication data
  Future<void> clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
    await prefs.remove(_refreshTokenKey);
  }

  // Update user profile
  Future<UserModel> updateProfile({
    String? name,
    String? profileImageUrl,
    Map<String, dynamic>? metadata,
  }) async {
    if (_currentUser == null) {
      throw Exception('No authenticated user');
    }

    try {
      // Save to backend database first
      await _saveUserProfileToDatabase(metadata);

      // Update local user data
      final updatedUser = _currentUser!.copyWith(
        name: name,
        profileImageUrl: profileImageUrl,
        metadata: metadata,
      );

      _currentUser = updatedUser;

      // Update stored user data locally
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, jsonEncode(updatedUser.toJson()));

      return updatedUser;
    } catch (e) {
      print('Error updating profile: $e');
      // Still update locally as fallback
      final updatedUser = _currentUser!.copyWith(
        name: name,
        profileImageUrl: profileImageUrl,
        metadata: metadata,
      );

      _currentUser = updatedUser;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, jsonEncode(updatedUser.toJson()));
      return updatedUser;
    }
  }

  // Save user profile data to database
  Future<void> _saveUserProfileToDatabase(
    Map<String, dynamic>? metadata,
  ) async {
    if (_currentUser == null || metadata == null) {
      print('❌ Cannot save profile: user or metadata is null');
      return;
    }

    print('🔄 Attempting to save profile data...');
    print('📝 Profile metadata: $metadata');
    print('👤 Current user: ${_currentUser!.email}');

    // Always try mock API first for guaranteed storage
    print('💾 Saving to mock API (guaranteed storage)...');
    final mockSuccess = await MockApiService.saveUserProfile({
      'user_id': _currentUser!.id,
      'email': _currentUser!.email,
      'full_name': _currentUser!.name,
      'age': metadata['age'],
      'height': metadata['height'],
      'current_weight': metadata['weight'],
      'fitness_level': metadata['fitnessLevel'],
      'primary_goals': metadata['fitnessGoals'],
      'equipment_access': metadata['availableEquipment'],
      'preferred_workout_days': metadata['workoutFrequency'],
      'gender': metadata['gender'],
      'profile_setup_completed': true,
      'profile_setup_date': DateTime.now().toIso8601String(),
    });

    if (mockSuccess) {
      print('✅ Profile saved to mock API successfully (guaranteed storage)');
    } else {
      print('❌ Failed to save to mock API - this should never happen');
    }

    // Skip backend save for instant performance - data is already saved locally
    print(
      '⚡ Skipping backend save for instant performance (data saved locally)',
    );

    // Always succeed if mock API worked
    if (!mockSuccess) {
      throw Exception('Failed to save profile data');
    }
  }

  // Check if user has specific role
  bool hasRole(String role) {
    return _currentUser?.role == role;
  }

  // Update current user from mock API data
  Future<void> updateCurrentUserFromMockAPI(UserModel updatedUser) async {
    _currentUser = updatedUser;

    // Also save to local storage
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(updatedUser.toJson()));

    print('✅ Current user updated with mock API data');
  }

  // Check if user belongs to specific gym
  bool belongsToGym(String gymId) {
    return _currentUser?.gymId == gymId;
  }

  // Get current user (for compatibility with auth provider)
  Future<UserModel?> getCurrentUser() async {
    return _currentUser;
  }
}
