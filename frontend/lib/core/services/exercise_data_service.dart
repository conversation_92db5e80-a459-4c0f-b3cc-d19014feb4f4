import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/exercise_model.dart';

/// Service for managing exercise data from Google Drive
class ExerciseDataService {
  static const String _exercisesCacheKey = 'cached_exercises';
  static const String _lastUpdateKey = 'exercises_last_update';
  
  // Google Drive configuration
  static const String driveBaseUrl = 'https://drive.google.com/uc?export=download&id=';
  
  /// Load exercises from Google Drive or cache
  Future<List<ExerciseModel>> loadExercises({
    String? driveVideoFolderId,
    String? drivePdfFileId,
    bool forceRefresh = false,
  }) async {
    print('🏋️ ExerciseDataService: Loading exercises...');
    
    // Check cache first unless force refresh
    if (!forceRefresh) {
      final cachedExercises = await _loadFromCache();
      if (cachedExercises.isNotEmpty) {
        print('✅ Loaded ${cachedExercises.length} exercises from cache');
        return cachedExercises;
      }
    }
    
    // Load from Google Drive if IDs provided
    if (driveVideoFolderId != null && drivePdfFileId != null) {
      try {
        print('🌐 Loading exercises from Google Drive...');
        final exercises = await _loadFromGoogleDrive(
          driveVideoFolderId,
          drivePdfFileId,
        );
        
        if (exercises.isNotEmpty) {
          await _saveToCache(exercises);
          print('✅ Loaded ${exercises.length} exercises from Google Drive');
          return exercises;
        }
      } catch (e) {
        print('❌ Error loading from Google Drive: $e');
      }
    }
    
    // Fallback to demo exercises
    print('🔄 Using demo exercises as fallback');
    return _getDemoExercises();
  }
  
  /// Load exercises from Google Drive
  Future<List<ExerciseModel>> _loadFromGoogleDrive(
    String videoFolderId,
    String pdfFileId,
  ) async {
    final exercises = <ExerciseModel>[];
    
    try {
      // Step 1: Get video list from Google Drive folder
      print('📹 Fetching video list from Google Drive folder...');
      final videoList = await _getVideoListFromDrive(videoFolderId);
      
      // Step 2: Get exercise descriptions from PDF
      print('📄 Fetching exercise descriptions from PDF...');
      final descriptions = await _getDescriptionsFromPdf(pdfFileId);
      
      // Step 3: Match videos with descriptions and create exercises
      print('🔗 Matching videos with descriptions...');
      for (final video in videoList) {
        final exercise = _createExerciseFromVideo(video, descriptions);
        if (exercise != null) {
          exercises.add(exercise);
        }
      }
      
      print('✅ Created ${exercises.length} exercises from Google Drive data');
      return exercises;
      
    } catch (e) {
      print('❌ Error loading from Google Drive: $e');
      rethrow;
    }
  }
  
  /// Get video list from Google Drive folder
  Future<List<Map<String, dynamic>>> _getVideoListFromDrive(
    String folderId,
  ) async {
    // Note: This requires Google Drive API setup
    // For now, return demo structure - you'll need to implement actual API calls
    
    print('📹 Simulating Google Drive video fetch...');
    
    // Demo video structure - replace with actual Google Drive API calls
    return [
      {
        'id': 'video1_id',
        'name': 'Push_Up_Beginner_Chest_Exercise.mp4',
        'url': 'https://drive.google.com/file/d/video1_id/view',
        'downloadUrl': '${driveBaseUrl}video1_id',
      },
      {
        'id': 'video2_id', 
        'name': 'Squat_Intermediate_Legs_Exercise.mp4',
        'url': 'https://drive.google.com/file/d/video2_id/view',
        'downloadUrl': '${driveBaseUrl}video2_id',
      },
      {
        'id': 'video3_id',
        'name': 'Deadlift_Advanced_Back_Exercise.mp4', 
        'url': 'https://drive.google.com/file/d/video3_id/view',
        'downloadUrl': '${driveBaseUrl}video3_id',
      },
      // Add more demo videos...
    ];
  }
  
  /// Get exercise descriptions from PDF file
  Future<Map<String, String>> _getDescriptionsFromPdf(String pdfFileId) async {
    print('📄 Simulating PDF description fetch...');
    
    // Demo descriptions - replace with actual PDF parsing
    return {
      'push_up': 'A fundamental upper body exercise that targets the chest, shoulders, and triceps. Start in a plank position with hands shoulder-width apart.',
      'squat': 'A compound lower body exercise that targets the quadriceps, hamstrings, and glutes. Stand with feet shoulder-width apart and lower your body.',
      'deadlift': 'A compound exercise that targets the posterior chain including hamstrings, glutes, and back muscles. Lift the barbell from the ground to hip level.',
      // Add more descriptions...
    };
  }
  
  /// Create exercise model from video data and descriptions
  ExerciseModel? _createExerciseFromVideo(
    Map<String, dynamic> video,
    Map<String, String> descriptions,
  ) {
    try {
      final filename = video['name'] as String;
      
      // Parse filename: "Exercise_Name_Level_BodyPart_Exercise.mp4"
      final parts = filename.replaceAll('.mp4', '').split('_');
      if (parts.length < 3) return null;
      
      final exerciseName = parts[0].replaceAll('_', ' ');
      final level = parts.length > 1 ? parts[1] : 'Beginner';
      final bodyPart = parts.length > 2 ? parts[2] : 'Full Body';
      
      // Find matching description
      final descriptionKey = exerciseName.toLowerCase().replaceAll(' ', '_');
      final description = descriptions[descriptionKey] ?? 
          'A ${level.toLowerCase()} level exercise targeting $bodyPart muscles.';
      
      return ExerciseModel(
        id: video['id'],
        name: exerciseName,
        description: description,
        category: bodyPart,
        difficulty: level,
        duration: _estimateDuration(level),
        caloriesPerMinute: _estimateCalories(level, bodyPart),
        instructions: _generateInstructions(exerciseName, description),
        videoUrl: video['downloadUrl'],
        thumbnailUrl: _generateThumbnailUrl(video['id']),
        equipment: _determineEquipment(exerciseName),
        muscleGroups: _determineMuscleGroups(bodyPart),
        tags: [level, bodyPart, 'Video'],
      );
      
    } catch (e) {
      print('❌ Error creating exercise from video: $e');
      return null;
    }
  }
  
  /// Estimate exercise duration based on difficulty
  int _estimateDuration(String level) {
    switch (level.toLowerCase()) {
      case 'beginner':
        return 30; // 30 seconds
      case 'intermediate':
        return 45; // 45 seconds
      case 'advanced':
        return 60; // 60 seconds
      default:
        return 30;
    }
  }
  
  /// Estimate calories burned per minute
  double _estimateCalories(String level, String bodyPart) {
    double baseCalories = 5.0;
    
    // Adjust for difficulty
    switch (level.toLowerCase()) {
      case 'beginner':
        baseCalories *= 0.8;
        break;
      case 'intermediate':
        baseCalories *= 1.0;
        break;
      case 'advanced':
        baseCalories *= 1.3;
        break;
    }
    
    // Adjust for body part
    switch (bodyPart.toLowerCase()) {
      case 'legs':
      case 'back':
        baseCalories *= 1.2; // Larger muscle groups burn more
        break;
      case 'chest':
      case 'shoulders':
        baseCalories *= 1.0;
        break;
      case 'arms':
        baseCalories *= 0.8;
        break;
    }
    
    return baseCalories;
  }
  
  /// Generate exercise instructions
  List<String> _generateInstructions(String name, String description) {
    // Basic instructions - can be enhanced with AI or manual data
    return [
      'Warm up properly before starting',
      'Maintain proper form throughout the exercise',
      'Control the movement - don\'t rush',
      'Breathe steadily during the exercise',
      'Stop if you feel any pain',
    ];
  }
  
  /// Generate thumbnail URL for video
  String _generateThumbnailUrl(String videoId) {
    // You can use Google Drive thumbnail API or generate custom thumbnails
    return 'https://drive.google.com/thumbnail?id=$videoId&sz=w400-h300';
  }
  
  /// Determine required equipment from exercise name
  List<String> _determineEquipment(String exerciseName) {
    final name = exerciseName.toLowerCase();
    
    if (name.contains('dumbbell') || name.contains('weight')) {
      return ['Dumbbells'];
    } else if (name.contains('barbell') || name.contains('deadlift')) {
      return ['Barbell', 'Weight Plates'];
    } else if (name.contains('pull') && name.contains('up')) {
      return ['Pull-up Bar'];
    } else if (name.contains('bench')) {
      return ['Bench'];
    } else {
      return ['None']; // Bodyweight exercise
    }
  }
  
  /// Determine target muscle groups
  List<String> _determineMuscleGroups(String bodyPart) {
    switch (bodyPart.toLowerCase()) {
      case 'chest':
        return ['Chest', 'Triceps', 'Shoulders'];
      case 'back':
        return ['Back', 'Biceps', 'Rear Delts'];
      case 'legs':
        return ['Quadriceps', 'Hamstrings', 'Glutes', 'Calves'];
      case 'shoulders':
        return ['Shoulders', 'Triceps'];
      case 'arms':
        return ['Biceps', 'Triceps', 'Forearms'];
      default:
        return ['Full Body'];
    }
  }
  
  /// Load exercises from cache
  Future<List<ExerciseModel>> _loadFromCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_exercisesCacheKey);
      
      if (cachedData != null) {
        final List<dynamic> jsonList = json.decode(cachedData);
        return jsonList.map((json) => ExerciseModel.fromJson(json)).toList();
      }
    } catch (e) {
      print('❌ Error loading from cache: $e');
    }
    
    return [];
  }
  
  /// Save exercises to cache
  Future<void> _saveToCache(List<ExerciseModel> exercises) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = exercises.map((e) => e.toJson()).toList();
      await prefs.setString(_exercisesCacheKey, json.encode(jsonList));
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
      
      print('💾 Saved ${exercises.length} exercises to cache');
    } catch (e) {
      print('❌ Error saving to cache: $e');
    }
  }
  
  /// Get demo exercises as fallback
  List<ExerciseModel> _getDemoExercises() {
    return [
      ExerciseModel(
        id: 'demo_1',
        name: 'Push Up',
        description: 'Classic upper body exercise targeting chest, shoulders, and triceps',
        category: 'Chest',
        difficulty: 'Beginner',
        duration: 30,
        caloriesPerMinute: 6.0,
        instructions: [
          'Start in plank position',
          'Lower body to ground',
          'Push back up',
          'Repeat'
        ],
        videoUrl: 'https://example.com/pushup.mp4',
        thumbnailUrl: 'https://example.com/pushup_thumb.jpg',
        equipment: ['None'],
        muscleGroups: ['Chest', 'Triceps', 'Shoulders'],
        tags: ['Beginner', 'Chest', 'Bodyweight'],
      ),
      // Add more demo exercises...
    ];
  }
  
  /// Clear exercise cache
  Future<void> clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_exercisesCacheKey);
    await prefs.remove(_lastUpdateKey);
    print('🗑️ Exercise cache cleared');
  }
  
  /// Get last update time
  Future<DateTime?> getLastUpdateTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timeString = prefs.getString(_lastUpdateKey);
    return timeString != null ? DateTime.parse(timeString) : null;
  }
}
