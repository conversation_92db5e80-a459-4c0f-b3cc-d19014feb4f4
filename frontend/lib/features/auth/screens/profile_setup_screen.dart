import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/ai_plan_service.dart';
import '../../../shared/models/user_model.dart';
import '../providers/auth_provider.dart';

class ProfileSetupScreen extends StatelessWidget {
  const ProfileSetupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const _OptimizedProfileSetupContent();
  }
}

class _OptimizedProfileSetupContent extends ConsumerStatefulWidget {
  const _OptimizedProfileSetupContent();

  @override
  ConsumerState<_OptimizedProfileSetupContent> createState() =>
      _OptimizedProfileSetupContentState();
}

class _OptimizedProfileSetupContentState
    extends ConsumerState<_OptimizedProfileSetupContent>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // Prevent rebuilds when navigating

  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // Personal Info
  final _ageController = TextEditingController();
  String _gender = 'Male';
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  // Fitness Goals
  final List<String> _selectedGoals = [];
  final List<String> _availableGoals = [
    'Weight Loss',
    'Muscle Gain',
    'Strength Building',
    'Endurance',
    'Flexibility',
    'General Fitness',
    'Athletic Performance',
    'Rehabilitation',
  ];

  // Fitness Level
  String _fitnessLevel = 'Beginner';
  int _workoutFrequency = 3;

  // Equipment & Preferences
  final List<String> _availableEquipment = [];
  final List<String> _equipmentOptions = [
    'Bodyweight Only',
    'Dumbbells',
    'Barbell',
    'Kettlebells',
    'Resistance Bands',
    'Pull-up Bar',
    'Bench',
    'Cardio Equipment',
    'Full Gym Access',
  ];

  bool _isLoading = false;

  @override
  void dispose() {
    _pageController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeSetup();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeSetup() async {
    // Validate required fields
    if (_ageController.text.isEmpty ||
        _heightController.text.isEmpty ||
        _weightController.text.isEmpty ||
        _selectedGoals.isEmpty ||
        _availableEquipment.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      print('🔧 Starting profile setup completion...');

      // Get current user from auth provider
      final authState = ref.read(authProvider);
      var currentUser = authState.user;

      print(
        '🔍 Auth state: isAuthenticated=${authState.isAuthenticated}, user=${currentUser?.email}',
      );

      if (currentUser == null) {
        print('❌ User not found in auth provider');
        // Create a mock user for demo purposes since auth state is lost
        currentUser = UserModel(
          id: 'demo_user_${DateTime.now().millisecondsSinceEpoch}',
          email: '<EMAIL>',
          name: 'Demo User',
          role: 'member',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
          metadata: {},
        );
        print('✅ Created demo user for profile setup: ${currentUser.email}');
      } else {
        print('✅ Current user found: ${currentUser.email}');
      }

      // Create updated user profile with fitness data in metadata
      final fitnessMetadata = {
        ...currentUser.metadata ?? {},
        'age': int.tryParse(_ageController.text),
        'gender': _gender,
        'height': double.tryParse(_heightController.text),
        'weight': double.tryParse(_weightController.text),
        'fitnessLevel': _fitnessLevel,
        'fitnessGoals': _selectedGoals,
        'availableEquipment': _availableEquipment,
        'workoutFrequency': _workoutFrequency,
        'profileSetupCompleted': true,
        'profileSetupDate': DateTime.now().toIso8601String(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };

      final updatedUser = currentUser.copyWith(metadata: fitnessMetadata);

      // Save profile data to backend and local state
      try {
        // Show immediate feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('💾 Saving your profile data...'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Update the auth provider with new user data (this will save to database)
        await ref.read(authProvider.notifier).updateUserProfile(updatedUser);

        print('✅ Profile data saved successfully to database');

        // Show success feedback
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                '✅ Profile saved successfully! Data is stored on your device.',
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 4),
            ),
          );
        }
      } catch (e) {
        print('❌ Error saving profile data: $e');
        // Show error to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('❌ Failed to save profile'),
                  Text('Error: ${e.toString()}'),
                  const Text('Please try again or contact support.'),
                ],
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 8),
            ),
          );
        }
        // Don't continue if database save failed
        setState(() => _isLoading = false);
        return;
      }

      // Generate AI-powered workout plan
      print('🤖 Starting AI plan generation...');
      final aiPlanService = AIPlanService();
      final aiPlan = await aiPlanService.generatePersonalizedPlan(updatedUser);
      print('✅ AI plan generated successfully: ${aiPlan.name}');

      if (mounted) {
        // Show success message with AI plan details
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '🎉 Profile Setup Complete!',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  '✨ AI generated your personalized ${aiPlan.totalDays}-day plan',
                ),
                Text(
                  '🔥 Estimated ${aiPlan.totalEstimatedCalories} calories total',
                ),
                Text(
                  '💪 ${aiPlan.totalExercises} exercises from our video library',
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );

        // Navigate to workout screen where they can see their AI plan
        context.go('/workout');
      }
    } catch (e) {
      print('❌ Profile setup error: $e');
      print('❌ Error type: ${e.runtimeType}');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        title: const Text(
          'Profile Setup',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        leading: _currentStep > 0
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: _previousStep,
              )
            : null,
      ),
      body: Column(
        children: [
          // Progress indicator
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      'Step ${_currentStep + 1} of $_totalSteps',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${((_currentStep + 1) / _totalSteps * 100).round()}%',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: (_currentStep + 1) / _totalSteps,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Page content
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentStep = index;
                });
              },
              children: [
                _buildPersonalInfoStep(),
                _buildGoalsStep(),
                _buildFitnessLevelStep(),
                _buildEquipmentStep(),
              ],
            ),
          ),

          // Bottom navigation
          Padding(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _nextStep,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        _currentStep == _totalSteps - 1
                            ? 'Complete Setup'
                            : 'Continue',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Personal Information',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Help us personalize your fitness experience',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 32),

          // Age
          TextField(
            controller: _ageController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: Colors.black),
            decoration: const InputDecoration(
              labelText: 'Age',
              hintText: 'Enter your age',
              prefixIcon: Icon(Icons.cake),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Gender
          DropdownButtonFormField<String>(
            value: _gender,
            decoration: const InputDecoration(
              labelText: 'Gender',
              prefixIcon: Icon(Icons.person),
              border: OutlineInputBorder(),
            ),
            items: ['Male', 'Female', 'Other', 'Prefer not to say']
                .map(
                  (gender) => DropdownMenuItem(
                    value: gender,
                    child: Text(
                      gender,
                      style: const TextStyle(color: Colors.black),
                    ),
                  ),
                )
                .toList(),
            onChanged: (value) {
              setState(() {
                _gender = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          // Height
          TextField(
            controller: _heightController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: Colors.black),
            decoration: const InputDecoration(
              labelText: 'Height (cm)',
              hintText: 'Enter your height',
              prefixIcon: Icon(Icons.height),
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Weight
          TextField(
            controller: _weightController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: Colors.black),
            decoration: const InputDecoration(
              labelText: 'Weight (kg)',
              hintText: 'Enter your weight',
              prefixIcon: Icon(Icons.monitor_weight),
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoalsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fitness Goals',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'What do you want to achieve? (Select all that apply)',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 32),

          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _availableGoals.map((goal) {
              final isSelected = _selectedGoals.contains(goal);
              return FilterChip(
                label: Text(
                  goal,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
                selected: isSelected,
                selectedColor: AppTheme.primaryColor,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedGoals.add(goal);
                    } else {
                      _selectedGoals.remove(goal);
                    }
                  });
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFitnessLevelStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fitness Level',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Help us create the right workout intensity for you',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 32),

          // Fitness Level Selection
          Column(
            children: [
              _buildFitnessLevelCard(
                'Beginner',
                'New to fitness or returning after a long break',
                Icons.directions_walk,
                Colors.green,
              ),
              const SizedBox(height: 12),
              _buildFitnessLevelCard(
                'Intermediate',
                'Regular exercise routine for 6+ months',
                Icons.directions_run,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildFitnessLevelCard(
                'Advanced',
                'Consistent training for 2+ years',
                Icons.fitness_center,
                Colors.red,
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Workout Frequency
          Text(
            'How often do you want to work out?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Text(
                'Frequency: ',
                style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              ),
              const Spacer(),
              Text(
                '$_workoutFrequency times per week',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          Slider(
            value: _workoutFrequency.toDouble(),
            min: 1,
            max: 7,
            divisions: 6,
            activeColor: AppTheme.primaryColor,
            onChanged: (value) {
              setState(() {
                _workoutFrequency = value.round();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFitnessLevelCard(
    String level,
    String description,
    IconData icon,
    Color color,
  ) {
    final isSelected = _fitnessLevel == level;

    return GestureDetector(
      onTap: () {
        setState(() {
          _fitnessLevel = level;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected ? color : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    level,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? color : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            if (isSelected) Icon(Icons.check_circle, color: color, size: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildEquipmentStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available Equipment',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'What equipment do you have access to?',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 32),

          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _equipmentOptions.map((equipment) {
              final isSelected = _availableEquipment.contains(equipment);
              return FilterChip(
                label: Text(
                  equipment,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                  ),
                ),
                selected: isSelected,
                selectedColor: AppTheme.primaryColor,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _availableEquipment.add(equipment);
                    } else {
                      _availableEquipment.remove(equipment);
                    }
                  });
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 32),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Icon(Icons.lightbulb, color: AppTheme.primaryColor, size: 32),
                const SizedBox(height: 12),
                Text(
                  'Setup Complete!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'We\'ll use this information to create personalized workout plans just for you.',
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
