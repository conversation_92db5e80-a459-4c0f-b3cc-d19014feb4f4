import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';

class GoogleAuthService {
  static final GoogleAuthService _instance = GoogleAuthService._internal();
  factory GoogleAuthService() => _instance;
  GoogleAuthService._internal();

  late GoogleSignIn _googleSignIn;
  GoogleSignInAccount? _currentUser;

  /// Initialize Google Sign-In
  void initialize({
    required String webClientId,
    List<String> scopes = const ['email', 'profile'],
  }) {
    _googleSignIn = GoogleSignIn(
      clientId: webClientId,
      scopes: scopes,
    );

    // Listen to sign-in state changes
    _googleSignIn.onCurrentUserChanged.listen((GoogleSignInAccount? account) {
      _currentUser = account;
    });
  }

  /// Get current signed-in user
  GoogleSignInAccount? get currentUser => _currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _currentUser != null;

  /// Sign in with Google
  Future<GoogleSignInAccount?> signIn() async {
    try {
      final GoogleSignInAccount? account = await _googleSignIn.signIn();
      if (account != null) {
        _currentUser = account;
        return account;
      }
      return null;
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign-In Error: $error');
      }
      rethrow;
    }
  }

  /// Sign out from Google
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      _currentUser = null;
    } catch (error) {
      if (kDebugMode) {
        print('Google Sign-Out Error: $error');
      }
      rethrow;
    }
  }

  /// Disconnect from Google (revoke access)
  Future<void> disconnect() async {
    try {
      await _googleSignIn.disconnect();
      _currentUser = null;
    } catch (error) {
      if (kDebugMode) {
        print('Google Disconnect Error: $error');
      }
      rethrow;
    }
  }

  /// Get Google ID Token for backend verification
  Future<String?> getIdToken() async {
    try {
      final GoogleSignInAccount? account = _currentUser ?? await _googleSignIn.signInSilently();
      if (account != null) {
        final GoogleSignInAuthentication auth = await account.authentication;
        return auth.idToken;
      }
      return null;
    } catch (error) {
      if (kDebugMode) {
        print('Error getting ID token: $error');
      }
      return null;
    }
  }

  /// Get Google Access Token
  Future<String?> getAccessToken() async {
    try {
      final GoogleSignInAccount? account = _currentUser ?? await _googleSignIn.signInSilently();
      if (account != null) {
        final GoogleSignInAuthentication auth = await account.authentication;
        return auth.accessToken;
      }
      return null;
    } catch (error) {
      if (kDebugMode) {
        print('Error getting access token: $error');
      }
      return null;
    }
  }

  /// Silent sign-in (check if user is already signed in)
  Future<GoogleSignInAccount?> signInSilently() async {
    try {
      final GoogleSignInAccount? account = await _googleSignIn.signInSilently();
      if (account != null) {
        _currentUser = account;
      }
      return account;
    } catch (error) {
      if (kDebugMode) {
        print('Silent sign-in error: $error');
      }
      return null;
    }
  }

  /// Get user profile information
  Map<String, dynamic>? getUserProfile() {
    if (_currentUser == null) return null;

    return {
      'id': _currentUser!.id,
      'email': _currentUser!.email,
      'displayName': _currentUser!.displayName,
      'photoUrl': _currentUser!.photoUrl,
    };
  }
}
