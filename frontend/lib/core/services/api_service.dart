import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import 'mock_data_service.dart';

class ApiService {
  static String get _baseUrl => ApiConfig.baseUrl;

  static const Duration _timeout = Duration(
    seconds: 15,
  ); // Optimized timeout for mobile devices

  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  // HTTP client with timeout
  final http.Client _client = http.Client();

  // Mock data service for fallback
  final MockDataService _mockDataService = MockDataService();

  // Authentication token (will be set after login)
  String? _authToken;

  // Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
  }

  // Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  // Common headers
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  // Generic GET request with fallback
  Future<Map<String, dynamic>> _get(String endpoint) async {
    try {
      final response = await _client
          .get(Uri.parse('$_baseUrl$endpoint'), headers: _headers)
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic POST request
  Future<Map<String, dynamic>> _post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl$endpoint'),
            headers: _headers,
            body: jsonEncode(data),
          )
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic PUT request
  Future<Map<String, dynamic>> _put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _client
          .put(
            Uri.parse('$_baseUrl$endpoint'),
            headers: _headers,
            body: jsonEncode(data),
          )
          .timeout(_timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return jsonDecode(response.body);
    } else {
      throw ApiException(
        'HTTP ${response.statusCode}: ${response.body}',
        statusCode: response.statusCode,
      );
    }
  }

  // ============ EXERCISE API ============

  /// Get exercises with filtering (with fallback to mock data)
  Future<Map<String, dynamic>> getExercises({
    String? search,
    List<String>? muscleGroups,
    List<String>? equipment,
    List<String>? difficulty,
    List<String>? category,
    bool? isFavorite,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['q'] = search;
      }
      if (muscleGroups != null && muscleGroups.isNotEmpty) {
        queryParams['muscle_groups'] = muscleGroups.join(',');
      }
      if (equipment != null && equipment.isNotEmpty) {
        queryParams['equipment'] = equipment.join(',');
      }
      if (difficulty != null && difficulty.isNotEmpty) {
        queryParams['difficulty'] = difficulty.join(',');
      }
      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category.join(',');
      }
      if (isFavorite != null) {
        queryParams['is_favorite'] = isFavorite.toString();
      }

      final uri = Uri.parse(
        '$_baseUrl/api/workouts/exercises',
      ).replace(queryParameters: queryParams);
      final response = await _client
          .get(uri, headers: _headers)
          .timeout(_timeout);
      return _handleResponse(response);
    } catch (e) {
      // Fallback to mock data
      print('API call failed, using mock data: $e');
      debugPrint('API call failed, using mock data: $e');
      return await _mockDataService.getExercises(
        search: search,
        muscleGroups: muscleGroups,
        equipment: equipment,
        difficulty: difficulty,
        category: category,
        isFavorite: isFavorite,
        limit: limit,
        offset: offset,
      );
    }
  }

  /// Get specific exercise by ID (with fallback to mock data)
  Future<Map<String, dynamic>> getExercise(int exerciseId) async {
    try {
      return await _get('/api/workouts/exercises/$exerciseId');
    } catch (e) {
      // Fallback to mock data
      print('API call failed, using mock data: $e');
      final mockExercise = await _mockDataService.getExercise(exerciseId);
      if (mockExercise != null) {
        return mockExercise;
      }
      throw ApiException('Exercise not found: $exerciseId');
    }
  }

  /// Get muscle groups (with fallback to mock data)
  Future<List<dynamic>> getMuscleGroups() async {
    try {
      final response = await _get('/api/exercises/categories/muscle-groups');
      return response as List<dynamic>;
    } catch (e) {
      // Fallback to mock data
      print('API call failed, using mock data: $e');
      return await _mockDataService.getMuscleGroups();
    }
  }

  /// Get equipment types (with fallback to mock data)
  Future<List<dynamic>> getEquipment() async {
    try {
      final response = await _get('/api/exercises/categories/equipment');
      return response as List<dynamic>;
    } catch (e) {
      // Fallback to mock data
      print('API call failed, using mock data: $e');
      return await _mockDataService.getEquipment();
    }
  }

  /// Get exercise statistics (with fallback to mock data)
  Future<Map<String, dynamic>> getExerciseStats() async {
    try {
      return await _get('/api/exercises/stats');
    } catch (e) {
      // Fallback to mock data
      print('API call failed, using mock data: $e');
      return await _mockDataService.getExerciseStats();
    }
  }

  // ============ ANALYTICS API ============

  /// Get analytics overview
  Future<Map<String, dynamic>> getAnalyticsOverview() async {
    return await _get('/api/analytics/overview');
  }

  /// Get workout analytics
  Future<Map<String, dynamic>> getWorkoutAnalytics({
    String period = 'month',
  }) async {
    final queryParams = {'period': period};
    final uri = Uri.parse(
      '$_baseUrl/api/analytics/workouts',
    ).replace(queryParameters: queryParams);
    final response = await _client
        .get(uri, headers: _headers)
        .timeout(_timeout);
    return _handleResponse(response);
  }

  /// Get progress analytics
  Future<Map<String, dynamic>> getProgressAnalytics({
    String metric = 'weight',
    String period = 'month',
  }) async {
    final queryParams = {'metric': metric, 'period': period};
    final uri = Uri.parse(
      '$_baseUrl/api/analytics/progress',
    ).replace(queryParameters: queryParams);
    final response = await _client
        .get(uri, headers: _headers)
        .timeout(_timeout);
    return _handleResponse(response);
  }

  /// Get goal analytics
  Future<Map<String, dynamic>> getGoalAnalytics() async {
    return await _get('/api/analytics/goals');
  }

  /// Get social analytics
  Future<Map<String, dynamic>> getSocialAnalytics() async {
    return await _get('/api/analytics/social');
  }

  // ============ USER API ============

  /// Get current user profile
  Future<Map<String, dynamic>> getCurrentUser() async {
    return await _get('/api/users/me');
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateUser(Map<String, dynamic> userData) async {
    return await _put('/api/users/me', userData);
  }

  /// Get user profile details
  Future<Map<String, dynamic>> getUserProfile() async {
    return await _get('/api/users/me/profile');
  }

  /// Create user profile
  Future<Map<String, dynamic>> createUserProfile(
    Map<String, dynamic> profileData,
  ) async {
    return await _post('/api/users/me/profile', profileData);
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateUserProfile(
    Map<String, dynamic> profileData,
  ) async {
    return await _put('/api/users/me/profile', profileData);
  }

  /// Get user measurements
  Future<List<dynamic>> getUserMeasurements({String? measurementType}) async {
    final queryParams = <String, String>{};
    if (measurementType != null) {
      queryParams['measurement_type'] = measurementType;
    }

    final uri = Uri.parse(
      '$_baseUrl/api/users/me/measurements',
    ).replace(queryParameters: queryParams);
    final response = await _client
        .get(uri, headers: _headers)
        .timeout(_timeout);
    final result = _handleResponse(response);
    return result as List<dynamic>;
  }

  /// Create user measurement
  Future<Map<String, dynamic>> createUserMeasurement(
    Map<String, dynamic> measurementData,
  ) async {
    return await _post('/api/users/me/measurements', measurementData);
  }

  /// Get user goals
  Future<List<dynamic>> getUserGoals({bool? isCompleted}) async {
    final queryParams = <String, String>{};
    if (isCompleted != null) {
      queryParams['is_completed'] = isCompleted.toString();
    }

    final uri = Uri.parse(
      '$_baseUrl/api/users/me/goals',
    ).replace(queryParameters: queryParams);
    final response = await _client
        .get(uri, headers: _headers)
        .timeout(_timeout);
    final result = _handleResponse(response);
    return result as List<dynamic>;
  }

  /// Create user goal
  Future<Map<String, dynamic>> createUserGoal(
    Map<String, dynamic> goalData,
  ) async {
    return await _post('/api/users/me/goals', goalData);
  }

  /// Update user goal
  Future<Map<String, dynamic>> updateUserGoal(
    int goalId,
    Map<String, dynamic> goalData,
  ) async {
    return await _put('/api/users/me/goals/$goalId', goalData);
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStats() async {
    return await _get('/api/users/me/stats');
  }

  /// Get user analytics
  Future<Map<String, dynamic>> getUserAnalytics() async {
    return await _get('/api/users/me/analytics');
  }

  // ============ HEALTH CHECK ============

  /// Check API health
  Future<Map<String, dynamic>> healthCheck() async {
    return await _get('/health');
  }

  /// Test API connection
  Future<bool> testConnection() async {
    try {
      await healthCheck();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Authentication Methods

  /// Register a new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String fullName,
    required String password,
    String? username,
  }) async {
    try {
      final response = await _post('/api/auth/register', {
        'email': email,
        'full_name': fullName,
        'password': password,
        if (username != null) 'username': username,
      });
      return response;
    } catch (e) {
      print('Registration failed: $e');
      debugPrint('Registration failed: $e');
      rethrow;
    }
  }

  /// Login user and get authentication token
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _post('/api/auth/login', {
        'email': email,
        'password': password,
      });

      // Store the token for future requests
      if (response['access_token'] != null) {
        setAuthToken(response['access_token']);
      }

      return response;
    } catch (e) {
      print('Login failed: $e');
      debugPrint('Login failed: $e');
      rethrow;
    }
  }

  /// Get current user profile from API
  Future<Map<String, dynamic>> getCurrentUserProfile() async {
    try {
      return await _get('/api/auth/me');
    } catch (e) {
      print('Get current user failed: $e');
      debugPrint('Get current user failed: $e');
      rethrow;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      await _post('/api/auth/logout', {});
      clearAuthToken();
    } catch (e) {
      print('Logout failed: $e');
      debugPrint('Logout failed: $e');
      // Clear token anyway
      clearAuthToken();
    }
  }

  // Cleanup
  void dispose() {
    _client.close();
  }
}

// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, {this.statusCode});

  @override
  String toString() => 'ApiException: $message';
}
