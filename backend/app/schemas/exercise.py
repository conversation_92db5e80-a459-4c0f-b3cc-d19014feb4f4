"""
Exercise Pydantic schemas for request/response validation
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ExerciseDifficulty(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class ExerciseType(str, Enum):
    STRENGTH = "strength"
    CARDIO = "cardio"
    FLEXIBILITY = "flexibility"
    BALANCE = "balance"
    SPORTS = "sports"


class ExerciseCategory(str, Enum):
    UPPER_BODY = "upper_body"
    LOWER_BODY = "lower_body"
    CORE = "core"
    FULL_BODY = "full_body"
    CARDIO = "cardio"


# Exercise schemas
class ExerciseBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    instructions: Optional[List[str]] = None
    tips: Optional[List[str]] = None
    muscle_groups: List[str] = Field(..., min_items=1)
    equipment: Optional[List[str]] = None
    difficulty: ExerciseDifficulty
    exercise_type: ExerciseType
    category: ExerciseCategory
    duration_minutes: Optional[int] = Field(None, gt=0, le=300)
    calories_per_minute: Optional[float] = Field(None, gt=0)


class ExerciseCreate(ExerciseBase):
    pass


class ExerciseUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    instructions: Optional[List[str]] = None
    tips: Optional[List[str]] = None
    muscle_groups: Optional[List[str]] = Field(None, min_items=1)
    equipment: Optional[List[str]] = None
    difficulty: Optional[ExerciseDifficulty] = None
    exercise_type: Optional[ExerciseType] = None
    category: Optional[ExerciseCategory] = None
    duration_minutes: Optional[int] = Field(None, gt=0, le=300)
    calories_per_minute: Optional[float] = Field(None, gt=0)
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    is_active: Optional[bool] = None


class ExerciseResponse(ExerciseBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    slug: str
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    is_active: bool
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None


# Exercise variation schemas
class ExerciseVariationBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    difficulty_modifier: str = Field(..., pattern="^(easier|harder|same)$")
    instructions: Optional[List[str]] = None


class ExerciseVariationCreate(ExerciseVariationBase):
    exercise_id: int


class ExerciseVariationResponse(ExerciseVariationBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    exercise_id: int
    video_url: Optional[str] = None
    created_at: datetime


# User exercise schemas
class UserExerciseBase(BaseModel):
    is_favorite: bool = False
    personal_best_weight: Optional[float] = Field(None, gt=0)
    personal_best_reps: Optional[int] = Field(None, gt=0)
    personal_best_time: Optional[float] = Field(None, gt=0)  # seconds
    notes: Optional[str] = None


class UserExerciseCreate(UserExerciseBase):
    exercise_id: int


class UserExerciseUpdate(UserExerciseBase):
    pass


class UserExerciseResponse(UserExerciseBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    exercise_id: int
    last_performed: Optional[datetime] = None
    total_sessions: int
    created_at: datetime
    updated_at: Optional[datetime] = None


# Exercise log schemas
class ExerciseLogBase(BaseModel):
    sets_completed: Optional[int] = Field(None, gt=0)
    reps_completed: Optional[List[int]] = None  # Reps per set
    weight_used: Optional[List[float]] = None  # Weight per set
    duration_seconds: Optional[float] = Field(None, gt=0)
    distance_meters: Optional[float] = Field(None, gt=0)
    calories_burned: Optional[float] = Field(None, gt=0)
    difficulty_rating: Optional[int] = Field(None, ge=1, le=10)
    form_rating: Optional[int] = Field(None, ge=1, le=10)
    notes: Optional[str] = None


class ExerciseLogCreate(ExerciseLogBase):
    exercise_id: int
    workout_session_id: Optional[int] = None


class ExerciseLogResponse(ExerciseLogBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    user_id: int
    exercise_id: int
    workout_session_id: Optional[int] = None
    performed_at: datetime


# Muscle group and equipment schemas
class MuscleGroupBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    category: str = Field(..., pattern="^(primary|secondary)$")
    description: Optional[str] = None


class MuscleGroupCreate(MuscleGroupBase):
    pass


class MuscleGroupResponse(MuscleGroupBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    created_at: datetime


class EquipmentBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    category: str = Field(..., pattern="^(free_weights|machines|bodyweight|cardio)$")
    description: Optional[str] = None
    is_common: bool = False


class EquipmentCreate(EquipmentBase):
    pass


class EquipmentResponse(EquipmentBase):
    model_config = ConfigDict(from_attributes=True)
    
    id: int
    image_url: Optional[str] = None
    created_at: datetime


# Exercise search and filter schemas
class ExerciseSearchParams(BaseModel):
    q: Optional[str] = Field(None, max_length=255)  # Search query
    muscle_groups: Optional[List[str]] = None
    equipment: Optional[List[str]] = None
    difficulty: Optional[List[ExerciseDifficulty]] = None
    exercise_type: Optional[List[ExerciseType]] = None
    category: Optional[List[ExerciseCategory]] = None
    duration_min: Optional[int] = Field(None, ge=0)
    duration_max: Optional[int] = Field(None, ge=0)
    is_favorite: Optional[bool] = None
    limit: int = Field(20, ge=1, le=100)
    offset: int = Field(0, ge=0)


class ExerciseListResponse(BaseModel):
    exercises: List[ExerciseResponse]
    total: int
    limit: int
    offset: int
    has_more: bool


# Exercise statistics
class ExerciseStats(BaseModel):
    total_exercises: int = 0
    exercises_by_category: Dict[str, int] = {}
    exercises_by_difficulty: Dict[str, int] = {}
    exercises_by_equipment: Dict[str, int] = {}
    most_popular_exercises: List[Dict[str, Any]] = []
    user_favorites_count: int = 0
