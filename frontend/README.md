# WibeFit Frontend

Flutter mobile application for WibeFit - AI-powered fitness companion.

## Features

- **Cross-Platform**: Runs on iOS and Android
- **Beautiful UI**: Light theme with orange and purple branding
- **Authentication**: Multi-role login (Member, Trainer, Admin)
- **Workout Management**: Exercise library, workout plans, and tracking
- **Profile Management**: User profiles and settings
- **Community Features**: Social interactions and sharing
- **Analytics**: Workout progress and statistics
- **Responsive Design**: Optimized for all screen sizes

## Quick Start

### Prerequisites

- Flutter SDK 3.16.0+
- Dart 3.2.0+
- Android Studio / Xcode
- iOS Simulator / Android Emulator

### Installation

1. **Navigate to frontend**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Run the app**:
   ```bash
   # For Android
   flutter run -d android
   
   # For iOS
   flutter run -d ios
   
   # For specific device
   flutter run -d [device-id]
   ```

### Building

#### Android APK
```bash
flutter build apk --release
```

#### iOS App
```bash
flutter build ios --release
```

## Project Structure

```
frontend/
├── lib/
│   ├── core/                    # Core functionality
│   │   ├── constants/          # App constants
│   │   ├── theme/              # App theme and styling
│   │   └── utils/              # Utility functions
│   ├── features/               # Feature modules
│   │   ├── auth/               # Authentication
│   │   │   ├── screens/        # Login, register screens
│   │   │   ├── services/       # Auth services
│   │   │   └── widgets/        # Auth widgets
│   │   ├── workout/            # Workout features
│   │   │   ├── screens/        # Workout screens
│   │   │   ├── services/       # Workout services
│   │   │   └── widgets/        # Workout widgets
│   │   ├── profile/            # User profile
│   │   ├── analytics/          # Analytics and stats
│   │   └── community/          # Community features
│   ├── shared/                 # Shared components
│   │   ├── widgets/            # Reusable widgets
│   │   └── services/           # Shared services
│   └── main.dart               # App entry point
├── assets/                     # Static assets
│   ├── images/                 # Images
│   ├── logos/                  # App logos
│   ├── videos/                 # Exercise videos
│   └── animations/             # Animations
├── android/                    # Android-specific files
├── ios/                        # iOS-specific files
└── pubspec.yaml               # Dependencies and config
```

## Demo Credentials

Use these accounts for testing:

- **Member**: `<EMAIL>` / `wibefit123`
- **Trainer**: `<EMAIL>` / `trainer123`
- **Admin**: `<EMAIL>` / `admin123`

## Key Features

### Authentication
- Multi-role login system
- JWT token management
- Secure logout functionality

### Workout Management
- Exercise library with filtering
- Custom workout plans
- Workout session tracking
- Progress monitoring

### User Interface
- Light theme with brand colors
- Responsive design
- Smooth animations
- Intuitive navigation

### Navigation
- Bottom navigation bar
- GoRouter for routing
- Deep linking support
- Back button handling

## Development

### Hot Reload
```bash
# In running app, press 'r' for hot reload
# Press 'R' for hot restart
```

### Testing
```bash
flutter test
```

### Code Generation
```bash
flutter packages pub run build_runner build
```

### Debugging
```bash
flutter run --debug
flutter logs
```

## Building for Production

### Android
1. Configure signing in `android/app/build.gradle`
2. Build release APK: `flutter build apk --release`
3. Build App Bundle: `flutter build appbundle --release`

### iOS
1. Configure signing in Xcode
2. Build for iOS: `flutter build ios --release`
3. Archive in Xcode for App Store

## Dependencies

### Core
- `flutter`: SDK
- `go_router`: Navigation
- `provider`: State management

### UI
- `flutter_svg`: SVG support
- `cached_network_image`: Image caching
- `shimmer`: Loading animations

### Networking
- `http`: HTTP requests
- `dio`: Advanced HTTP client

### Storage
- `shared_preferences`: Local storage
- `flutter_secure_storage`: Secure storage

### Utilities
- `intl`: Internationalization
- `path_provider`: File paths
- `permission_handler`: Permissions

## Configuration

### Environment Setup
1. Copy `lib/core/config/env.example.dart` to `env.dart`
2. Configure API endpoints and keys
3. Set up Firebase (if using)
4. Configure analytics

### Theme Customization
- Edit `lib/core/theme/app_theme.dart`
- Modify colors in `lib/core/constants/colors.dart`
- Update fonts in `pubspec.yaml`

## Troubleshooting

### Common Issues

1. **Build Errors**:
   ```bash
   flutter clean
   flutter pub get
   ```

2. **iOS Simulator Issues**:
   ```bash
   xcrun simctl list devices
   flutter emulators --launch apple_ios_simulator
   ```

3. **Android Emulator Issues**:
   ```bash
   flutter emulators
   flutter emulators --launch [emulator-id]
   ```

### Performance
- Use `flutter run --profile` for performance testing
- Analyze with `flutter analyze`
- Profile with DevTools

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow Flutter style guide
4. Add tests for new features
5. Submit a pull request

## Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language](https://dart.dev/)
- [Material Design](https://material.io/)
- [Flutter Packages](https://pub.dev/)
