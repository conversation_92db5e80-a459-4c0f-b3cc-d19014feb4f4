"""
OpenRouter AI service for intelligent fitness recommendations
"""

import httpx
import json
import logging
from typing import Dict, List, Optional, Any
from openai import OpenAI

from .config import settings

logger = logging.getLogger(__name__)


class OpenRouterService:
    """OpenRouter AI service for fitness-related AI features"""
    
    def __init__(self):
        """Initialize OpenRouter client"""
        try:
            if settings.openrouter_api_key:
                self.client = OpenAI(
                    base_url=settings.openrouter_base_url,
                    api_key=settings.openrouter_api_key,
                )
                self.model = settings.openrouter_model
                logger.info("OpenRouter client initialized successfully")
            else:
                logger.warning("OpenRouter API key not provided")
                self.client = None
                
        except Exception as e:
            logger.error(f"Failed to initialize OpenRouter client: {e}")
            self.client = None
    
    async def generate_workout_plan(
        self, 
        user_profile: Dict[str, Any],
        preferences: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Generate a personalized workout plan using AI
        
        Args:
            user_profile: User's fitness profile (age, weight, fitness level, etc.)
            preferences: User's workout preferences (duration, equipment, goals)
            
        Returns:
            Generated workout plan or None if failed
        """
        if not self.client:
            logger.error("OpenRouter client not initialized")
            return None
        
        try:
            prompt = self._create_workout_prompt(user_profile, preferences)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional fitness trainer and nutritionist. Generate detailed, safe, and effective workout plans based on user profiles and preferences. Always prioritize safety and proper form."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            workout_plan = self._parse_workout_response(response.choices[0].message.content)
            logger.info("Workout plan generated successfully")
            return workout_plan
            
        except Exception as e:
            logger.error(f"Failed to generate workout plan: {e}")
            return None
    
    async def analyze_exercise_form(
        self, 
        exercise_name: str,
        user_description: str
    ) -> Optional[Dict[str, Any]]:
        """
        Analyze exercise form and provide feedback
        
        Args:
            exercise_name: Name of the exercise
            user_description: User's description of their form or issues
            
        Returns:
            Form analysis and recommendations
        """
        if not self.client:
            logger.error("OpenRouter client not initialized")
            return None
        
        try:
            prompt = f"""
            Analyze the exercise form for {exercise_name}.
            User description: {user_description}
            
            Please provide:
            1. Form analysis and potential issues
            2. Specific corrections and tips
            3. Safety considerations
            4. Progressive improvements
            
            Format the response as JSON with keys: analysis, corrections, safety, progression
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert fitness trainer specializing in exercise form analysis. Provide detailed, actionable feedback to help users improve their exercise technique safely."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            analysis = self._parse_json_response(response.choices[0].message.content)
            logger.info(f"Exercise form analysis completed for {exercise_name}")
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze exercise form: {e}")
            return None
    
    async def get_nutrition_advice(
        self, 
        user_profile: Dict[str, Any],
        fitness_goals: List[str]
    ) -> Optional[Dict[str, Any]]:
        """
        Get personalized nutrition advice
        
        Args:
            user_profile: User's profile including age, weight, activity level
            fitness_goals: List of fitness goals (weight loss, muscle gain, etc.)
            
        Returns:
            Nutrition recommendations
        """
        if not self.client:
            logger.error("OpenRouter client not initialized")
            return None
        
        try:
            prompt = f"""
            Provide personalized nutrition advice for:
            User Profile: {json.dumps(user_profile)}
            Fitness Goals: {', '.join(fitness_goals)}
            
            Please provide:
            1. Daily calorie recommendations
            2. Macronutrient breakdown
            3. Meal timing suggestions
            4. Specific food recommendations
            5. Hydration guidelines
            
            Format as JSON with keys: calories, macros, timing, foods, hydration
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a certified nutritionist and dietitian. Provide evidence-based, personalized nutrition advice that aligns with fitness goals and promotes overall health."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.5,
                max_tokens=1500
            )
            
            advice = self._parse_json_response(response.choices[0].message.content)
            logger.info("Nutrition advice generated successfully")
            return advice
            
        except Exception as e:
            logger.error(f"Failed to generate nutrition advice: {e}")
            return None
    
    async def analyze_progress(
        self, 
        progress_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Analyze user's fitness progress and provide insights
        
        Args:
            progress_data: Historical fitness data and measurements
            
        Returns:
            Progress analysis and recommendations
        """
        if not self.client:
            logger.error("OpenRouter client not initialized")
            return None
        
        try:
            prompt = f"""
            Analyze the following fitness progress data and provide insights:
            {json.dumps(progress_data)}
            
            Please provide:
            1. Progress summary and trends
            2. Areas of improvement
            3. Potential plateaus or concerns
            4. Recommendations for next steps
            5. Motivation and encouragement
            
            Format as JSON with keys: summary, improvements, concerns, recommendations, motivation
            """
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a fitness coach analyzing progress data. Provide encouraging, data-driven insights that help users understand their progress and stay motivated."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.6,
                max_tokens=1200
            )
            
            analysis = self._parse_json_response(response.choices[0].message.content)
            logger.info("Progress analysis completed successfully")
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze progress: {e}")
            return None
    
    def _create_workout_prompt(self, user_profile: Dict, preferences: Dict) -> str:
        """Create a detailed prompt for workout generation"""
        return f"""
        Create a personalized workout plan for:
        
        User Profile:
        - Age: {user_profile.get('age', 'Not specified')}
        - Fitness Level: {user_profile.get('fitness_level', 'Beginner')}
        - Weight: {user_profile.get('weight', 'Not specified')}
        - Height: {user_profile.get('height', 'Not specified')}
        - Health Conditions: {user_profile.get('health_conditions', 'None')}
        
        Preferences:
        - Duration: {preferences.get('duration', '30 minutes')}
        - Equipment: {preferences.get('equipment', 'Bodyweight')}
        - Goals: {preferences.get('goals', 'General fitness')}
        - Workout Type: {preferences.get('type', 'Mixed')}
        
        Please provide a detailed workout plan with:
        1. Warm-up exercises (5-10 minutes)
        2. Main workout with specific exercises, sets, reps, and rest periods
        3. Cool-down and stretching (5-10 minutes)
        4. Safety tips and modifications
        5. Expected benefits and progression
        
        Format as JSON with keys: warmup, main_workout, cooldown, safety_tips, benefits
        """
    
    def _parse_workout_response(self, response: str) -> Dict[str, Any]:
        """Parse AI response into structured workout data"""
        try:
            # Try to extract JSON from the response
            return self._parse_json_response(response)
        except:
            # Fallback to structured text parsing
            return {
                "raw_response": response,
                "parsed": False,
                "message": "Workout plan generated but requires manual parsing"
            }
    
    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response from AI"""
        try:
            # Try to find JSON in the response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Return structured response if no JSON found
                return {
                    "content": response,
                    "parsed": False
                }
                
        except json.JSONDecodeError:
            return {
                "content": response,
                "parsed": False,
                "error": "Failed to parse JSON response"
            }


# Global OpenRouter service instance
openrouter_service = OpenRouterService()
