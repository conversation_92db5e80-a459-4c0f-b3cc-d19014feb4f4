import 'package:json_annotation/json_annotation.dart';
import '../../exercise/models/exercise_model.dart';

part 'workout_models.g.dart';

// Workout Plan Model
@JsonSerializable()
class WorkoutPlan {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String difficulty; // beginner, intermediate, advanced
  final int durationMinutes;
  final String category; // strength, cardio, flexibility, mixed
  final List<String>? targetMuscleGroups;
  final List<String>? equipmentNeeded;
  final double? caloriesEstimate;
  final bool isPublic;
  final bool isFeatured;
  final double rating;
  final int totalRatings;
  final int totalCompletions;
  final List<String>? tags;
  final String? thumbnailUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<WorkoutExercise>? exercises;

  WorkoutPlan({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    required this.difficulty,
    required this.durationMinutes,
    required this.category,
    this.targetMuscleGroups,
    this.equipmentNeeded,
    this.caloriesEstimate,
    this.isPublic = true,
    this.isFeatured = false,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.totalCompletions = 0,
    this.tags,
    this.thumbnailUrl,
    required this.createdAt,
    this.updatedAt,
    this.exercises,
  });

  factory WorkoutPlan.fromJson(Map<String, dynamic> json) =>
      _$WorkoutPlanFromJson(json);
  Map<String, dynamic> toJson() => _$WorkoutPlanToJson(this);
}

// Workout Exercise Model (Exercise within a workout plan)
@JsonSerializable()
class WorkoutExercise {
  final int id;
  final int workoutPlanId;
  final int exerciseId;
  final int orderIndex;
  final int? sets;
  final dynamic reps; // Can be int or List<int> for ranges
  final double? weightKg;
  final int? durationSeconds;
  final int? restSeconds;
  final double? distanceMeters;
  final String? notes;
  final bool isWarmup;
  final bool isCooldown;
  final Exercise? exercise; // Populated exercise details

  WorkoutExercise({
    required this.id,
    required this.workoutPlanId,
    required this.exerciseId,
    required this.orderIndex,
    this.sets,
    this.reps,
    this.weightKg,
    this.durationSeconds,
    this.restSeconds,
    this.distanceMeters,
    this.notes,
    this.isWarmup = false,
    this.isCooldown = false,
    this.exercise,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) =>
      _$WorkoutExerciseFromJson(json);
  Map<String, dynamic> toJson() => _$WorkoutExerciseToJson(this);
}

// Workout Session Model (Active/Completed workout instance)
@JsonSerializable()
class WorkoutSession {
  final int? id;
  final int? userId;
  final int? workoutPlanId;
  final String? name; // For custom workouts
  final DateTime startedAt;
  final DateTime? completedAt;
  final DateTime? pausedAt;
  final double? durationMinutes;
  final double? caloriesBurned;
  final int? averageHeartRate;
  final int? maxHeartRate;
  final int? difficultyRating; // 1-10 scale
  final int? satisfactionRating; // 1-10 scale
  final String? notes;
  final String? weather;
  final String? location;
  final bool isCompleted;
  final WorkoutPlan? workoutPlan;
  final List<ExerciseLog>? exerciseLogs;

  WorkoutSession({
    this.id,
    this.userId,
    this.workoutPlanId,
    this.name,
    required this.startedAt,
    this.completedAt,
    this.pausedAt,
    this.durationMinutes,
    this.caloriesBurned,
    this.averageHeartRate,
    this.maxHeartRate,
    this.difficultyRating,
    this.satisfactionRating,
    this.notes,
    this.weather,
    this.location,
    this.isCompleted = false,
    this.workoutPlan,
    this.exerciseLogs,
  });

  factory WorkoutSession.fromJson(Map<String, dynamic> json) =>
      _$WorkoutSessionFromJson(json);
  Map<String, dynamic> toJson() => _$WorkoutSessionToJson(this);

  // Helper methods
  bool get isActive => !isCompleted && completedAt == null;
  bool get isPaused => pausedAt != null && !isCompleted;

  Duration get elapsedTime {
    final endTime = completedAt ?? pausedAt ?? DateTime.now();
    return endTime.difference(startedAt);
  }

  WorkoutSession copyWith({
    int? id,
    int? userId,
    int? workoutPlanId,
    String? name,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? pausedAt,
    double? durationMinutes,
    double? caloriesBurned,
    int? averageHeartRate,
    int? maxHeartRate,
    int? difficultyRating,
    int? satisfactionRating,
    String? notes,
    String? weather,
    String? location,
    bool? isCompleted,
    WorkoutPlan? workoutPlan,
    List<ExerciseLog>? exerciseLogs,
  }) {
    return WorkoutSession(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      workoutPlanId: workoutPlanId ?? this.workoutPlanId,
      name: name ?? this.name,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      pausedAt: pausedAt ?? this.pausedAt,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      caloriesBurned: caloriesBurned ?? this.caloriesBurned,
      averageHeartRate: averageHeartRate ?? this.averageHeartRate,
      maxHeartRate: maxHeartRate ?? this.maxHeartRate,
      difficultyRating: difficultyRating ?? this.difficultyRating,
      satisfactionRating: satisfactionRating ?? this.satisfactionRating,
      notes: notes ?? this.notes,
      weather: weather ?? this.weather,
      location: location ?? this.location,
      isCompleted: isCompleted ?? this.isCompleted,
      workoutPlan: workoutPlan ?? this.workoutPlan,
      exerciseLogs: exerciseLogs ?? this.exerciseLogs,
    );
  }
}

// Exercise Log Model (Individual exercise performance within a session)
@JsonSerializable()
class ExerciseLog {
  final int? id;
  final int? workoutSessionId;
  final int exerciseId;
  final int setNumber;
  final int? reps;
  final double? weightKg;
  final int? durationSeconds;
  final double? distanceMeters;
  final int? restSeconds;
  final String? notes;
  final DateTime? completedAt;
  final Exercise? exercise;

  ExerciseLog({
    this.id,
    this.workoutSessionId,
    required this.exerciseId,
    required this.setNumber,
    this.reps,
    this.weightKg,
    this.durationSeconds,
    this.distanceMeters,
    this.restSeconds,
    this.notes,
    this.completedAt,
    this.exercise,
  });

  factory ExerciseLog.fromJson(Map<String, dynamic> json) =>
      _$ExerciseLogFromJson(json);
  Map<String, dynamic> toJson() => _$ExerciseLogToJson(this);
}

// Custom Workout Model (For workout builder)
@JsonSerializable()
class CustomWorkout {
  final String name;
  final String difficulty;
  final String duration;
  final String target;
  final List<CustomWorkoutExercise> exercises;
  final DateTime createdAt;

  CustomWorkout({
    required this.name,
    required this.difficulty,
    required this.duration,
    required this.target,
    required this.exercises,
    required this.createdAt,
  });

  factory CustomWorkout.fromJson(Map<String, dynamic> json) =>
      _$CustomWorkoutFromJson(json);
  Map<String, dynamic> toJson() => _$CustomWorkoutToJson(this);

  // Convert to WorkoutSession for execution
  WorkoutSession toWorkoutSession() {
    return WorkoutSession(name: name, startedAt: DateTime.now());
  }
}

// Custom Workout Exercise Model
@JsonSerializable()
class CustomWorkoutExercise {
  final Exercise exercise;
  final int sets;
  final int reps;
  final int restSeconds;
  final double? weightKg;
  final int? durationSeconds;

  CustomWorkoutExercise({
    required this.exercise,
    required this.sets,
    required this.reps,
    required this.restSeconds,
    this.weightKg,
    this.durationSeconds,
  });

  factory CustomWorkoutExercise.fromJson(Map<String, dynamic> json) =>
      _$CustomWorkoutExerciseFromJson(json);
  Map<String, dynamic> toJson() => _$CustomWorkoutExerciseToJson(this);
}

// Workout Statistics Model
@JsonSerializable()
class WorkoutStats {
  final int totalWorkouts;
  final int totalSessions;
  final double totalTimeMinutes;
  final double totalCaloriesBurned;
  final double? averageSessionDuration;
  final String? favoriteWorkoutCategory;
  final Map<String, int> workoutsByDifficulty;
  final Map<String, int> workoutsByCategory;
  final List<WorkoutSession> recentSessions;

  WorkoutStats({
    this.totalWorkouts = 0,
    this.totalSessions = 0,
    this.totalTimeMinutes = 0,
    this.totalCaloriesBurned = 0,
    this.averageSessionDuration,
    this.favoriteWorkoutCategory,
    this.workoutsByDifficulty = const {},
    this.workoutsByCategory = const {},
    this.recentSessions = const [],
  });

  factory WorkoutStats.fromJson(Map<String, dynamic> json) =>
      _$WorkoutStatsFromJson(json);
  Map<String, dynamic> toJson() => _$WorkoutStatsToJson(this);
}
